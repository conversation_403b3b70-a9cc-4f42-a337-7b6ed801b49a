DATABASE SCHEMA EXPORT
Generated on: 2025-08-04 16:08:33
Database: trogon_skillage
================================================================================

TABLE: accounts
----------------------------------------
CREATE TABLE `accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) DEFAULT NULL,
  `balance` double DEFAULT 0,
  `description` varchar(400) DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         50                    NULL           
  balance              double          N/A                   NULL           
  description          varchar         400                   NULL           
  is_primary           tinyint         1                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: app_version
----------------------------------------
CREATE TABLE `app_version` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `app_version` varchar(10) NOT NULL,
  `app_version_ios` varchar(10) NOT NULL,
  `date_modified` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  app_version          varchar         10                    NULL           
  app_version_ios      varchar         10                    NULL           
  date_modified        datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: assignment
----------------------------------------
CREATE TABLE `assignment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(1000) NOT NULL,
  `description` varchar(1000) NOT NULL,
  `added_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `due_date` date NOT NULL,
  `file` varchar(1000) NOT NULL,
  `category_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         1000                  NULL           
  description          varchar         1000                  NULL           
  added_date           timestamp       N/A                   current_timestamp()
  due_date             date            N/A                   NULL           
  file                 varchar         1000                  NULL           
  category_id          int             11                    NULL           
  course_id            int             11                    NULL           
  section_id           int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: auto_notification
----------------------------------------
CREATE TABLE `auto_notification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` text NOT NULL,
  `description` text NOT NULL,
  `course_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `send_type` int(11) DEFAULT NULL COMMENT '1= from purchase, 2= from specific date',
  `send_from_date` date DEFAULT NULL,
  `day` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `image` varchar(155) DEFAULT NULL,
  `external_link` varchar(155) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                text            N/A                   NULL           
  description          text            N/A                   NULL           
  course_id            int             11                    NULL           
  category_id          int             11                    NULL           
  send_type            int             11                    NULL           
  send_from_date       date            N/A                   NULL           
  day                  int             11                    NULL           
  timestamp            timestamp       N/A                   current_timestamp()
  image                varchar         155                   NULL           
  external_link        varchar         155                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: banner_vimeo_videolinks
----------------------------------------
CREATE TABLE `banner_vimeo_videolinks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `quality` text DEFAULT NULL,
  `rendition` text DEFAULT NULL,
  `height` bigint(20) DEFAULT NULL,
  `width` text DEFAULT NULL,
  `type` text DEFAULT NULL,
  `link` text DEFAULT NULL,
  `fps` text DEFAULT NULL,
  `size` text DEFAULT NULL,
  `public_name` text DEFAULT NULL,
  `size_short` text DEFAULT NULL,
  `download_link` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=195 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  course_id            int             11                    NULL           
  quality              text            N/A                   NULL           
  rendition            text            N/A                   NULL           
  height               bigint          20                    NULL           
  width                text            N/A                   NULL           
  type                 text            N/A                   NULL           
  link                 text            N/A                   NULL           
  fps                  text            N/A                   NULL           
  size                 text            N/A                   NULL           
  public_name          text            N/A                   NULL           
  size_short           text            N/A                   NULL           
  download_link        text            N/A                   NULL           
  created_at           datetime        N/A                   NULL           
  updated_at           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: banners
----------------------------------------
CREATE TABLE `banners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `link` varchar(150) DEFAULT NULL,
  `image` varchar(150) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           
  link                 varchar         150                   NULL           
  image                varchar         150                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: batch
----------------------------------------
CREATE TABLE `batch` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           
  start_date           date            N/A                   NULL           
  end_date             date            N/A                   NULL           
  course_id            int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: batch_lesson
----------------------------------------
CREATE TABLE `batch_lesson` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `summary` longtext DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `free` varchar(10) DEFAULT 'off',
  `duration` varchar(255) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  title                varchar         255                   NULL           
  course_id            int             11                    NULL           
  summary              longtext        N/A                   NULL           
  order                int             11                    NULL           
  free                 varchar         10                    off            
  duration             varchar         255                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: batch_lesson_files
----------------------------------------
CREATE TABLE `batch_lesson_files` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) NOT NULL,
  `video_id` int(11) NOT NULL DEFAULT 0,
  `title` varchar(255) DEFAULT NULL,
  `duration` varchar(255) DEFAULT NULL,
  `video_type` varchar(255) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `download_url` varchar(150) DEFAULT NULL,
  `thumbnail` mediumtext NOT NULL,
  `lesson_type` varchar(255) DEFAULT NULL,
  `attachment` varchar(255) DEFAULT NULL,
  `attachment_type` varchar(255) DEFAULT NULL,
  `summary` longtext DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `free` varchar(10) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  lesson_id            int             11                    NULL           
  video_id             int             11                    NULL           
  title                varchar         255                   NULL           
  duration             varchar         255                   NULL           
  video_type           varchar         255                   NULL           
  video_url            varchar         255                   NULL           
  download_url         varchar         150                   NULL           
  thumbnail            mediumtext      N/A                   NULL           
  lesson_type          varchar         255                   NULL           
  attachment           varchar         255                   NULL           
  attachment_type      varchar         255                   NULL           
  summary              longtext        N/A                   NULL           
  order                int             11                    NULL           
  free                 varchar         10                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: batch_lesson_schedule
----------------------------------------
CREATE TABLE `batch_lesson_schedule` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `lesson_id` int(11) DEFAULT NULL,
  `schedule_date` date DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_batch_lesson_schedule_course_batch` (`course_id`,`batch_id`),
  KEY `idx_batch_lesson_schedule_lesson_id` (`lesson_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  batch_id             int             11                    NULL           
  course_id            int             11                    NULL           
  lesson_id            int             11                    NULL           
  schedule_date        date            N/A                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_batch_lesson_schedule_course_batch course_id       NON-UNIQUE
  idx_batch_lesson_schedule_course_batch batch_id        NON-UNIQUE
  idx_batch_lesson_schedule_lesson_id lesson_id       NON-UNIQUE

================================================================================

TABLE: batch_students
----------------------------------------
CREATE TABLE `batch_students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  batch_id             int             11                    NULL           
  user_id              int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: batch_task_uploads
----------------------------------------
CREATE TABLE `batch_task_uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `file` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file`)),
  `remarks` mediumtext DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  task_id              int             11                    NULL           
  file                 longtext        N/A                   NULL           
  remarks              mediumtext      N/A                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: batch_tasks
----------------------------------------
CREATE TABLE `batch_tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) DEFAULT NULL,
  `task_type` tinyint(4) DEFAULT NULL COMMENT '1=reading, 2=writing, 3=listening, 4=speaking',
  `title` varchar(200) DEFAULT NULL,
  `file_question` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file_question`)),
  `file_answer` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file_answer`)),
  `file_audio` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file_audio`)),
  `order` int(11) NOT NULL DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_id            int             11                    NULL           
  task_type            tinyint         4                     NULL           
  title                varchar         200                   NULL           
  file_question        longtext        N/A                   NULL           
  file_answer          longtext        N/A                   NULL           
  file_audio           longtext        N/A                   NULL           
  order                int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: category
----------------------------------------
CREATE TABLE `category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `parent` int(11) DEFAULT 0,
  `slug` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `short_description` varchar(400) DEFAULT NULL,
  `video_type` varchar(50) DEFAULT NULL,
  `video_url` varchar(100) DEFAULT NULL,
  `font_awesome_class` varchar(255) DEFAULT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `banner` varchar(255) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  code                 varchar         255                   NULL           
  name                 varchar         255                   NULL           
  parent               int             11                    NULL           
  slug                 varchar         255                   NULL           
  description          text            N/A                   NULL           
  short_description    varchar         400                   NULL           
  video_type           varchar         50                    NULL           
  video_url            varchar         100                   NULL           
  font_awesome_class   varchar         255                   NULL           
  thumbnail            varchar         255                   NULL           
  banner               varchar         255                   NULL           
  order                int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: ci_sessions
----------------------------------------
CREATE TABLE `ci_sessions` (
  `id` varchar(40) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `timestamp` int(10) unsigned NOT NULL DEFAULT 0,
  `data` blob NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   varchar         40                    NULL           
  ip_address           varchar         45                    NULL           
  timestamp            int             10                    NULL           
  data                 blob            N/A                   NULL           

================================================================================

TABLE: class
----------------------------------------
CREATE TABLE `class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         100                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: contact_form
----------------------------------------
CREATE TABLE `contact_form` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(40) DEFAULT NULL,
  `email` varchar(30) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `phone` varchar(30) DEFAULT NULL,
  `address` varchar(150) DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         40                    NULL           
  email                varchar         30                    NULL           
  course_id            int             11                    NULL           
  phone                varchar         30                    NULL           
  address              varchar         150                   NULL           
  remarks              varchar         150                   NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: coupon_code
----------------------------------------
CREATE TABLE `coupon_code` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT 0,
  `package` varchar(100) DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `discount_perc` double DEFAULT NULL,
  `code` varchar(100) DEFAULT NULL,
  `validity` int(11) DEFAULT NULL,
  `total_no` int(11) DEFAULT NULL,
  `per_user_no` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  package_id           int             11                    NULL           
  user_id              int             11                    NULL           
  package              varchar         100                   NULL           
  amount               int             11                    NULL           
  discount_perc        double          N/A                   NULL           
  code                 varchar         100                   NULL           
  validity             int             11                    NULL           
  total_no             int             11                    NULL           
  per_user_no          int             11                    NULL           
  start_date           date            N/A                   NULL           
  end_date             date            N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: coupon_code_apply
----------------------------------------
CREATE TABLE `coupon_code_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL,
  `amount` double DEFAULT NULL,
  `status` varchar(15) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `package_id` int(11) DEFAULT NULL,
  `expires_on` date DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  coupon_id            int             11                    NULL           
  amount               double          N/A                   NULL           
  status               varchar         15                    NULL           
  course_id            int             11                    NULL           
  package_id           int             11                    NULL           
  expires_on           date            N/A                   NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course
----------------------------------------
CREATE TABLE `course` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `slug` varchar(300) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `course_type` tinyint(4) DEFAULT NULL COMMENT '1= Normal, 2 = Interactive',
  `user_type` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`user_type`)),
  `is_online` int(11) NOT NULL,
  `short_description` longtext DEFAULT NULL,
  `description` longtext DEFAULT NULL,
  `have_up_level_course` int(11) NOT NULL DEFAULT 0,
  `up_level_course_id` int(11) DEFAULT NULL,
  `outcomes` longtext DEFAULT NULL,
  `tips` text DEFAULT NULL,
  `language` varchar(255) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `sub_category_id` int(11) DEFAULT NULL,
  `language_id` int(11) DEFAULT NULL,
  `section` longtext DEFAULT NULL,
  `requirements` longtext DEFAULT NULL,
  `price` double DEFAULT NULL,
  `discount_flag` int(11) DEFAULT 0,
  `discounted_price` int(11) DEFAULT NULL,
  `level` varchar(50) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `banner` varchar(255) DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `video_type` varchar(150) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `visibility` int(11) DEFAULT NULL,
  `is_top_course` int(11) DEFAULT 0,
  `course_banner` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`course_banner`)),
  `meta_title` varchar(150) DEFAULT NULL,
  `meta_keywords` longtext DEFAULT NULL,
  `meta_description` longtext DEFAULT NULL,
  `is_free_course` int(11) DEFAULT NULL,
  `show_live` tinyint(1) NOT NULL DEFAULT 0,
  `show_material` tinyint(1) NOT NULL DEFAULT 0,
  `show_exam` tinyint(1) NOT NULL DEFAULT 0,
  `show_practice` tinyint(1) NOT NULL DEFAULT 0,
  `show_lock` int(11) NOT NULL DEFAULT 0,
  `is_life_time` int(11) NOT NULL DEFAULT 0,
  `is_batch_type` tinyint(1) DEFAULT 0,
  `also_offline_course` int(11) DEFAULT 0,
  `demo_class_link` varchar(150) DEFAULT NULL,
  `duration` varchar(20) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `offline` int(11) DEFAULT NULL,
  `offline_online` int(11) DEFAULT NULL COMMENT 'online =1 , offline= 0',
  `syllabus` text DEFAULT NULL,
  `zoom_id` varchar(50) DEFAULT NULL,
  `zoom_password` varchar(50) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  title                varchar         255                   NULL           
  slug                 varchar         300                   NULL           
  status               varchar         255                   NULL           
  course_type          tinyint         4                     NULL           
  user_type            longtext        N/A                   NULL           
  is_online            int             11                    NULL           
  short_description    longtext        N/A                   NULL           
  description          longtext        N/A                   NULL           
  have_up_level_course int             11                    NULL           
  up_level_course_id   int             11                    NULL           
  outcomes             longtext        N/A                   NULL           
  tips                 text            N/A                   NULL           
  language             varchar         255                   NULL           
  category_id          int             11                    NULL           
  sub_category_id      int             11                    NULL           
  language_id          int             11                    NULL           
  section              longtext        N/A                   NULL           
  requirements         longtext        N/A                   NULL           
  price                double          N/A                   NULL           
  discount_flag        int             11                    NULL           
  discounted_price     int             11                    NULL           
  level                varchar         50                    NULL           
  user_id              int             11                    NULL           
  thumbnail            varchar         255                   NULL           
  banner               varchar         255                   NULL           
  icon                 varchar         255                   NULL           
  video_type           varchar         150                   NULL           
  video_url            varchar         255                   NULL           
  visibility           int             11                    NULL           
  is_top_course        int             11                    NULL           
  course_banner        longtext        N/A                   NULL           
  meta_title           varchar         150                   NULL           
  meta_keywords        longtext        N/A                   NULL           
  meta_description     longtext        N/A                   NULL           
  is_free_course       int             11                    NULL           
  show_live            tinyint         1                     NULL           
  show_material        tinyint         1                     NULL           
  show_exam            tinyint         1                     NULL           
  show_practice        tinyint         1                     NULL           
  show_lock            int             11                    NULL           
  is_life_time         int             11                    NULL           
  is_batch_type        tinyint         1                     NULL           
  also_offline_course  int             11                    NULL           
  demo_class_link      varchar         150                   NULL           
  duration             varchar         20                    NULL           
  order                int             11                    NULL           
  offline              int             11                    NULL           
  offline_online       int             11                    NULL           
  syllabus             text            N/A                   NULL           
  zoom_id              varchar         50                    NULL           
  zoom_password        varchar         50                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_card
----------------------------------------
CREATE TABLE `course_card` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `details` longtext NOT NULL,
  `course_id` varchar(100) NOT NULL,
  `date` varchar(100) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  details              longtext        N/A                   NULL           
  course_id            varchar         100                   NULL           
  date                 varchar         100                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_exam
----------------------------------------
CREATE TABLE `course_exam` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `duration` varchar(20) DEFAULT NULL,
  `free` tinyint(1) DEFAULT NULL,
  `publish_result` tinyint(1) NOT NULL DEFAULT 0,
  `order` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  description          text            N/A                   NULL           
  course_id            int             11                    NULL           
  category_id          int             11                    NULL           
  duration             varchar         20                    NULL           
  free                 tinyint         1                     NULL           
  publish_result       tinyint         1                     NULL           
  order                int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_exam_answer
----------------------------------------
CREATE TABLE `course_exam_answer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `exam_id` int(11) DEFAULT NULL,
  `attempt_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `answer_status` tinyint(1) DEFAULT NULL,
  `submitted_answer` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`submitted_answer`)),
  `correct_answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`correct_answers`)),
  `datetime` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=531 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  exam_id              int             11                    NULL           
  attempt_id           int             11                    NULL           
  question_id          int             11                    NULL           
  answer_status        tinyint         1                     NULL           
  submitted_answer     longtext        N/A                   NULL           
  correct_answers      longtext        N/A                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_exam_attempt
----------------------------------------
CREATE TABLE `course_exam_attempt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `exam_id` int(11) DEFAULT NULL,
  `attempt_count` int(11) DEFAULT NULL,
  `total_questions` double DEFAULT NULL,
  `corrected_questions` double DEFAULT NULL,
  `wrong_questions` double DEFAULT NULL,
  `skipped_questions` double DEFAULT NULL,
  `exam_score` double DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  exam_id              int             11                    NULL           
  attempt_count        int             11                    NULL           
  total_questions      double          N/A                   NULL           
  corrected_questions  double          N/A                   NULL           
  wrong_questions      double          N/A                   NULL           
  skipped_questions    double          N/A                   NULL           
  exam_score           double          N/A                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_exam_questions
----------------------------------------
CREATE TABLE `course_exam_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `exam_id` int(11) DEFAULT NULL,
  `title` mediumtext DEFAULT NULL,
  `title_file` varchar(200) DEFAULT NULL,
  `hint` mediumtext DEFAULT NULL,
  `hint_file` varchar(200) DEFAULT NULL,
  `solution` mediumtext DEFAULT NULL,
  `solution_file` varchar(200) DEFAULT NULL,
  `is_equation` tinyint(1) DEFAULT NULL,
  `is_equation_solution` tinyint(1) DEFAULT NULL,
  `number_of_options` tinyint(4) DEFAULT NULL,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`options`)),
  `correct_answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`correct_answers`)),
  `order` int(11) DEFAULT NULL,
  `q_type` tinyint(4) DEFAULT NULL COMMENT '1=text, 2=image',
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  exam_id              int             11                    NULL           
  title                mediumtext      N/A                   NULL           
  title_file           varchar         200                   NULL           
  hint                 mediumtext      N/A                   NULL           
  hint_file            varchar         200                   NULL           
  solution             mediumtext      N/A                   NULL           
  solution_file        varchar         200                   NULL           
  is_equation          tinyint         1                     NULL           
  is_equation_solution tinyint         1                     NULL           
  number_of_options    tinyint         4                     NULL           
  options              longtext        N/A                   NULL           
  correct_answers      longtext        N/A                   NULL           
  order                int             11                    NULL           
  q_type               tinyint         4                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  deleted              tinyint         1                     NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_language
----------------------------------------
CREATE TABLE `course_language` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(250) DEFAULT NULL,
  `description` varchar(250) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `icon` varchar(250) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         250                   NULL           
  description          varchar         250                   NULL           
  course_id            int             11                    NULL           
  icon                 varchar         250                   NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: course_type
----------------------------------------
CREATE TABLE `course_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: create_order
----------------------------------------
CREATE TABLE `create_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT NULL,
  `order_id` varchar(50) DEFAULT NULL,
  `payment_id` int(11) DEFAULT NULL,
  `payment_id_raz` int(11) DEFAULT NULL,
  `amount` double DEFAULT NULL,
  `redeemed_amount` double DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `payment_captured` int(11) DEFAULT NULL,
  `order_status` varchar(20) DEFAULT NULL COMMENT 'pending, cancelled, completed',
  `payment_through` int(11) NOT NULL DEFAULT 1 COMMENT '1=App, 2=Landing page	',
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1512 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  package_id           int             11                    NULL           
  coupon_id            int             11                    NULL           
  order_id             varchar         50                    NULL           
  payment_id           int             11                    NULL           
  payment_id_raz       int             11                    NULL           
  amount               double          N/A                   NULL           
  redeemed_amount      double          N/A                   NULL           
  user_id              int             11                    NULL           
  payment_captured     int             11                    NULL           
  order_status         varchar         20                    NULL           
  payment_through      int             11                    1              
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: currency
----------------------------------------
CREATE TABLE `currency` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `code` varchar(255) DEFAULT NULL,
  `symbol` varchar(255) DEFAULT NULL,
  `paypal_supported` int(11) DEFAULT NULL,
  `stripe_supported` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         255                   NULL           
  code                 varchar         255                   NULL           
  symbol               varchar         255                   NULL           
  paypal_supported     int             11                    NULL           
  stripe_supported     int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: demo_video
----------------------------------------
CREATE TABLE `demo_video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `course_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`course_id`)),
  `video_type` varchar(15) DEFAULT NULL,
  `video_url` varchar(150) DEFAULT NULL,
  `thumbnail` varchar(150) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  course_id            longtext        N/A                   NULL           
  video_type           varchar         15                    NULL           
  video_url            varchar         150                   NULL           
  thumbnail            varchar         150                   NULL           
  order                int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: enquiry
----------------------------------------
CREATE TABLE `enquiry` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(40) DEFAULT NULL,
  `email` varchar(40) DEFAULT NULL,
  `phone` varchar(30) DEFAULT NULL,
  `address` varchar(150) DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         40                    NULL           
  email                varchar         40                    NULL           
  phone                varchar         30                    NULL           
  address              varchar         150                   NULL           
  remarks              varchar         150                   NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: enrol
----------------------------------------
CREATE TABLE `enrol` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `package_id` int(11) DEFAULT NULL,
  `premium` tinyint(1) NOT NULL DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_enrol_user_course` (`user_id`,`course_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  user_id              int             11                    NULL           
  course_id            int             11                    NULL           
  batch_id             int             11                    NULL           
  package_id           int             11                    NULL           
  premium              tinyint         1                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_enrol_user_course user_id         NON-UNIQUE
  idx_enrol_user_course course_id       NON-UNIQUE

================================================================================

TABLE: enrol_ins
----------------------------------------
CREATE TABLE `enrol_ins` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `date_added` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  section_id           int             11                    NULL           
  date_added           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: expense
----------------------------------------
CREATE TABLE `expense` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `amount` double DEFAULT 0,
  `category_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `account_id` int(11) DEFAULT NULL,
  `expense_date` date DEFAULT NULL,
  `reference_no` varchar(50) DEFAULT NULL,
  `remarks` varchar(400) DEFAULT NULL,
  `instructor_salary_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           
  amount               double          N/A                   NULL           
  category_id          int             11                    NULL           
  user_id              int             11                    NULL           
  account_id           int             11                    NULL           
  expense_date         date            N/A                   NULL           
  reference_no         varchar         50                    NULL           
  remarks              varchar         400                   NULL           
  instructor_salary_id int             11                    NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           
  deleted_at           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: expense_category
----------------------------------------
CREATE TABLE `expense_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `is_system` tinyint(1) DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           
  is_system            tinyint         1                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: favourites
----------------------------------------
CREATE TABLE `favourites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_id` int(11) DEFAULT NULL,
  `item_type` varchar(100) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  item_id              int             11                    NULL           
  item_type            varchar         100                   NULL           
  course_id            int             11                    NULL           
  user_id              int             11                    NULL           
  create_date          datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: feed
----------------------------------------
CREATE TABLE `feed` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `content` mediumtext DEFAULT NULL,
  `image` varchar(150) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `feed_type` varchar(15) DEFAULT NULL,
  `video_url` varchar(150) DEFAULT NULL,
  `created_on` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_on` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=48 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  content              mediumtext      N/A                   NULL           
  image                varchar         150                   NULL           
  category_id          int             11                    NULL           
  course_id            int             11                    NULL           
  feed_type            varchar         15                    NULL           
  video_url            varchar         150                   NULL           
  created_on           datetime        N/A                   current_timestamp()
  updated_on           datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: feed_bookmarks
----------------------------------------
CREATE TABLE `feed_bookmarks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `feed_id` int(11) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  feed_id              int             11                    NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: feed_category
----------------------------------------
CREATE TABLE `feed_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: feed_likes
----------------------------------------
CREATE TABLE `feed_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `feed_id` int(11) DEFAULT NULL,
  `datetime` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=336 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  feed_id              int             11                    NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: flag
----------------------------------------
CREATE TABLE `flag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message` varchar(1000) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  message              varchar         1000                  NULL           
  user_id              int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: flag_lesson
----------------------------------------
CREATE TABLE `flag_lesson` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_files_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` varchar(6000) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_files_id      int             11                    NULL           
  user_id              int             11                    NULL           
  message              varchar         6000                  NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: frontend_settings
----------------------------------------
CREATE TABLE `frontend_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) DEFAULT NULL,
  `value` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  key                  varchar         255                   NULL           
  value                longtext        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: generated_certificates
----------------------------------------
CREATE TABLE `generated_certificates` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `certificate_url` text DEFAULT NULL,
  `certificate_no` varchar(160) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `status` varchar(160) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `created_on` datetime DEFAULT current_timestamp(),
  `approved_date` datetime DEFAULT NULL,
  `rejected_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1531 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  certificate_url      text            N/A                   NULL           
  certificate_no       varchar         160                   NULL           
  user_id              int             11                    NULL           
  course_id            int             11                    NULL           
  status               varchar         160                   NULL           
  date                 date            N/A                   NULL           
  created_on           datetime        N/A                   current_timestamp()
  approved_date        datetime        N/A                   NULL           
  rejected_date        datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: habit
----------------------------------------
CREATE TABLE `habit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(260) DEFAULT NULL,
  `habit_category` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         260                   NULL           
  habit_category       int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: habit_category
----------------------------------------
CREATE TABLE `habit_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(260) DEFAULT NULL,
  `icon` text DEFAULT NULL,
  `order` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         260                   NULL           
  icon                 text            N/A                   NULL           
  order                int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: instructor_enrol
----------------------------------------
CREATE TABLE `instructor_enrol` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `instructor_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  instructor_id        int             11                    NULL           
  course_id            int             11                    NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: instructor_students
----------------------------------------
CREATE TABLE `instructor_students` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `student_id` int(11) NOT NULL,
  `instructor_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  student_id           int             11                    NULL           
  instructor_id        int             11                    NULL           
  course_id            int             11                    NULL           
  status               tinyint         1                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: job_announcement
----------------------------------------
CREATE TABLE `job_announcement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `description` varchar(250) DEFAULT NULL,
  `category` varchar(150) DEFAULT NULL,
  `department` varchar(150) DEFAULT NULL,
  `age_limit` varchar(100) DEFAULT NULL,
  `vacancy` int(11) DEFAULT NULL,
  `last_date` date DEFAULT NULL,
  `qualification` varchar(150) DEFAULT NULL,
  `link` varchar(150) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  description          varchar         250                   NULL           
  category             varchar         150                   NULL           
  department           varchar         150                   NULL           
  age_limit            varchar         100                   NULL           
  vacancy              int             11                    NULL           
  last_date            date            N/A                   NULL           
  qualification        varchar         150                   NULL           
  link                 varchar         150                   NULL           
  status               int             11                    NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: language
----------------------------------------
CREATE TABLE `language` (
  `phrase_id` int(11) NOT NULL AUTO_INCREMENT,
  `phrase` longtext DEFAULT NULL,
  `english` longtext DEFAULT NULL,
  `Bengali` longtext DEFAULT NULL,
  PRIMARY KEY (`phrase_id`)
) ENGINE=MyISAM AUTO_INCREMENT=594 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  phrase_id            int             11         PRIMARY    NULL           
  phrase               longtext        N/A                   NULL           
  english              longtext        N/A                   NULL           
  Bengali              longtext        N/A                   NULL           

INDEXES:
  PRIMARY              phrase_id       UNIQUE    

================================================================================

TABLE: last_video
----------------------------------------
CREATE TABLE `last_video` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_id` int(11) NOT NULL,
  `video_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `progress` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  section_id           int             11                    NULL           
  video_id             int             11                    NULL           
  user_id              int             11                    NULL           
  progress             int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: lead_history
----------------------------------------
CREATE TABLE `lead_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lead_id` int(11) DEFAULT NULL,
  `cre_id` int(11) DEFAULT NULL,
  `lead_status` int(11) DEFAULT NULL,
  `lead_source` int(11) DEFAULT NULL,
  `followup_date` date DEFAULT NULL,
  `remarks` varchar(400) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lead_id              int             11                    NULL           
  cre_id               int             11                    NULL           
  lead_status          int             11                    NULL           
  lead_source          int             11                    NULL           
  followup_date        date            N/A                   NULL           
  remarks              varchar         400                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: lead_manager
----------------------------------------
CREATE TABLE `lead_manager` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) DEFAULT NULL,
  `phone` varchar(30) DEFAULT NULL,
  `phone_secondary` varchar(20) DEFAULT NULL,
  `email` varchar(30) DEFAULT NULL,
  `password` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         20                    NULL           
  phone                varchar         30                    NULL           
  phone_secondary      varchar         20                    NULL           
  email                varchar         30                    NULL           
  password             varchar         20                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: lead_source
----------------------------------------
CREATE TABLE `lead_source` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `source` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  source               varchar         20                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: lead_status
----------------------------------------
CREATE TABLE `lead_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `status` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  status               varchar         50                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: lead_upload
----------------------------------------
CREATE TABLE `lead_upload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attempted` int(11) DEFAULT NULL,
  `completed` int(11) DEFAULT NULL,
  `duplicate` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  attempted            int             11                    NULL           
  completed            int             11                    NULL           
  duplicate            int             11                    NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: leads_upload
----------------------------------------
CREATE TABLE `leads_upload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `attempted` int(11) DEFAULT NULL,
  `completed` int(11) DEFAULT NULL,
  `duplicate` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  attempted            int             11                    NULL           
  completed            int             11                    NULL           
  duplicate            int             11                    NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: lesson
----------------------------------------
CREATE TABLE `lesson` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `topic_id` int(11) DEFAULT NULL,
  `module_category_id` int(11) NOT NULL DEFAULT 0,
  `lesson_type` int(11) DEFAULT NULL COMMENT '1= practice, 2= quiz, 3=video, 4= materials, 5= liveclass',
  `thumbnail` varchar(150) DEFAULT NULL,
  `summary` longtext DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `show_popup` int(11) NOT NULL DEFAULT 1,
  `schedule_date` datetime DEFAULT NULL,
  `free` varchar(10) DEFAULT NULL,
  `tips` longtext DEFAULT NULL,
  `duration` varchar(255) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_lesson_course_id` (`course_id`),
  KEY `idx_lesson_course_order` (`course_id`,`order`)
) ENGINE=InnoDB AUTO_INCREMENT=357 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  title                varchar         255                   NULL           
  course_id            int             11                    NULL           
  section_id           int             11                    NULL           
  topic_id             int             11                    NULL           
  module_category_id   int             11                    NULL           
  lesson_type          int             11                    NULL           
  thumbnail            varchar         150                   NULL           
  summary              longtext        N/A                   NULL           
  order                int             11                    NULL           
  show_popup           int             11                    1              
  schedule_date        datetime        N/A                   NULL           
  free                 varchar         10                    NULL           
  tips                 longtext        N/A                   NULL           
  duration             varchar         255                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_lesson_course_id course_id       NON-UNIQUE
  idx_lesson_course_order course_id       NON-UNIQUE
  idx_lesson_course_order order           NON-UNIQUE

================================================================================

TABLE: lesson_files
----------------------------------------
CREATE TABLE `lesson_files` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) NOT NULL,
  `video_id` int(11) NOT NULL DEFAULT 0,
  `title` varchar(255) DEFAULT NULL,
  `duration` varchar(255) DEFAULT NULL,
  `video_type` varchar(255) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `video_url_old` varchar(100) DEFAULT NULL,
  `html5_video_url` varchar(450) DEFAULT NULL,
  `download_url` varchar(150) DEFAULT NULL,
  `thumbnail` mediumtext NOT NULL,
  `lesson_type` varchar(255) DEFAULT NULL,
  `attachment` longtext DEFAULT NULL,
  `attachment_type` varchar(255) DEFAULT NULL,
  `is_portrait` int(11) NOT NULL DEFAULT 0,
  `is_downloadable` int(11) NOT NULL DEFAULT 0,
  `activity_type` varchar(250) DEFAULT NULL,
  `activity_file` longtext DEFAULT NULL,
  `audio_file` varchar(150) DEFAULT NULL,
  `notes` longtext DEFAULT NULL,
  `description` mediumtext DEFAULT NULL,
  `is_practice` int(11) DEFAULT NULL,
  `publish_result` tinyint(4) DEFAULT NULL,
  `from_date` date DEFAULT NULL,
  `from_time` time DEFAULT NULL,
  `to_date` date DEFAULT NULL,
  `to_time` time DEFAULT NULL,
  `live_type` int(11) DEFAULT NULL,
  `student_id` int(11) DEFAULT NULL,
  `habit_id` int(11) DEFAULT NULL,
  `zoom_id` varchar(100) DEFAULT NULL,
  `zoom_password` varchar(100) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `summary` longtext DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `free` varchar(10) DEFAULT NULL,
  `schedule_date` datetime DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_lesson_files_lesson_id` (`lesson_id`)
) ENGINE=InnoDB AUTO_INCREMENT=943 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  lesson_id            int             11                    NULL           
  video_id             int             11                    NULL           
  title                varchar         255                   NULL           
  duration             varchar         255                   NULL           
  video_type           varchar         255                   NULL           
  video_url            varchar         255                   NULL           
  video_url_old        varchar         100                   NULL           
  html5_video_url      varchar         450                   NULL           
  download_url         varchar         150                   NULL           
  thumbnail            mediumtext      N/A                   NULL           
  lesson_type          varchar         255                   NULL           
  attachment           longtext        N/A                   NULL           
  attachment_type      varchar         255                   NULL           
  is_portrait          int             11                    NULL           
  is_downloadable      int             11                    NULL           
  activity_type        varchar         250                   NULL           
  activity_file        longtext        N/A                   NULL           
  audio_file           varchar         150                   NULL           
  notes                longtext        N/A                   NULL           
  description          mediumtext      N/A                   NULL           
  is_practice          int             11                    NULL           
  publish_result       tinyint         4                     NULL           
  from_date            date            N/A                   NULL           
  from_time            time            N/A                   NULL           
  to_date              date            N/A                   NULL           
  to_time              time            N/A                   NULL           
  live_type            int             11                    NULL           
  student_id           int             11                    NULL           
  habit_id             int             11                    NULL           
  zoom_id              varchar         100                   NULL           
  zoom_password        varchar         100                   NULL           
  role_id              int             11                    NULL           
  summary              longtext        N/A                   NULL           
  order                int             11                    NULL           
  free                 varchar         10                    NULL           
  schedule_date        datetime        N/A                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_lesson_files_lesson_id lesson_id       NON-UNIQUE

================================================================================

TABLE: live_settings
----------------------------------------
CREATE TABLE `live_settings` (
  `id` int(11) NOT NULL,
  `meeting_name` varchar(100) DEFAULT NULL,
  `meeting_id` varchar(50) DEFAULT NULL,
  `meeting_password` varchar(50) DEFAULT NULL,
  `api_client_id` varchar(50) DEFAULT NULL,
  `api_client_secret` varchar(50) DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  meeting_name         varchar         100                   NULL           
  meeting_id           varchar         50                    NULL           
  meeting_password     varchar         50                    NULL           
  api_client_id        varchar         50                    NULL           
  api_client_secret    varchar         50                    NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: liveclass_attendance
----------------------------------------
CREATE TABLE `liveclass_attendance` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `zoom_id` int(11) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_liveclass_attendance_user_zoom` (`user_id`,`zoom_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  zoom_id              int             11                    NULL           
  created_date         datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_liveclass_attendance_user_zoom user_id         NON-UNIQUE
  idx_liveclass_attendance_user_zoom zoom_id         NON-UNIQUE

================================================================================

TABLE: master_concept
----------------------------------------
CREATE TABLE `master_concept` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `video_type` varchar(15) DEFAULT NULL,
  `video_url` varchar(150) DEFAULT NULL,
  `thumbnail` varchar(150) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  video_type           varchar         15                    NULL           
  video_url            varchar         150                   NULL           
  thumbnail            varchar         150                   NULL           
  order                int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: material_progress_status
----------------------------------------
CREATE TABLE `material_progress_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `lesson_file_id` int(11) DEFAULT NULL,
  `attachment_type` varchar(255) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_material_progress_user_lesson` (`user_id`,`lesson_file_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5952 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  lesson_file_id       int             11                    NULL           
  attachment_type      varchar         255                   NULL           
  created_date         datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_material_progress_user_lesson user_id         NON-UNIQUE
  idx_material_progress_user_lesson lesson_file_id  NON-UNIQUE

================================================================================

TABLE: message
----------------------------------------
CREATE TABLE `message` (
  `message_id` int(11) NOT NULL AUTO_INCREMENT,
  `message_thread_code` longtext DEFAULT NULL,
  `message` longtext DEFAULT NULL,
  `message_file` varchar(150) DEFAULT NULL,
  `sender` longtext DEFAULT NULL,
  `message_type` varchar(10) DEFAULT NULL COMMENT '1= text, 2= image, 3= pdf',
  `date_time` datetime NOT NULL DEFAULT current_timestamp(),
  `timestamp` longtext DEFAULT NULL,
  `read_status` int(11) DEFAULT NULL,
  PRIMARY KEY (`message_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  message_id           int             11         PRIMARY    NULL           
  message_thread_code  longtext        N/A                   NULL           
  message              longtext        N/A                   NULL           
  message_file         varchar         150                   NULL           
  sender               longtext        N/A                   NULL           
  message_type         varchar         10                    NULL           
  date_time            datetime        N/A                   current_timestamp()
  timestamp            longtext        N/A                   NULL           
  read_status          int             11                    NULL           

INDEXES:
  PRIMARY              message_id      UNIQUE    

================================================================================

TABLE: message_read_status
----------------------------------------
CREATE TABLE `message_read_status` (
  `read_status_id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  PRIMARY KEY (`read_status_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  read_status_id       int             11         PRIMARY    NULL           
  message_id           int             11                    NULL           
  user_id              int             11                    NULL           
  status               tinyint         1                     1              

INDEXES:
  PRIMARY              read_status_id  UNIQUE    

================================================================================

TABLE: message_thread
----------------------------------------
CREATE TABLE `message_thread` (
  `message_thread_id` int(11) NOT NULL AUTO_INCREMENT,
  `message_thread_code` longtext DEFAULT NULL,
  `sender` varchar(255) DEFAULT '',
  `receiver` varchar(255) DEFAULT '',
  `thread_type` tinyint(4) DEFAULT NULL COMMENT '1 = single, 2 = group',
  `item_type` smallint(6) DEFAULT NULL COMMENT '1=student, 2=teacher, 3=section, 4=course, 5=batch',
  `last_message_timestamp` longtext DEFAULT NULL,
  PRIMARY KEY (`message_thread_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  message_thread_id    int             11         PRIMARY    NULL           
  message_thread_code  longtext        N/A                   NULL           
  sender               varchar         255                   NULL           
  receiver             varchar         255                   NULL           
  thread_type          tinyint         4                     NULL           
  item_type            smallint        6                     NULL           
  last_message_timestamp longtext        N/A                   NULL           

INDEXES:
  PRIMARY              message_thread_id UNIQUE    

================================================================================

TABLE: module
----------------------------------------
CREATE TABLE `module` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: module_categories
----------------------------------------
CREATE TABLE `module_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(250) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         250                   NULL           
  status               int             11                    NULL           
  created_by           int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_by           int             11                    NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: module_completion_history
----------------------------------------
CREATE TABLE `module_completion_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `module_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `show_popup` int(11) DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_module_completion_user_module` (`user_id`,`module_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6195 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  module_id            int             11                    NULL           
  user_id              int             11                    NULL           
  show_popup           int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_module_completion_user_module user_id         NON-UNIQUE
  idx_module_completion_user_module module_id       NON-UNIQUE

================================================================================

TABLE: module_wise_points
----------------------------------------
CREATE TABLE `module_wise_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL COMMENT '1 = Module, 2=lesson_file',
  `item_id` int(11) DEFAULT NULL,
  `points` bigint(20) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  type                 int             11                    NULL           
  item_id              int             11                    NULL           
  points               bigint          20                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: news
----------------------------------------
CREATE TABLE `news` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `description` mediumtext DEFAULT NULL,
  `image` varchar(250) DEFAULT NULL,
  `created_on` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  description          mediumtext      N/A                   NULL           
  image                varchar         250                   NULL           
  created_on           datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: notice
----------------------------------------
CREATE TABLE `notice` (
  `notice_id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) NOT NULL,
  `section_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `description` varchar(100) NOT NULL,
  `date` date DEFAULT NULL,
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  notice_id            int             11         PRIMARY    NULL           
  course_id            int             11                    NULL           
  section_id           int             11                    NULL           
  title                varchar         100                   NULL           
  description          varchar         100                   NULL           
  date                 date            N/A                   NULL           

INDEXES:
  PRIMARY              notice_id       UNIQUE    

================================================================================

TABLE: notification
----------------------------------------
CREATE TABLE `notification` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` mediumtext NOT NULL,
  `description` mediumtext NOT NULL,
  `course_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `premium` int(11) NOT NULL,
  `notification_for` int(11) NOT NULL DEFAULT 0 COMMENT '0 = for all, 1 = paid, 2 = not-paid',
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `image` varchar(155) DEFAULT NULL,
  `external_link` varchar(155) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                mediumtext      N/A                   NULL           
  description          mediumtext      N/A                   NULL           
  course_id            int             11                    NULL           
  category_id          int             11                    NULL           
  premium              int             11                    NULL           
  notification_for     int             11                    NULL           
  timestamp            timestamp       N/A                   current_timestamp()
  image                varchar         155                   NULL           
  external_link        varchar         155                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: orders
----------------------------------------
CREATE TABLE `orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `title` varchar(150) DEFAULT NULL,
  `order_status` varchar(150) DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `track_id` varchar(50) DEFAULT NULL,
  `order_date` date DEFAULT NULL,
  `delivery_date` date DEFAULT NULL,
  `amount` double DEFAULT NULL,
  `date_time` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  title                varchar         150                   NULL           
  order_status         varchar         150                   NULL           
  remarks              varchar         150                   NULL           
  track_id             varchar         50                    NULL           
  order_date           date            N/A                   NULL           
  delivery_date        date            N/A                   NULL           
  amount               double          N/A                   NULL           
  date_time            datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: package
----------------------------------------
CREATE TABLE `package` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `type` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `course_id` int(11) NOT NULL,
  `section_id` varchar(50) DEFAULT NULL,
  `amount` double NOT NULL DEFAULT 0,
  `discount` double NOT NULL DEFAULT 0,
  `landing_amount` double NOT NULL DEFAULT 0,
  `is_free` tinyint(1) DEFAULT 0,
  `package_type` int(11) NOT NULL,
  `remarks` varchar(100) DEFAULT '',
  `offline` int(11) NOT NULL DEFAULT 0,
  `description` mediumtext DEFAULT NULL,
  `start_day` int(11) DEFAULT NULL,
  `end_day` int(11) DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) NOT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_package_course_id` (`course_id`)
) ENGINE=InnoDB AUTO_INCREMENT=55 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         100                   NULL           
  type                 int             11                    NULL           
  category_id          int             11                    NULL           
  course_id            int             11                    NULL           
  section_id           varchar         50                    NULL           
  amount               double          N/A                   NULL           
  discount             double          N/A                   NULL           
  landing_amount       double          N/A                   NULL           
  is_free              tinyint         1                     NULL           
  package_type         int             11                    NULL           
  remarks              varchar         100                   NULL           
  offline              int             11                    NULL           
  description          mediumtext      N/A                   NULL           
  start_day            int             11                    NULL           
  end_day              int             11                    NULL           
  duration             int             11                    NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_package_course_id course_id       NON-UNIQUE

================================================================================

TABLE: package_features
----------------------------------------
CREATE TABLE `package_features` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `title` varchar(100) DEFAULT NULL,
  `value_type` varchar(10) DEFAULT NULL,
  `value` varchar(100) DEFAULT NULL,
  `listing_order` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  package_id           int             11                    NULL           
  title                varchar         100                   NULL           
  value_type           varchar         10                    NULL           
  value                varchar         100                   NULL           
  listing_order        int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: package_lessons
----------------------------------------
CREATE TABLE `package_lessons` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `package_id` int(11) DEFAULT NULL,
  `lesson_id` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=93 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  package_id           int             11                    NULL           
  lesson_id            int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: payment_info
----------------------------------------
CREATE TABLE `payment_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_no` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `package_id` int(11) DEFAULT NULL,
  `amount_paid` int(11) DEFAULT NULL,
  `account_id` int(11) DEFAULT NULL,
  `discount` int(11) DEFAULT NULL,
  `redeemed_amount` double DEFAULT NULL,
  `user_phone` varchar(20) DEFAULT NULL,
  `user_email` varchar(55) DEFAULT NULL,
  `coupon_id` int(11) DEFAULT 0,
  `code` varchar(100) DEFAULT '',
  `razorpay_payment_id` varchar(50) DEFAULT NULL,
  `razorpay_order_id` varchar(50) DEFAULT NULL,
  `razorpay_signature` varchar(250) DEFAULT NULL,
  `note` varchar(255) DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `package_duration` int(11) DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `is_extended` tinyint(1) DEFAULT NULL,
  `is_upgrade` tinyint(1) NOT NULL DEFAULT 0,
  `upgrade_package_id` int(11) DEFAULT NULL,
  `payment_through` int(11) NOT NULL DEFAULT 1 COMMENT '1=App, 2=Landing page',
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_payment_info_user_id` (`user_id`),
  KEY `idx_payment_info_package_user` (`package_id`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1058 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  invoice_no           int             11                    NULL           
  user_id              int             11                    NULL           
  package_id           int             11                    NULL           
  amount_paid          int             11                    NULL           
  account_id           int             11                    NULL           
  discount             int             11                    NULL           
  redeemed_amount      double          N/A                   NULL           
  user_phone           varchar         20                    NULL           
  user_email           varchar         55                    NULL           
  coupon_id            int             11                    NULL           
  code                 varchar         100                   NULL           
  razorpay_payment_id  varchar         50                    NULL           
  razorpay_order_id    varchar         50                    NULL           
  razorpay_signature   varchar         250                   NULL           
  note                 varchar         255                   NULL           
  payment_date         datetime        N/A                   NULL           
  package_duration     int             11                    NULL           
  expiry_date          date            N/A                   NULL           
  is_extended          tinyint         1                     NULL           
  is_upgrade           tinyint         1                     NULL           
  upgrade_package_id   int             11                    NULL           
  payment_through      int             11                    1              
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  deleted_at           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_payment_info_user_id user_id         NON-UNIQUE
  idx_payment_info_package_user package_id      NON-UNIQUE
  idx_payment_info_package_user user_id         NON-UNIQUE

================================================================================

TABLE: pdf_category
----------------------------------------
CREATE TABLE `pdf_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  order                int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: pdf_material
----------------------------------------
CREATE TABLE `pdf_material` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `url` varchar(150) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  url                  varchar         150                   NULL           
  category_id          int             11                    NULL           
  order                int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: permission
----------------------------------------
CREATE TABLE `permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) DEFAULT NULL,
  `slug` varchar(100) DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         50                    NULL           
  slug                 varchar         100                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: permission_relationship
----------------------------------------
CREATE TABLE `permission_relationship` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `permission_id` int(11) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `master` tinyint(1) DEFAULT NULL,
  `read` tinyint(1) DEFAULT NULL,
  `write` tinyint(1) DEFAULT NULL,
  `delete` tinyint(1) DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  permission_id        int             11                    NULL           
  role_id              int             11                    NULL           
  master               tinyint         1                     NULL           
  read                 tinyint         1                     NULL           
  write                tinyint         1                     NULL           
  delete               tinyint         1                     NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: point_settings
----------------------------------------
CREATE TABLE `point_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(255) DEFAULT NULL,
  `value` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  key                  varchar         255                   NULL           
  value                longtext        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: practice_answer
----------------------------------------
CREATE TABLE `practice_answer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `attempt_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `answer_correct` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`answer_correct`)),
  `answer_submitted` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`answer_submitted`)),
  `answer_status` tinyint(4) DEFAULT NULL COMMENT '1=correct, 2=incorrect, 3=skip',
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  attempt_id           int             11                    NULL           
  question_id          int             11                    NULL           
  answer_correct       longtext        N/A                   NULL           
  answer_submitted     longtext        N/A                   NULL           
  answer_status        tinyint         4                     NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: practice_attempt
----------------------------------------
CREATE TABLE `practice_attempt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `question_no` int(11) DEFAULT NULL,
  `question_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`question_id`)),
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `time_taken` varchar(30) DEFAULT NULL,
  `correct` int(11) DEFAULT NULL,
  `incorrect` int(11) DEFAULT NULL,
  `skip` int(11) DEFAULT NULL,
  `score` double DEFAULT NULL,
  `submit_status` tinyint(1) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  course_id            int             11                    NULL           
  question_no          int             11                    NULL           
  question_id          longtext        N/A                   NULL           
  start_time           datetime        N/A                   NULL           
  end_time             datetime        N/A                   NULL           
  time_taken           varchar         30                    NULL           
  correct              int             11                    NULL           
  incorrect            int             11                    NULL           
  skip                 int             11                    NULL           
  score                double          N/A                   NULL           
  submit_status        tinyint         1                     NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: premium
----------------------------------------
CREATE TABLE `premium` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `course_id` int(11) NOT NULL,
  `section_id` varchar(250) DEFAULT NULL,
  `unlock_all` tinyint(1) DEFAULT NULL,
  `premium` tinyint(1) DEFAULT 1,
  `date_added` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  course_id            int             11                    NULL           
  section_id           varchar         250                   NULL           
  unlock_all           tinyint         1                     NULL           
  premium              tinyint         1                     1              
  date_added           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: question
----------------------------------------
CREATE TABLE `question` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `quiz_id` int(11) DEFAULT NULL,
  `title` longtext DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `number_of_options` int(11) DEFAULT NULL,
  `options` longtext DEFAULT NULL,
  `correct_answers` longtext DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `q_type` int(11) NOT NULL,
  `hint` longtext NOT NULL,
  `title_file` longtext NOT NULL,
  `hint_file` longtext NOT NULL,
  `solution` mediumtext DEFAULT NULL,
  `solution_file` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  quiz_id              int             11                    NULL           
  title                longtext        N/A                   NULL           
  type                 varchar         255                   NULL           
  number_of_options    int             11                    NULL           
  options              longtext        N/A                   NULL           
  correct_answers      longtext        N/A                   NULL           
  order                int             11                    NULL           
  q_type               int             11                    NULL           
  hint                 longtext        N/A                   NULL           
  title_file           longtext        N/A                   NULL           
  hint_file            longtext        N/A                   NULL           
  solution             mediumtext      N/A                   NULL           
  solution_file        varchar         150                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: question_bank
----------------------------------------
CREATE TABLE `question_bank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `title` mediumtext DEFAULT NULL,
  `title_file` varchar(200) DEFAULT NULL,
  `hint` mediumtext DEFAULT NULL,
  `hint_file` varchar(200) DEFAULT NULL,
  `solution` mediumtext DEFAULT NULL,
  `solution_file` varchar(200) DEFAULT NULL,
  `is_equation` tinyint(1) DEFAULT NULL,
  `is_equation_solution` tinyint(1) DEFAULT NULL,
  `number_of_options` tinyint(4) DEFAULT NULL,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`options`)),
  `correct_answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`correct_answers`)),
  `order` int(11) DEFAULT NULL,
  `q_type` tinyint(4) DEFAULT NULL COMMENT '1=text, 2=image',
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_id            int             11                    NULL           
  section_id           int             11                    NULL           
  course_id            int             11                    NULL           
  category_id          int             11                    NULL           
  title                mediumtext      N/A                   NULL           
  title_file           varchar         200                   NULL           
  hint                 mediumtext      N/A                   NULL           
  hint_file            varchar         200                   NULL           
  solution             mediumtext      N/A                   NULL           
  solution_file        varchar         200                   NULL           
  is_equation          tinyint         1                     NULL           
  is_equation_solution tinyint         1                     NULL           
  number_of_options    tinyint         4                     NULL           
  options              longtext        N/A                   NULL           
  correct_answers      longtext        N/A                   NULL           
  order                int             11                    NULL           
  q_type               tinyint         4                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  deleted              tinyint         1                     NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: quiz
----------------------------------------
CREATE TABLE `quiz` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `lesson_id` int(11) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `is_practice` int(11) NOT NULL DEFAULT 0,
  `duration` varchar(20) DEFAULT NULL,
  `free` tinyint(1) DEFAULT NULL,
  `publish_result` tinyint(1) NOT NULL DEFAULT 0,
  `order` int(11) DEFAULT NULL,
  `from_date` date DEFAULT NULL,
  `from_time` time DEFAULT NULL,
  `to_date` date DEFAULT NULL,
  `to_time` time DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  description          text            N/A                   NULL           
  lesson_id            int             11                    NULL           
  section_id           int             11                    NULL           
  course_id            int             11                    NULL           
  category_id          int             11                    NULL           
  is_practice          int             11                    NULL           
  duration             varchar         20                    NULL           
  free                 tinyint         1                     NULL           
  publish_result       tinyint         1                     NULL           
  order                int             11                    NULL           
  from_date            date            N/A                   NULL           
  from_time            time            N/A                   NULL           
  to_date              date            N/A                   NULL           
  to_time              time            N/A                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: quiz_answer
----------------------------------------
CREATE TABLE `quiz_answer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `quiz_id` int(11) DEFAULT NULL,
  `attempt_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `answer_status` tinyint(1) DEFAULT NULL,
  `submitted_answer` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`submitted_answer`)),
  `correct_answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`correct_answers`)),
  `datetime` datetime DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1047 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  quiz_id              int             11                    NULL           
  attempt_id           int             11                    NULL           
  question_id          int             11                    NULL           
  answer_status        tinyint         1                     NULL           
  submitted_answer     longtext        N/A                   NULL           
  correct_answers      longtext        N/A                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: quiz_attempt
----------------------------------------
CREATE TABLE `quiz_attempt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `quiz_id` int(11) DEFAULT NULL,
  `attempt_count` int(11) DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_quiz_attempt_user_quiz` (`user_id`,`quiz_id`)
) ENGINE=InnoDB AUTO_INCREMENT=118 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  quiz_id              int             11                    NULL           
  attempt_count        int             11                    NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_quiz_attempt_user_quiz user_id         NON-UNIQUE
  idx_quiz_attempt_user_quiz quiz_id         NON-UNIQUE

================================================================================

TABLE: quiz_category
----------------------------------------
CREATE TABLE `quiz_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  status               int             11                    NULL           
  created_date         datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: quiz_questions
----------------------------------------
CREATE TABLE `quiz_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quiz_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  quiz_id              int             11                    NULL           
  question_id          int             11                    NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: rating
----------------------------------------
CREATE TABLE `rating` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `rating` double DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `ratable_id` int(11) DEFAULT NULL,
  `ratable_type` varchar(50) DEFAULT NULL,
  `date_added` int(11) DEFAULT NULL,
  `last_modified` int(11) DEFAULT NULL,
  `review` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  rating               double          N/A                   NULL           
  user_id              int             11                    NULL           
  ratable_id           int             11                    NULL           
  ratable_type         varchar         50                    NULL           
  date_added           int             11                    NULL           
  last_modified        int             11                    NULL           
  review               longtext        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: reels
----------------------------------------
CREATE TABLE `reels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `content` mediumtext DEFAULT NULL,
  `image` varchar(150) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `feed_type` varchar(15) DEFAULT NULL,
  `vimeo_url` text DEFAULT NULL,
  `video_url` text DEFAULT NULL,
  `html5_video_url` text DEFAULT NULL,
  `created_on` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_on` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         200                   NULL           
  content              mediumtext      N/A                   NULL           
  image                varchar         150                   NULL           
  course_id            int             11                    NULL           
  feed_type            varchar         15                    NULL           
  vimeo_url            text            N/A                   NULL           
  video_url            text            N/A                   NULL           
  html5_video_url      text            N/A                   NULL           
  created_on           datetime        N/A                   current_timestamp()
  updated_on           datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: refer_a_friend
----------------------------------------
CREATE TABLE `refer_a_friend` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         100                   NULL           
  phone                varchar         15                    NULL           
  user_id              int             11                    NULL           
  status               tinyint         1                     NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: role
----------------------------------------
CREATE TABLE `role` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `date_added` int(11) DEFAULT NULL,
  `last_modified` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  name                 varchar         255                   NULL           
  date_added           int             11                    NULL           
  last_modified        int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: salary_settings
----------------------------------------
CREATE TABLE `salary_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `instructor_salary` double DEFAULT NULL,
  `cre_salary` double DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  course_id            int             11                    NULL           
  instructor_salary    double          N/A                   NULL           
  cre_salary           double          N/A                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: section
----------------------------------------
CREATE TABLE `section` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `thumbnail` varchar(500) DEFAULT NULL,
  `background` varchar(500) DEFAULT NULL,
  `icon` varchar(500) DEFAULT NULL,
  `free` varchar(10) NOT NULL,
  `created_on` int(11) DEFAULT NULL,
  `updated_on` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         255                   NULL           
  course_id            int             11                    NULL           
  order                int             11                    NULL           
  thumbnail            varchar         500                   NULL           
  background           varchar         500                   NULL           
  icon                 varchar         500                   NULL           
  free                 varchar         10                    NULL           
  created_on           int             11                    NULL           
  updated_on           int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: settings
----------------------------------------
CREATE TABLE `settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) DEFAULT NULL,
  `value` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  key                  varchar         255                   NULL           
  value                longtext        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: skill_level
----------------------------------------
CREATE TABLE `skill_level` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(250) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         250                   NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: stories
----------------------------------------
CREATE TABLE `stories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `date` date NOT NULL,
  `image` varchar(150) DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  date                 date            N/A                   NULL           
  image                varchar         150                   NULL           
  status               tinyint         1                     NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: student_course_progress
----------------------------------------
CREATE TABLE `student_course_progress` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `total_modules` int(11) DEFAULT NULL,
  `completed_modules` int(11) DEFAULT NULL,
  `course_progress` double DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_student_course_progress_user_course` (`user_id`,`course_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2433 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  course_id            int             11                    NULL           
  total_modules        int             11                    NULL           
  completed_modules    int             11                    NULL           
  course_progress      double          N/A                   NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_student_course_progress_user_course user_id         NON-UNIQUE
  idx_student_course_progress_user_course course_id       NON-UNIQUE

================================================================================

TABLE: student_upload
----------------------------------------
CREATE TABLE `student_upload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `course_id` int(11) DEFAULT NULL,
  `user_type_id` int(11) DEFAULT NULL,
  `title` mediumtext DEFAULT NULL,
  `title_file` varchar(250) DEFAULT NULL,
  `hint` mediumtext DEFAULT NULL,
  `hint_file` varchar(200) DEFAULT NULL,
  `solution` mediumtext DEFAULT NULL,
  `solution_file` varchar(200) DEFAULT NULL,
  `is_equation` tinyint(1) DEFAULT NULL,
  `is_equation_solution` tinyint(1) DEFAULT NULL,
  `number_of_options` tinyint(4) DEFAULT NULL,
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`options`)),
  `correct_answers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`correct_answers`)),
  `order` int(11) DEFAULT NULL,
  `q_type` tinyint(4) DEFAULT NULL COMMENT '1=text, 2=image',
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  course_id            int             11                    NULL           
  user_type_id         int             11                    NULL           
  title                mediumtext      N/A                   NULL           
  title_file           varchar         250                   NULL           
  hint                 mediumtext      N/A                   NULL           
  hint_file            varchar         200                   NULL           
  solution             mediumtext      N/A                   NULL           
  solution_file        varchar         200                   NULL           
  is_equation          tinyint         1                     NULL           
  is_equation_solution tinyint         1                     NULL           
  number_of_options    tinyint         4                     NULL           
  options              longtext        N/A                   NULL           
  correct_answers      longtext        N/A                   NULL           
  order                int             11                    NULL           
  q_type               tinyint         4                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  deleted              tinyint         1                     NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: support_chat
----------------------------------------
CREATE TABLE `support_chat` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `sender_id` int(11) DEFAULT NULL,
  `sender_to` int(11) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  sender_id            int             11                    NULL           
  sender_to            int             11                    NULL           
  message              text            N/A                   NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  deleted_by           int             11                    NULL           
  created_at           datetime        N/A                   NULL           
  updated_at           datetime        N/A                   NULL           
  deleted_at           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: tag
----------------------------------------
CREATE TABLE `tag` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `tag` varchar(255) DEFAULT NULL,
  `tagable_id` int(11) DEFAULT NULL,
  `tagable_type` varchar(255) DEFAULT NULL,
  `date_added` int(11) DEFAULT NULL,
  `last_modified` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  tag                  varchar         255                   NULL           
  tagable_id           int             11                    NULL           
  tagable_type         varchar         255                   NULL           
  date_added           int             11                    NULL           
  last_modified        int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: tasks
----------------------------------------
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) DEFAULT NULL,
  `task_type` tinyint(4) DEFAULT NULL COMMENT '1=reading, 2=writing, 3=listening, 4=speaking',
  `title` varchar(200) DEFAULT NULL,
  `file_question` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file_question`)),
  `file_answer` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file_answer`)),
  `file_audio` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file_audio`)),
  `order` int(11) NOT NULL DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_id            int             11                    NULL           
  task_type            tinyint         4                     NULL           
  title                varchar         200                   NULL           
  file_question        longtext        N/A                   NULL           
  file_answer          longtext        N/A                   NULL           
  file_audio           longtext        N/A                   NULL           
  order                int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: test
----------------------------------------
CREATE TABLE `test` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `summary` mediumtext DEFAULT NULL,
  `duration` time DEFAULT NULL,
  `from_date` date DEFAULT NULL,
  `from_time` time DEFAULT NULL,
  `to_date` date DEFAULT NULL,
  `to_time` time DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL,
  `image` varchar(150) DEFAULT NULL,
  `free` varchar(5) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  course_id            int             11                    NULL           
  summary              mediumtext      N/A                   NULL           
  duration             time            N/A                   NULL           
  from_date            date            N/A                   NULL           
  from_time            time            N/A                   NULL           
  to_date              date            N/A                   NULL           
  to_time              time            N/A                   NULL           
  status               tinyint         1                     NULL           
  image                varchar         150                   NULL           
  free                 varchar         5                     NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: test_answer
----------------------------------------
CREATE TABLE `test_answer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `test_id` int(11) DEFAULT NULL,
  `attempt_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `answer_correct` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`answer_correct`)),
  `answer_submitted` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`answer_submitted`)),
  `answer_status` tinyint(4) DEFAULT NULL COMMENT '1=correct, 2=incorrect, 3=skip ',
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  test_id              int             11                    NULL           
  attempt_id           int             11                    NULL           
  question_id          int             11                    NULL           
  answer_correct       longtext        N/A                   NULL           
  answer_submitted     longtext        N/A                   NULL           
  answer_status        tinyint         4                     NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: test_attempt
----------------------------------------
CREATE TABLE `test_attempt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `test_id` int(11) DEFAULT NULL,
  `question_no` int(11) DEFAULT NULL,
  `question_id` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`question_id`)),
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `time_taken` int(11) DEFAULT NULL COMMENT 'minutes',
  `correct` int(11) DEFAULT NULL,
  `incorrect` int(11) DEFAULT NULL,
  `skip` int(11) DEFAULT NULL,
  `score` int(11) DEFAULT NULL,
  `submit_status` tinyint(1) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  test_id              int             11                    NULL           
  question_no          int             11                    NULL           
  question_id          longtext        N/A                   NULL           
  start_time           datetime        N/A                   NULL           
  end_time             datetime        N/A                   NULL           
  time_taken           int             11                    NULL           
  correct              int             11                    NULL           
  incorrect            int             11                    NULL           
  skip                 int             11                    NULL           
  score                int             11                    NULL           
  submit_status        tinyint         1                     NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: test_questions
----------------------------------------
CREATE TABLE `test_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `test_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `test_id` (`test_id`,`question_id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  test_id              int             11                    NULL           
  question_id          int             11                    NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    
  test_id              test_id         UNIQUE    
  test_id              question_id     UNIQUE    

================================================================================

TABLE: testimonial
----------------------------------------
CREATE TABLE `testimonial` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `designation` varchar(200) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `photo` varchar(150) DEFAULT NULL,
  `video_url` varchar(150) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `review` text DEFAULT NULL,
  `rating` varchar(10) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  name                 varchar         100                   NULL           
  designation          varchar         200                   NULL           
  course_id            int             11                    NULL           
  photo                varchar         150                   NULL           
  video_url            varchar         150                   NULL           
  date                 date            N/A                   NULL           
  review               text            N/A                   NULL           
  rating               varchar         10                    NULL           
  order                int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: timetable
----------------------------------------
CREATE TABLE `timetable` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `section_id` int(11) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `exam_time` datetime DEFAULT NULL,
  `course_type` int(11) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  section_id           int             11                    NULL           
  course_id            int             11                    NULL           
  exam_time            datetime        N/A                   NULL           
  course_type          int             11                    NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: topic
----------------------------------------
CREATE TABLE `topic` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `section_id` int(11) DEFAULT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `thumbnail` varchar(500) DEFAULT NULL,
  `background` varchar(500) DEFAULT NULL,
  `icon` varchar(500) DEFAULT NULL,
  `free` varchar(10) NOT NULL,
  `created_on` int(11) DEFAULT NULL,
  `updated_on` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         255                   NULL           
  section_id           int             11                    NULL           
  order                int             11                    NULL           
  thumbnail            varchar         500                   NULL           
  background           varchar         500                   NULL           
  icon                 varchar         500                   NULL           
  free                 varchar         10                    NULL           
  created_on           int             11                    NULL           
  updated_on           int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: upcoming_live_class
----------------------------------------
CREATE TABLE `upcoming_live_class` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(300) NOT NULL,
  `course_id` varchar(300) NOT NULL,
  `subject_id` varchar(300) NOT NULL,
  `chapter_id` varchar(300) NOT NULL,
  `video_type` varchar(300) NOT NULL,
  `video_url` mediumtext NOT NULL,
  `item_bg` varchar(500) NOT NULL,
  `teacher` mediumtext NOT NULL,
  `from_date` date NOT NULL,
  `from_time` time NOT NULL,
  `to_date` date NOT NULL,
  `to_time` time NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         300                   NULL           
  course_id            varchar         300                   NULL           
  subject_id           varchar         300                   NULL           
  chapter_id           varchar         300                   NULL           
  video_type           varchar         300                   NULL           
  video_url            mediumtext      N/A                   NULL           
  item_bg              varchar         500                   NULL           
  teacher              mediumtext      N/A                   NULL           
  from_date            date            N/A                   NULL           
  from_time            time            N/A                   NULL           
  to_date              date            N/A                   NULL           
  to_time              time            N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_activity
----------------------------------------
CREATE TABLE `user_activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `lesson_id` int(11) DEFAULT NULL,
  `activity_id` int(11) DEFAULT NULL,
  `activity_type` varchar(250) DEFAULT NULL,
  `file` varchar(200) DEFAULT NULL,
  `title` varchar(250) DEFAULT NULL,
  `description` mediumtext DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_activity_user_activity` (`user_id`,`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=436 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  lesson_id            int             11                    NULL           
  activity_id          int             11                    NULL           
  activity_type        varchar         250                   NULL           
  file                 varchar         200                   NULL           
  title                varchar         250                   NULL           
  description          mediumtext      N/A                   NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_user_activity_user_activity user_id         NON-UNIQUE
  idx_user_activity_user_activity activity_id     NON-UNIQUE

================================================================================

TABLE: user_file
----------------------------------------
CREATE TABLE `user_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `assignment_id` int(11) NOT NULL,
  `file` varchar(1000) NOT NULL,
  `added_date` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  assignment_id        int             11                    NULL           
  file                 varchar         1000                  NULL           
  added_date           timestamp       N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_goals
----------------------------------------
CREATE TABLE `user_goals` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(260) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `habit_category_id` int(11) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `time_period` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         260                   NULL           
  user_id              int             11                    NULL           
  habit_category_id    int             11                    NULL           
  description          text            N/A                   NULL           
  time_period          int             11                    NULL           
  start_date           date            N/A                   NULL           
  end_date             date            N/A                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_goals_activity
----------------------------------------
CREATE TABLE `user_goals_activity` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `goal_id` int(11) DEFAULT NULL,
  `lesson_file_id` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_goals_activity_user_lesson` (`user_id`,`lesson_file_id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  date                 date            N/A                   NULL           
  goal_id              int             11                    NULL           
  lesson_file_id       int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_user_goals_activity_user_lesson user_id         NON-UNIQUE
  idx_user_goals_activity_user_lesson lesson_file_id  NON-UNIQUE

================================================================================

TABLE: user_lecture_note
----------------------------------------
CREATE TABLE `user_lecture_note` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `video_id` int(11) DEFAULT NULL,
  `note_index` time DEFAULT NULL,
  `note_text` varchar(400) DEFAULT NULL,
  `note_url` varchar(150) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  video_id             int             11                    NULL           
  note_index           time            N/A                   NULL           
  note_text            varchar         400                   NULL           
  note_url             varchar         150                   NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_lesson
----------------------------------------
CREATE TABLE `user_lesson` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_completed` tinyint(1) DEFAULT NULL,
  `user_completed_at` datetime DEFAULT NULL,
  `instructor_id` int(11) DEFAULT NULL,
  `instructor_completed` tinyint(4) DEFAULT NULL,
  `instructor_completed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_id            int             11                    NULL           
  user_id              int             11                    NULL           
  user_completed       tinyint         1                     NULL           
  user_completed_at    datetime        N/A                   NULL           
  instructor_id        int             11                    NULL           
  instructor_completed tinyint         4                     NULL           
  instructor_completed_at datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_log
----------------------------------------
CREATE TABLE `user_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `request_url` varchar(1000) DEFAULT NULL,
  `request_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`request_json`)),
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   bigint          20         PRIMARY    NULL           
  user_id              int             11                    NULL           
  request_url          varchar         1000                  NULL           
  request_json         longtext        N/A                   NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_points
----------------------------------------
CREATE TABLE `user_points` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `type_id` int(11) DEFAULT NULL COMMENT 'based on point_settings table',
  `item_id` int(11) DEFAULT NULL,
  `points` bigint(20) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_points_user_id_id` (`user_id`,`id` DESC),
  KEY `idx_user_points_user_id_expiry` (`user_id`,`expiry_date`)
) ENGINE=InnoDB AUTO_INCREMENT=28906 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  type_id              int             11                    NULL           
  item_id              int             11                    NULL           
  points               bigint          20                    NULL           
  date                 date            N/A                   NULL           
  expiry_date          date            N/A                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_user_points_user_id_id user_id         NON-UNIQUE
  idx_user_points_user_id_id id              NON-UNIQUE
  idx_user_points_user_id_expiry user_id         NON-UNIQUE
  idx_user_points_user_id_expiry expiry_date     NON-UNIQUE

================================================================================

TABLE: user_queries
----------------------------------------
CREATE TABLE `user_queries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `category` varchar(255) DEFAULT NULL,
  `query` varchar(1000) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  category             varchar         255                   NULL           
  query                varchar         1000                  NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_task_uploads
----------------------------------------
CREATE TABLE `user_task_uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `task_id` int(11) DEFAULT NULL,
  `file` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`file`)),
  `remarks` mediumtext DEFAULT NULL,
  `datetime` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  task_id              int             11                    NULL           
  file                 longtext        N/A                   NULL           
  remarks              mediumtext      N/A                   NULL           
  datetime             datetime        N/A                   current_timestamp()

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: user_type
----------------------------------------
CREATE TABLE `user_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(150) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         150                   NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: users
----------------------------------------
CREATE TABLE `users` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `phone` varchar(15) NOT NULL,
  `country_code` varchar(10) DEFAULT NULL,
  `phone_full` varchar(50) DEFAULT NULL,
  `user_email` varchar(50) DEFAULT NULL,
  `gender` varchar(60) DEFAULT NULL,
  `user_type` int(11) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `whatsapp` varchar(20) DEFAULT NULL,
  `username` varchar(150) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `qualification` varchar(150) DEFAULT NULL,
  `role_id` int(11) DEFAULT NULL,
  `verification_code` longtext DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `validity` int(11) DEFAULT NULL COMMENT '0 = reg not complete,\r\n1 = reg completed',
  `device_id` varchar(1000) DEFAULT NULL,
  `pin_code` varchar(10) DEFAULT NULL,
  `place` varchar(1000) DEFAULT NULL,
  `course_id` int(11) DEFAULT NULL,
  `category_id` int(11) DEFAULT NULL,
  `course_type` int(11) DEFAULT NULL,
  `cre_id` int(11) DEFAULT NULL,
  `lead_status` int(11) DEFAULT NULL,
  `lead_source` int(11) DEFAULT NULL,
  `lead_note` varchar(150) DEFAULT NULL,
  `followup_date` date DEFAULT NULL,
  `instructor` int(11) DEFAULT 0,
  `notification_token` varchar(500) DEFAULT NULL,
  `premium` int(11) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  `referred_by` int(11) DEFAULT 0,
  `dynamic_link` varchar(100) NOT NULL,
  `upload_id` int(11) DEFAULT NULL,
  `biography` longtext DEFAULT NULL,
  `zoom_id` varchar(150) DEFAULT NULL,
  `zoom_password` varchar(150) DEFAULT NULL,
  `sound_status` tinyint(1) DEFAULT NULL,
  `created_by` int(11) DEFAULT 0,
  `updated_by` int(11) DEFAULT 0,
  `created_on` datetime DEFAULT NULL,
  `updated_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9238 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             10         PRIMARY    NULL           
  name                 varchar         255                   NULL           
  phone                varchar         15                    NULL           
  country_code         varchar         10                    NULL           
  phone_full           varchar         50                    NULL           
  user_email           varchar         50                    NULL           
  gender               varchar         60                    NULL           
  user_type            int             11                    NULL           
  email                varchar         50                    NULL           
  whatsapp             varchar         20                    NULL           
  username             varchar         150                   NULL           
  password             varchar         255                   NULL           
  qualification        varchar         150                   NULL           
  role_id              int             11                    NULL           
  verification_code    longtext        N/A                   NULL           
  status               int             11                    NULL           
  validity             int             11                    NULL           
  device_id            varchar         1000                  NULL           
  pin_code             varchar         10                    NULL           
  place                varchar         1000                  NULL           
  course_id            int             11                    NULL           
  category_id          int             11                    NULL           
  course_type          int             11                    NULL           
  cre_id               int             11                    NULL           
  lead_status          int             11                    NULL           
  lead_source          int             11                    NULL           
  lead_note            varchar         150                   NULL           
  followup_date        date            N/A                   NULL           
  instructor           int             11                    NULL           
  notification_token   varchar         500                   NULL           
  premium              int             11                    NULL           
  datetime             datetime        N/A                   NULL           
  referred_by          int             11                    NULL           
  dynamic_link         varchar         100                   NULL           
  upload_id            int             11                    NULL           
  biography            longtext        N/A                   NULL           
  zoom_id              varchar         150                   NULL           
  zoom_password        varchar         150                   NULL           
  sound_status         tinyint         1                     NULL           
  created_by           int             11                    NULL           
  updated_by           int             11                    NULL           
  created_on           datetime        N/A                   NULL           
  updated_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: video_banner
----------------------------------------
CREATE TABLE `video_banner` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `image` varchar(150) DEFAULT NULL,
  `link` varchar(150) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  image                varchar         150                   NULL           
  link                 varchar         150                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: video_index
----------------------------------------
CREATE TABLE `video_index` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `video_id` int(11) DEFAULT NULL,
  `index` time DEFAULT NULL,
  `thumbnail` varchar(150) DEFAULT NULL,
  `description` varchar(400) DEFAULT NULL,
  `datetime` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  video_id             int             11                    NULL           
  index                time            N/A                   NULL           
  thumbnail            varchar         150                   NULL           
  description          varchar         400                   NULL           
  datetime             datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: video_progress_status
----------------------------------------
CREATE TABLE `video_progress_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `lesson_file_id` int(11) DEFAULT NULL,
  `total_duration` time DEFAULT NULL,
  `user_progress` time DEFAULT NULL,
  `status` int(11) DEFAULT NULL COMMENT '0= in_completed, 1= completed	',
  `create_date` datetime DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_video_progress_status_user_file_status` (`user_id`,`lesson_file_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=13626 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  user_id              int             11                    NULL           
  lesson_file_id       int             11                    NULL           
  total_duration       time            N/A                   NULL           
  user_progress        time            N/A                   NULL           
  status               int             11                    NULL           
  create_date          datetime        N/A                   NULL           
  update_date          datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    
  idx_video_progress_status_user_file_status user_id         NON-UNIQUE
  idx_video_progress_status_user_file_status lesson_file_id  NON-UNIQUE
  idx_video_progress_status_user_file_status status          NON-UNIQUE

================================================================================

TABLE: vimeo_videolinks
----------------------------------------
CREATE TABLE `vimeo_videolinks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_file_id` int(11) DEFAULT NULL,
  `quality` text DEFAULT NULL,
  `rendition` text DEFAULT NULL,
  `height` bigint(20) DEFAULT NULL,
  `width` text DEFAULT NULL,
  `type` text DEFAULT NULL,
  `link` text DEFAULT NULL,
  `fps` text DEFAULT NULL,
  `size` text DEFAULT NULL,
  `public_name` text DEFAULT NULL,
  `size_short` text DEFAULT NULL,
  `download_link` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7041 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_file_id       int             11                    NULL           
  quality              text            N/A                   NULL           
  rendition            text            N/A                   NULL           
  height               bigint          20                    NULL           
  width                text            N/A                   NULL           
  type                 text            N/A                   NULL           
  link                 text            N/A                   NULL           
  fps                  text            N/A                   NULL           
  size                 text            N/A                   NULL           
  public_name          text            N/A                   NULL           
  size_short           text            N/A                   NULL           
  download_link        text            N/A                   NULL           
  created_at           datetime        N/A                   NULL           
  updated_at           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: vimeo_videolinks_old
----------------------------------------
CREATE TABLE `vimeo_videolinks_old` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lesson_file_id` int(11) DEFAULT NULL,
  `quality` text DEFAULT NULL,
  `rendition` text DEFAULT NULL,
  `height` bigint(20) DEFAULT NULL,
  `width` text DEFAULT NULL,
  `type` text DEFAULT NULL,
  `link` text DEFAULT NULL,
  `fps` text DEFAULT NULL,
  `size` text DEFAULT NULL,
  `public_name` text DEFAULT NULL,
  `size_short` text DEFAULT NULL,
  `download_link` text DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2201 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  lesson_file_id       int             11                    NULL           
  quality              text            N/A                   NULL           
  rendition            text            N/A                   NULL           
  height               bigint          20                    NULL           
  width                text            N/A                   NULL           
  type                 text            N/A                   NULL           
  link                 text            N/A                   NULL           
  fps                  text            N/A                   NULL           
  size                 text            N/A                   NULL           
  public_name          text            N/A                   NULL           
  size_short           text            N/A                   NULL           
  download_link        text            N/A                   NULL           
  created_at           datetime        N/A                   NULL           
  updated_at           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: wallet_expiry
----------------------------------------
CREATE TABLE `wallet_expiry` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(255) DEFAULT NULL,
  `value` longtext DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  key                  varchar         255                   NULL           
  value                longtext        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: zoom
----------------------------------------
CREATE TABLE `zoom` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(1000) NOT NULL,
  `category_id` varchar(250) DEFAULT NULL,
  `course_id` int(11) NOT NULL,
  `lesson_id` int(11) DEFAULT NULL,
  `live_type` int(11) DEFAULT NULL COMMENT '1 = course live, 2 = one to one live',
  `student_id` int(11) DEFAULT NULL,
  `package_id` int(11) DEFAULT NULL,
  `zoom_id` varchar(100) DEFAULT NULL,
  `password` varchar(100) DEFAULT NULL,
  `fromTime` time NOT NULL,
  `toTime` time NOT NULL,
  `status` varchar(100) NOT NULL,
  `fromDate` date NOT NULL,
  `toDate` date NOT NULL,
  `role_id` tinyint(4) DEFAULT NULL COMMENT '2= for students, 3= for instructors',
  `created_by` int(11) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  title                varchar         1000                  NULL           
  category_id          varchar         250                   NULL           
  course_id            int             11                    NULL           
  lesson_id            int             11                    NULL           
  live_type            int             11                    NULL           
  student_id           int             11                    NULL           
  package_id           int             11                    NULL           
  zoom_id              varchar         100                   NULL           
  password             varchar         100                   NULL           
  fromTime             time            N/A                   NULL           
  toTime               time            N/A                   NULL           
  status               varchar         100                   NULL           
  fromDate             date            N/A                   NULL           
  toDate               date            N/A                   NULL           
  role_id              tinyint         4                     NULL           
  created_by           int             11                    NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

TABLE: zoom_settings
----------------------------------------
CREATE TABLE `zoom_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `zak_token` varchar(400) DEFAULT NULL,
  `expire_date` datetime DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

FIELD DETAILS:
  id                   int             11         PRIMARY    NULL           
  zak_token            varchar         400                   NULL           
  expire_date          datetime        N/A                   NULL           
  created_on           datetime        N/A                   NULL           

INDEXES:
  PRIMARY              id              UNIQUE    

================================================================================

