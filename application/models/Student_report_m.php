<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Student_report_m extends MY_Model {

    /*
        | -----------------------------------------------------
        | PRODUCT NAME: 	TUTORPRO
        | -----------------------------------------------------
        | AUTHOR:			TROGON MEDIA PVT LTD
        | -----------------------------------------------------
        | EMAIL:			<EMAIL>
        | -----------------------------------------------------
        | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
        | -----------------------------------------------------
        | WEBSITE:			http://trogonmedia.com
        | -----------------------------------------------------
        */

    protected $_table_name = 'enrol';
    
    public function __construct()
    {
        parent::__construct();
        $this->load->model('lesson_files_m');
        $this->load->model('user_task_upload_m');
    }

    /**
     * Get all courses for dropdown
     */
    public function get_courses() {
        $this->db->select('id, title');
        $this->db->where('status', 'active');
        $this->db->order_by('title', 'ASC');
        return $this->db->get('course')->result_array();
    }

    /**
     * Get students enrolled in a specific course with their premium status and completion percentage
     */
    public function get_course_students($course_id) {
        $this->db->select('
            e.id as enrollment_id,
            e.user_id,
            e.course_id,
            e.premium,
            e.created_on as enrollment_date,
            u.name,
            u.email,
            u.phone,
            c.title as course_title
        ');
        $this->db->from('enrol e');
        $this->db->join('users u', 'e.user_id = u.id', 'left');
        $this->db->join('course c', 'e.course_id = c.id', 'left');
        $this->db->where('e.course_id', $course_id);
        $this->db->where('u.role_id', 2); // Only students
        $this->db->order_by('u.name', 'ASC');
        
        $students = $this->db->get()->result_array();
        
        // Calculate completion percentage for each student and check premium status
        foreach ($students as &$student) {
            $student['completion_percentage'] = $this->calculate_course_completion_percentage($student['user_id'], $course_id);
            // Check actual premium status from payment_info if not premium in enrol table
            if (!$student['premium']) {
                $student['premium'] = $this->check_premium_status_from_payments($student['user_id']);
            }
        }
        
        return $students;
    }

    /**
     * Calculate course completion percentage for a student
     */
    public function calculate_course_completion_percentage($user_id, $course_id) {
        // Get total lessons in the course
        $this->db->where('course_id', $course_id);
        $total_lessons = $this->db->get('lesson')->num_rows();
        
        if ($total_lessons == 0) {
            return 0;
        }
        
        // Get completed lessons
        $completed_lessons = 0;
        $this->db->select('id');
        $this->db->where('course_id', $course_id);
        $lessons = $this->db->get('lesson')->result_array();
        
        foreach ($lessons as $lesson) {
            if ($this->is_lesson_completed($user_id, $lesson['id'])) {
                $completed_lessons++;
            }
        }
        
        return round(($completed_lessons / $total_lessons) * 100, 2);
    }

    /**
     * Check if a lesson is completed by a student
     */
    private function is_lesson_completed($user_id, $lesson_id) {
        // Get all lesson files for this lesson
        $this->db->where('lesson_id', $lesson_id);
        $lesson_files = $this->db->get('lesson_files')->result_array();
        
        if (empty($lesson_files)) {
            return false;
        }
        
        $total_files = count($lesson_files);
        $completed_files = 0;
        
        foreach ($lesson_files as $file) {
            if ($this->check_file_completion_status($user_id, $file['id'], $file['attachment_type'])) {
                $completed_files++;
            }
        }
        
        // Lesson is completed if all files are completed
        return $completed_files == $total_files;
    }

    /**
     * Get detailed student report for a specific course
     */
    public function get_student_detailed_report($user_id, $course_id) {
        // Get student info
        $this->db->select('name, email, phone');
        $this->db->where('id', $user_id);
        $student = $this->db->get('users')->row_array();
        
        // Get course info
        $this->db->select('title');
        $this->db->where('id', $course_id);
        $course = $this->db->get('course')->row_array();
        
        // Get enrollment info
        $this->db->select('premium, created_on');
        $this->db->where('user_id', $user_id);
        $this->db->where('course_id', $course_id);
        $enrollment = $this->db->get('enrol')->row_array();

        // Check premium status from payments if not premium in enrollment
        if (!$enrollment['premium']) {
            $enrollment['premium'] = $this->check_premium_status_from_payments($user_id);
        }
        
        // Get lessons with completion status
        $this->db->select('id, title, summary, order');
        $this->db->where('course_id', $course_id);
        $this->db->order_by('order', 'ASC');
        $lessons = $this->db->get('lesson')->result_array();
        
        $total_lessons = count($lessons);
        $completed_lessons = 0;
        $total_lesson_videos = 0;
        $completed_lesson_videos = 0;
        
        foreach ($lessons as &$lesson) {
            // Get lesson files
            $this->db->select('id, title, attachment_type, lesson_type, video_url, order');
            $this->db->where('lesson_id', $lesson['id']);
            $this->db->order_by('order', 'ASC');
            $lesson_files = $this->db->get('lesson_files')->result_array();
            
            $lesson_completed_files = 0;
            $lesson_total_files = count($lesson_files);
            
            foreach ($lesson_files as &$file) {
                $is_completed = $this->check_file_completion_status($user_id, $file['id'], $file['attachment_type']);
                $file['is_completed'] = $is_completed;
                
                // Count videos
                if ($file['attachment_type'] == 'url') {
                    $total_lesson_videos++;
                    if ($is_completed) {
                        $completed_lesson_videos++;
                    }
                }
                
                if ($is_completed) {
                    $lesson_completed_files++;
                }
                
                // Get activity link if student has uploaded any activity
                $file['activity_link'] = $this->get_student_activity_link($user_id, $file['id'], $file['attachment_type']);
            }
            
            $lesson['lesson_files'] = $lesson_files;
            $lesson['is_completed'] = ($lesson_total_files > 0 && $lesson_completed_files == $lesson_total_files);
            $lesson['completion_percentage'] = $lesson_total_files > 0 ? round(($lesson_completed_files / $lesson_total_files) * 100, 2) : 0;
            
            if ($lesson['is_completed']) {
                $completed_lessons++;
            }
        }
        
        return [
            'student' => $student,
            'course' => $course,
            'enrollment' => $enrollment,
            'summary' => [
                'total_lessons' => $total_lessons,
                'completed_lessons' => $completed_lessons,
                'total_lesson_videos' => $total_lesson_videos,
                'completed_lesson_videos' => $completed_lesson_videos,
                'course_completion_percentage' => $total_lessons > 0 ? round(($completed_lessons / $total_lessons) * 100, 2) : 0
            ],
            'lessons' => $lessons
        ];
    }

    /**
     * Get student activity link if they have uploaded any activity for a lesson file
     */
    private function get_student_activity_link($user_id, $lesson_file_id, $attachment_type) {
        $activity_link = null;
        
        switch ($attachment_type) {
            case 'activity':
                // Check user_activity table
                $this->db->select('id, file, title, description');
                $this->db->where('user_id', $user_id);
                $this->db->where('activity_id', $lesson_file_id);
                $activity = $this->db->get('user_activity')->row_array();
                
                if ($activity) {
                    $activity_link = [
                        'type' => 'activity',
                        'url' => site_url('admin/student_report/view_activity/' . $activity['id']),
                        'title' => 'View Activity: ' . $activity['title']
                    ];
                }
                break;
                
            default:
                // Check for task uploads related to this lesson file
                $this->db->select('t.id as task_id, t.title, ut.id as upload_id');
                $this->db->from('tasks t');
                $this->db->join('user_task_uploads ut', 't.id = ut.task_id', 'left');
                $this->db->join('lesson_files lf', 't.lesson_id = lf.lesson_id', 'inner');
                $this->db->where('lf.id', $lesson_file_id);
                $this->db->where('ut.user_id', $user_id);
                $task_upload = $this->db->get()->row_array();
                
                if ($task_upload && $task_upload['upload_id']) {
                    $activity_link = [
                        'type' => 'task',
                        'url' => site_url('admin/student_report/view_task_upload/' . $task_upload['upload_id']),
                        'title' => 'View Task: ' . $task_upload['title']
                    ];
                }
                break;
        }
        
        return $activity_link;
    }

    /**
     * Check completion status for a specific lesson file
     * Based on the logic from lesson_files_m->get_completion_status
     */
    private function check_file_completion_status($user_id, $lesson_file_id, $attachment_type) {
        $completion_status = 0;

        switch ($attachment_type) {
            case 'url':
                $completed = $this->db->get_where('video_progress_status', [
                    'user_id' => $user_id,
                    'status' => 1,
                    'lesson_file_id' => $lesson_file_id
                ])->num_rows();
                $completion_status = $completed > 0 ? 1 : 0;
                break;

            case 'audio':
            case 'notes':
            case 'pdf':
                $completed = $this->db->get_where('material_progress_status', [
                    'user_id' => $user_id,
                    'lesson_file_id' => $lesson_file_id
                ])->num_rows();
                $completion_status = $completed > 0 ? 1 : 0;
                break;

            case 'activity':
                $completed = $this->db->get_where('user_activity', [
                    'user_id' => $user_id,
                    'activity_id' => $lesson_file_id
                ])->num_rows();
                $completion_status = $completed > 0 ? 1 : 0;
                break;

            case 'exam':
            case 'practice':
                $completed = $this->db->get_where('quiz_attempt', [
                    'user_id' => $user_id,
                    'quiz_id' => $lesson_file_id
                ])->num_rows();
                $completion_status = $completed > 0 ? 1 : 0;
                break;

            case 'live':
                $completed = $this->db->get_where('liveclass_attendance', [
                    'user_id' => $user_id,
                    'zoom_id' => $lesson_file_id
                ])->num_rows();
                $completion_status = $completed > 0 ? 1 : 0;
                break;

            case 'habit':
                $completed = $this->db->get_where('user_goals_activity', [
                    'user_id' => $user_id,
                    'lesson_file_id' => $lesson_file_id
                ])->num_rows();
                $completion_status = $completed > 0 ? 1 : 0;
                break;

            default:
                $completion_status = 0;
                break;
        }

        return $completion_status;
    }

    /**
     * Check premium status from payment_info table
     */
    public function check_premium_status_from_payments($user_id) {
        // Check if user has any active payments
        $this->db->select('id');
        $this->db->where('user_id', $user_id);
        $this->db->where('expiry_date >=', date('Y-m-d'));
        $active_payment = $this->db->get('payment_info')->num_rows();

        return $active_payment > 0 ? 1 : 0;
    }

    /**
     * Get display type for lesson file (show 'video' instead of 'url' for video files)
     */
    public function get_file_display_type($attachment_type, $lesson_type = null) {
        if ($attachment_type == 'url' && $lesson_type == 'video') {
            return 'video';
        }
        return $attachment_type;
    }
}
