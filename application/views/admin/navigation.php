
<!-- ========== Left Sidebar Start ========== -->
<div class="left-side-menu left-side-menu-detached">
    <div class="leftbar-user">
        <a href="javascript: void(0);">
            <img src="<?=get_user_image_url()?>" alt="user-image" height="42" class="rounded-circle shadow-sm">
            <span class="leftbar-user-name"><?=$this->session->userdata('first_name').' '.$this->session->userdata('first_name')?></span>
        </a>
    </div>

    <!--- Sidemenu -->
    <ul class="metismenu side-nav side-nav-light">

        <li class="side-nav-title side-nav-item text-info"><?php echo get_phrase('Academic'); ?></li>
        <li class="side-nav-item <?php if ($page_name == 'dashboard')echo 'active';?>">
            <a href="<?php echo site_url('admin/dashboard'); ?>" class="side-nav-link">
                <i class="dripicons-view-apps"></i>
                <span><?php echo get_phrase('dashboard'); ?></span>
            </a>
        </li>

        <?php
            if(has_permission('category', 'academic')){
        ?>
            <li class="side-nav-item <?php if ($page_name == 'category/index' || $page_name == 'category/add' || $page_name == 'category/edit' ): ?> active <?php endif; ?> ">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'category/index' || $page_name == 'category/add' || $page_name == 'category/edit' ): ?> active <?php endif; ?>">
                    <i class="dripicons-network-1"></i>
                    <span> <?php echo get_phrase('course_categories'); ?> </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    <li class = "<?php if($page_name == 'categories' || $page_name == 'category_edit') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/category/index'); ?>"><?php echo get_phrase('categories'); ?></a>
                    </li>

                    <li class = "<?php if($page_name == 'category_add') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/category/add'); ?>"><?php echo get_phrase('add_new_category'); ?></a>
                    </li>
                </ul>
            </li>
        <?php
        }
        ?>

        <?php
        if(has_permission('course', 'academic')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/course/index/'); ?>" class="side-nav-link <?php if ($page_name == 'course/index' || $page_name == 'course/add' || $page_name == 'course/edit')echo 'active';?>">
                    <i class="dripicons-archive"></i>
                    <span><?php echo get_phrase('courses'); ?></span>
                </a>
            </li>
            
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/course_exam/index/'); ?>" class="side-nav-link <?php if ($page_name == 'course_exam/index' || $page_name == 'course_exam/add' || $page_name == 'course_exam/edit')echo 'active';?>">
                    <i class="dripicons-archive"></i>
                    <span><?php echo get_phrase('course_exam'); ?></span>
                </a>
            </li>
        <?php
        }
        ?>
        
        <?php
            if(has_permission('course', 'academic')){
        ?>
            <li class="side-nav-item <?php if ($page_name == 'points_settings/global_settings'): ?> active <?php endif; ?> ">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'points_settings/global_settings' || $page_name == 'points_settings/module_wise_settings'): ?> active <?php endif; ?>">
                    <i class="dripicons-network-1"></i>
                    <span> <?php echo get_phrase('Points Settings'); ?> </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    
                    <li class = "<?php if($page_name == 'points_settings/global_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/points_settings/global_settings'); ?>"><?php echo get_phrase('global__points_settings'); ?></a>
                    </li>
                    
                    <li class = "<?php if($page_name == 'points_settings/wallet_expiry_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/points_settings/wallet_expiry_settings'); ?>"><?php echo get_phrase('wallet_expiry_settings'); ?></a>
                    </li>

                    <li class = "<?php if($page_name == 'points_settings/module_wise_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/points_settings/module_wise_settings'); ?>"><?php echo get_phrase('Module wise points'); ?></a>
                    </li>
                </ul>
            </li>
        <?php
            }
             if (has_permission('habit')){
        ?>
        
        <li class="side-nav-item <?php if ($page_name == 'habit/index'): ?> active <?php endif; ?> ">
            <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'habits/index' || $page_name == 'habit_category/index'): ?> active <?php endif; ?>">
                <i class="dripicons-network-1"></i>
                <span> <?php echo get_phrase('Habits'); ?> </span>
                <span class="menu-arrow"></span>
            </a>
            <ul class="side-nav-second-level" aria-expanded="false">
                
                <li class = "<?php if($page_name == 'habit_category/index') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/habit_category/index'); ?>"><?php echo get_phrase('Habit Category'); ?></a>
                </li>
                
                <li class = "<?php if($page_name == 'habit/index') echo 'active'; ?>">
                    <a href="<?php echo site_url('admin/habit/index'); ?>"><?php echo get_phrase('Habits'); ?></a>
                </li>

            </ul>
        </li>
        
        <?php
             }
        if(has_permission('liveclass', 'academic')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/liveclass'); ?>" class="side-nav-link <?php if ($page_name == 'liveclass' || $page_name == 'add_live_class' || $page_name == 'liveclass_edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Live Class</span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if (has_permission('users')){
            ?>
            <li class="side-nav-title side-nav-item mt-2 text-info"><?php echo get_phrase('Students & Users'); ?></li>
            <?php
        }
        ?>
        
        
        <?php
        if(has_permission('user_type', 'users')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/user_type'); ?>" class="side-nav-link <?php if ($page_name == 'user_type/index' || $page_name == 'user_type/add' || $page_name == 'user_type/edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span><?php echo get_phrase('user type'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>


        <?php
        if(has_permission('student')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/student'); ?>" class="side-nav-link <?php if ($page_name == 'student/index' || $page_name == 'student/add' || $page_name == 'student/edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span><?php echo get_phrase('students'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>
        
        <?php
        if(has_permission('student')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/report/course_completed_students'); ?>" class="side-nav-link <?php if ($page_name == 'report/course_completed_students')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span><?php echo get_phrase('course_completed_students'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('instructor', 'users')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/instructor/'); ?>" class="side-nav-link <?php if ($page_name == 'instructor/index' || $page_name == 'instructor/add' || $page_name == 'instructor/edit')echo 'active';?>">
                    <i class="mdi mdi-incognito"></i>
                    <span><?php echo get_phrase('instructors'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>
        
         <?php
        if(has_permission('sales_admin', 'users')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/sales_admin/'); ?>" class="side-nav-link <?php if ($page_name == 'sales_admin/index' || $page_name == 'sales_admin/add' || $page_name == 'sales_admin/edit')echo 'active';?>">
                    <i class="mdi mdi-incognito"></i>
                    <span><?php echo get_phrase('sales_admin'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if (has_permission('payments', 'payments')){
            ?>
            <li class="side-nav-title side-nav-item mt-2 text-info"><?php echo get_phrase('Packages & Payments'); ?></li>
            <?php
        }
        ?>




        <?php
        if(has_permission('package', 'payments')){
            ?>
            <li class="side-nav-item <?php if(get_admin_type()=='branch_admin') echo "d-none" ?> ">
                <a href="<?php echo site_url('admin/package/'); ?>" class="side-nav-link <?php if ($page_name == 'package/index' || $page_name == 'package/add' || $page_name == 'package/edit') echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span><?php echo get_phrase('packages'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('payments', 'payments')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/payments/'); ?>" class="side-nav-link <?php if ($page_name == 'payments') echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span><?php echo get_phrase('payments'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>
        
        <?php
        if(has_permission('coupon_code', 'payments')){
            ?>
            <li class="side-nav-item <?php if(get_admin_type()=='branch_admin') echo "d-none" ?> ">
                <a href="<?php echo site_url('admin/coupon_code/'); ?>" class="side-nav-link <?php if ($page_name == 'view_couponcode' || $page_name == 'add_coupon' || $page_name == 'edit_coupon')echo 'active';?>">
                    <i class="dripicons-archive"></i>
                    <span><?php echo get_phrase('coupon_code'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if (has_permission('exams', 'exams')){
            ?>
            <li class="side-nav-title side-nav-item mt-2 text-info"><?php echo get_phrase('exam & question bank'); ?></li>
            <?php
        }
        ?>





        <?php
        if(has_permission('quiz', 'exams')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/quiz/'); ?>" class="side-nav-link <?php if ($page_name == 'quiz' || $page_name == 'quiz_add' || $page_name == 'quiz_edit')echo 'active';?>">
                    <i class="fas fa-question-circle"></i>
                    <span>Quiz</span>
                </a>
            </li>
            <?php
        }
        ?>
        <?php
        if(has_permission('test', 'exams')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/test/'); ?>" class="side-nav-link <?php if ($page_name == 'test' || $page_name == 'test_add' || $page_name == 'test_edit')echo 'active';?>">
                    <i class="fas fa-question-circle"></i>
                    <span>Test</span>
                </a>
            </li>
            <?php
        }
        ?>
        
        <?php
        if(has_permission('question_bank', 'exams')){
            ?>
            <li class="side-nav-item <?php if ($page_name == 'question_bank'): ?> active <?php endif; ?>">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'question_bank'): ?> active <?php endif; ?>">
                    <i class="fas fa-question-circle"></i>
                    <span> Question Bank </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    <?php
                    if(has_permission('question_bank', 'exams')){
                        ?>
                        <li class="side-nav-item <?php if ($page_name == 'question_bank' || $page_name == 'question_bank_add' || $page_name == 'question_bank_edit')echo 'active';?>">
                            <a href="<?php echo site_url('admin/question_bank'); ?>" >
                                <?php echo get_phrase('question_bank'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                    <?php
                    if(has_permission('question_bulk_upload', 'exams')){
                        ?>
                        <li class="side-nav-item <?php if ($page_name == 'question_bulk_upload' || $page_name == 'question_bank_excel_upload' || $page_name == 'question_bank/question_bank_excel_upload')echo 'active';?>">
                            <a href="<?php echo site_url('admin/question_bank/question_bank_excel_upload/'); ?>" >
                                <?php echo get_phrase('question_bulk_upload'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                </ul>
            </li>
            <?php
        }
        ?>


        <?php
        if (has_permission('accounts', 'accounts')){
            ?>
            <!--<li class="side-nav-title side-nav-item mt-2 text-info"><?php echo get_phrase('ACCOUNTING'); ?></li>-->
            <?php
        }
        ?>



        <?php
        if(has_permission('payroll', 'accounts')){
            ?>
            <li class="side-nav-item d-none <?php if ($page_name == 'question_bank'): ?> active <?php endif; ?>">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'question_bank'): ?> active <?php endif; ?>">
                    <i class="dripicons-toggles"></i>
                    <span>Payroll</span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level d-none" aria-expanded="false">
                    <?php
                    if(has_permission('instructor_salary', 'accounts')){
                        ?>
                        <li class="side-nav-item d-none <?php if ($page_name == 'instructor_salary/index' || $page_name == 'instructor_salary/add' || $page_name == 'instructor_salary/edit')echo 'active';?>">
                            <a href="<?php echo site_url('admin/instructor_salary/index'); ?>" >
                                <?php echo get_phrase('instructor_salary'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                    
                    <?php
                    if(has_permission('counselor_salary', 'accounts')){
                        ?>
                        <li class="side-nav-item d-none <?php if ($page_name == 'cre_salary/index' || $page_name == 'cre_salary/add' || $page_name == 'cre_salary/edit')echo 'active';?>">
                            <a href="<?php echo site_url('admin/cre_salary/index'); ?>" >
                                <?php echo get_phrase('counselor_salary'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>

                    <?php
                    if(has_permission('salary_settings', 'accounts')){
                        ?>
                        <li class="side-nav-item d-none <?php if ($page_name == 'salary_settings/index')echo 'active';?>">
                            <a href="<?php echo site_url('admin/salary_settings/index'); ?>" >
                                <?php echo get_phrase('salary_settings'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                </ul>
            </li>
            <?php
        }
        ?>
        <?php
        if(has_permission('accounts', 'accounts')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/accounts/'); ?>" class="side-nav-link <?php if ($page_name == 'accounts/index')echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span>Accounts</span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('expense_category', 'accounts')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/expense_category/'); ?>" class="side-nav-link <?php if ($page_name == 'expense_category/index')echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span>Expense Category</span>
                </a>
            </li>
            <?php
        }
        ?>


        <?php
        if(has_permission('instructor_salary', 'accounts')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/expense/'); ?>" class="side-nav-link <?php if ($page_name == 'expense/index')echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span>Expense</span>
                </a>
            </li>
            <?php
        }
        ?>
        <?php
        if(has_permission('income', 'accounts')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/income/'); ?>" class="side-nav-link <?php if ($page_name == 'income/index')echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span>Income</span>
                </a>
            </li>
            <?php
        }
        ?>
        
        <?php
        if(has_permission('transaction', 'accounts')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/transaction/'); ?>" class="side-nav-link <?php if ($page_name == 'transaction/index')echo 'active';?>">
                    <i class="dripicons-duplicate"></i>
                    <span>Transactions</span>
                </a>
            </li>
            <?php
        }
        ?>
        


        








        <?php
            if (has_permission('others')){
                ?>
                <li class="side-nav-title side-nav-item mt-2 text-info"><?php echo get_phrase('Others'); ?></li>
                <?php
            }
        ?>

        <?php
            if(has_permission('pdf_material')){
                ?>
                <li class=" side-nav-item d-none <?php if ($page_name == 'pdf_material' || $page_name == 'pdf_material_add' || $page_name == 'pdf_category' || $page_name == 'pdf_category_add'): ?> active <?php endif; ?>">
                    <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'enrol_history' || $page_name == 'enrol_student'): ?> active <?php endif; ?>">
                        <i class="dripicons-network-3"></i>
                        <span> <?php echo get_phrase('pdf_materials'); ?> </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="side-nav-second-level" aria-expanded="false">
                        <li class = "<?php if($page_name == 'pdf_category') echo 'active'; ?>">
                            <a href="<?php echo site_url('admin/pdf_category'); ?>"><?php echo get_phrase('pdf_category'); ?></a>
                        </li>
                        <li class = "<?php if($page_name == 'pdf_material') echo 'active'; ?>">
                            <a href="<?php echo site_url('admin/pdf_material'); ?>"><?php echo get_phrase('pdf_materials'); ?></a>
                        </li>
                    </ul>
                </li>
                <?php
            }
        ?>

        <?php
        if(has_permission('demo_videos')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/demo_video/'); ?>" class="side-nav-link <?php if ($page_name == 'demo_video' || $page_name == 'demo_video_add' || $page_name == 'demo_video_edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Demo Videos</span>
                </a>
            </li>
            <?php
        }
        ?>

        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/subjects'); ?>" class="side-nav-link <?php if ($page_name == 'subjects' || $page_name == '' || $page_name == '')echo 'active';?>">
                <i class="fas fa-solid fa-book"></i>
                <span><?php echo get_phrase('subjects (Questions)'); ?></span>
            </a>
        </li>
        <?php
            if (has_permission('feed')){
                ?>
                <li class="side-nav-item  <?php if ($page_name == 'feed' || $page_name == 'feed_add' || $page_name == 'feed_category' || $page_name == 'feed_category_add'): ?> active <?php endif; ?>">
                    <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'feed_category' || $page_name == 'feed_category_add'): ?> active <?php endif; ?>">
                        <i class="dripicons-network-3"></i>
                        <span> <?php echo get_phrase('Feed'); ?> </span>
                        <span class="menu-arrow"></span>
                    </a>
                    <ul class="side-nav-second-level" aria-expanded="false">
                        <li class = "<?php if($page_name == 'feed') echo 'active'; ?>">
                            <a href="<?php echo site_url('admin/feed'); ?>"><?php echo get_phrase('feed'); ?></a>
                        </li>

                        <li class = "<?php if($page_name == 'feed_category') echo 'active'; ?>">
                            <a href="<?php echo site_url('admin/feed_category'); ?>"><?php echo get_phrase('feed_category'); ?></a>
                        </li>
                    </ul>
                </li>

                <?php
            }
            if (has_permission('reels')){
        ?>
        
        <li class="side-nav-item">
            <a href="<?php echo site_url('admin/reels'); ?>" class="side-nav-link <?php if ($page_name == 'reels/index' || $page_name == 'reels/add' || $page_name == 'reels/edit' )echo 'active';?>">
                <i class="dripicons-user-group"></i>
                <span>Reels</span>
            </a>
        </li>
        <?php } ?>
        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/quiz_transfer'); ?>" class="side-nav-link <?php if ($page_name == 'quiz_transfer' )echo 'active';?>">
                <i class="dripicons-user-group"></i>
                <span>Quiz Transfer</span>
            </a>
        </li>

        <?php
        if(has_permission('subject_transfer')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/subject_transfer'); ?>" class="side-nav-link <?php if ($page_name == 'subject_transfer' )echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Subject Transfer</span>
                </a>
            </li>
            <?php
        }
        ?>

        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/notice/view'); ?>" class="side-nav-link <?php if ($page_name == 'notice' || $page_name == 'add_notice' || $page_name == 'edit_notice')echo 'active';?>">
                <i class="dripicons-user-group"></i>
                <span>Notice</span>
            </a>
        </li>

        
        <?php
        if(has_permission('sub_admin')){
            ?>
            <li class="side-nav-item d-none<?php if(get_admin_type()=='branch_admin') echo "d-none" ?> ">
                <a href="<?php echo site_url('admin/sub_admin'); ?>" class="side-nav-link <?php if ($page_name == 'sub_admin' || $page_name == 'sub_admin_add' || $page_name == 'sub_admin_edit')echo 'active';?>">
                    <i class="mdi mdi-incognito"></i>
                    <span><?php echo get_phrase('sub_admin'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('CRM')){
            ?>
            <li class="d-none side-nav-item <?php if ($page_name == 'lead' || $page_name == 'lead_report' || $page_name == 'lead_source_report'): ?> active <?php endif; ?>">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'CRM' || $page_name == 'CRM' || $page_name == 'CRM' ): ?> active <?php endif; ?>">
                    <i class="mdi mdi-incognito"></i>
                    <span> CRM </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    
                    <?php
                    if(has_permission('lead_bulk_upload')){
                        ?>

                        <li class="side-nav-item <?php if ($page_name == 'lead_report')echo 'active';?>">
                            <a href="<?php echo site_url('admin/lead/lead_bulk_upload'); ?>">
                                <?php echo get_phrase('lead bulk upload'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                   
                    <?php
                    if(has_permission('lead_report')){
                        ?>

                        <li class="side-nav-item <?php if ($page_name == 'lead_report')echo 'active';?>">
                            <a href="<?php echo site_url('admin/lead/lead_report'); ?>">
                                <?php echo get_phrase('lead report'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>

                    <?php
                    if(has_permission('lead_source_report')){
                        ?>
                        <li class="side-nav-item <?php if ($page_name == 'lead_source_report')echo 'active';?>">
                            <a href="<?php echo site_url('admin/lead/lead_source_report'); ?>">
                                <?php echo get_phrase('source report'); ?>
                            </a>
                        </li>

                        <?php
                    }
                    ?>
                    <?php
                    if(has_permission('lead_status')){
                        ?>
                        <li class="side-nav-item <?php if ($page_name == 'lead_status' || $page_name == 'lead_status_add' || $page_name == 'lead_status_edit')echo 'active';?>">
                            <a href="<?php echo site_url('admin/lead_status'); ?>" >
                                <?php echo get_phrase('lead status'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                    <?php
                    if(has_permission('lead_source')){
                        ?>
                        <li class="side-nav-item  <?php if ($page_name == 'lead_source' || $page_name == 'lead_source_add' || $page_name == 'lead_source_edit')echo 'active';?>">
                            <a href="<?php echo site_url('admin/lead_source'); ?>">
                                <?php echo get_phrase('lead source'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>

                  

                </ul>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('telecaller')){
            ?>
            <li  class="side-nav-item d-none <?php if ($page_name == 'telecaller'): ?> active <?php endif; ?>" >
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'telecaller'): ?> active <?php endif; ?>">
                    <i class="mdi mdi-incognito"></i>
                    <span> Telecaller </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    <?php
                    if(has_permission('telecaller')){
                        ?>
                        <li class="side-nav-item <?php if ($page_name == 'telecaller' || $page_name == 'telecaller_add' || $page_name == 'telecaller_edit')echo 'active';?>">
                            <a href="<?php echo site_url('admin/telecaller'); ?>" >
                                <?php echo get_phrase('telecallers'); ?>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                </ul>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('stories')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/stories/'); ?>" class="side-nav-link <?php if ($page_name == 'stories' || $page_name == 'stories_add' || $page_name == 'stories_edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Stories</span>
                </a>
            </li>
            <?php
        }
        ?>
        <?php
        if(has_permission('enquiry')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/enquiry/'); ?>" class="side-nav-link <?php if ($page_name == 'enquiry' )echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Enquiry</span>
                </a>
            </li>
            <?php
        }
        ?>
        <?php
        if(has_permission('contact_form')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/enquiry/contact_form'); ?>" class="side-nav-link <?php if ($page_name == 'contact_form' )echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Contact Form</span>
                </a>
            </li>
            <?php
        }
        ?>
        

        <?php
        if(has_permission('notification')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/notification'); ?>" class="side-nav-link <?php if ($page_name == 'notification' || $page_name == 'notification' || $page_name == 'notification')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Notification</span>
                </a>
            </li>
            <?php
        }
        ?>
        
        
        <?php
        if(has_permission('aut_notification')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/notification/auto_notifications'); ?>" class="side-nav-link <?php if ($page_name == 'auto_notifications/index' || $page_name == 'auto_notifications/add' || $page_name == 'auto_notifications/edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Auto-Notification</span>
                </a>
            </li>
            <?php
        }
        ?>
        
        <?php
        if(has_permission('news')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/news'); ?>" class="side-nav-link <?php if ($page_name == 'news/index' || $page_name == 'news/add')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>News</span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        // if(has_permission('job_announcement')){
            ?>
            <!--<li class="side-nav-item">-->
            <!--    <a href="<?php echo site_url('admin/job_announcement'); ?>" class="side-nav-link <?php if ($page_name == 'job_announcements' || $page_name == 'job_announcements_add' || $page_name == 'job_announcements_edit')echo 'active';?>">-->
            <!--        <i class="dripicons-user-group"></i>-->
            <!--        <span>Job Announcements</span>-->
            <!--    </a>-->
            <!--</li>-->
            <?php
        // }
        ?>


        <?php
        if(has_permission('assignment')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/assignment/view'); ?>" class="side-nav-link <?php if ($page_name == 'assignment' || $page_name == 'add_assignment' || $page_name == 'edit_assignment')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Assignment</span>
                </a>
            </li>
            <?php
        }
        ?>

        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/master_concept/'); ?>" class="side-nav-link <?php if ($page_name == 'master_concept' || $page_name == 'master_concept_add' || $page_name == 'master_concept_edit')echo 'active';?>">
                <i class="dripicons-user-group"></i>
                <span>Master the Concept</span>
            </a>
        </li>

        <?php
        if(has_permission('review')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/testimonial/'); ?>" class="side-nav-link <?php if ($page_name == 'testimonial/index' || $page_name == 'testimonial/add' || $page_name == 'testimonial/edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Testimonial</span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('banner')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/banner/'); ?>" class="side-nav-link <?php if ($page_name == 'banner' || $page_name == 'banner_add' || $page_name == 'banner_edit')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Banner</span>
                </a>
            </li>
            <?php
        }
        ?>

        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/video_banner/'); ?>" class="side-nav-link <?php if ($page_name == 'video_banner' || $page_name == 'video_banner_add' || $page_name == 'video_banner_edit')echo 'active';?>">
                <i class="dripicons-user-group"></i>
                <span>Video Banner</span>
            </a>
        </li>

        <?php
        if(has_permission('user_queries')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/user_queries/'); ?>" class="side-nav-link <?php if ($page_name == 'user_queries')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>User Queries</span>
                </a>
            </li>
            <?php
        }
        ?>

        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/upcoming_live_class/'); ?>" class="side-nav-link <?php if ($page_name == 'master_concept' || $page_name == 'master_concept_add' || $page_name == 'master_concept_edit')echo 'active';?>">
                <i class="dripicons-user-group"></i>
                <span>Upcoming Live Classes</span>
            </a>
        </li>

        <?php
        if(has_permission('enrol_history')){
            ?>
            <li class="side-nav-item <?php if ($page_name == 'enrol_history' || $page_name == 'enrol_student' || $page_name == 'enrol_teacher' || $page_name == 'enrol_history_teacher'): ?> active <?php endif; ?>">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'enrol_history' || $page_name == 'enrol_student'): ?> active <?php endif; ?>">
                    <i class="dripicons-network-3"></i>
                    <span> <?php echo get_phrase('enrolment'); ?> </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    
                    <?php
                        if(has_permission('enrol')){
                    ?>
                    <li class = "<?php if($page_name == 'enrol_student') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/enrol/add'); ?>"><?php echo get_phrase('enrol_a_student'); ?></a>
                    </li>
                    <?php
                        } if(has_permission('enrol_history')){
                    ?>
                    <li class = "<?php if($page_name == 'enrol_history') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/enrol'); ?>"><?php echo get_phrase('enrol_history(student)'); ?></a>
                    </li>
                    <?php } ?>
                    <li class = "<?php if($page_name == 'enrol_history_teacher') echo 'active'; ?> d-none">
                        <a href="<?php echo site_url('admin/enrol_history_teacher'); ?>"><?php echo get_phrase('enrol_history(instructor)'); ?></a>
                    </li>
                    <li class = "<?php if($page_name == 'enrol_teacher') echo 'active'; ?> d-none">
                        <a href="<?php echo site_url('admin/enrol_teacher'); ?>"><?php echo get_phrase('enrol_instructor'); ?></a>
                    </li>
                </ul>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('refer_a_friend')){
            ?>
            <li class="side-nav-item d-none">
                <a href="<?php echo site_url('admin/refer_a_friend/'); ?>" class="side-nav-link <?php if ($page_name == 'refer_a_friend')echo 'active';?>">
                    <i class="dripicons-user-group"></i>
                    <span>Refer a Friend</span>
                </a>
            </li>
            <?php
        }
        ?>

        <?php
        if(has_permission('report')){
            ?>
            <li class="side-nav-item">
                <a href="javascript: void(0);" class="side-nav-link <?php if ($page_name == 'student_report' || $page_name == 'student_report/index' || $page_name == 'student_report/detailed_report' || $page_name == 'module_status_report' || $page_name == 'wallet_balance_report' || $page_name == 'wallet_validity_report' || $page_name == 'report/activity_report' || $page_name == 'report/uploaded_activitys'): ?> active <?php endif; ?>">
                    <i class="dripicons-box"></i>
                    <span> <?php echo get_phrase('report'); ?> </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    <li class = "<?php if($page_name == 'student_report/index' || $page_name == 'student_report/detailed_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/student_report'); ?>">Student Activity Report</a> </li>
                    <li class = "<?php if($page_name == 'student_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/student_report'); ?>">Student Enrollment Report</a> </li>
                    <li class = "<?php if($page_name == 'report/purchase_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/user_purchase_report'); ?>">Student  Enrollment Report</a> </li>
                    
                    <li class = "<?php if($page_name == 'report/quiz_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/quiz_report'); ?>">Quiz Report</a> </li>
                    <li class = "d-none<?php if($page_name == 'report/exam_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/exam_report'); ?>">Exam Report</a> </li>
                    
                    <li class = "<?php if($page_name == 'report/activity_report' || $page_name == 'report/uploaded_activitys' || $page_name == 'report/students_name') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/activity_report'); ?>">Activity Report</a> </li>
                    <li class = "<?php if($page_name == 'module_status_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/module_status_report'); ?>">Module Status Report</a> </li>
                    <li class = "<?php if($page_name == 'wallet_balance_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/wallet_balance_report'); ?>">Wallet Balance Report</a> </li>
                    <li class = "<?php if($page_name == 'wallet_validity_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/report/wallet_validity_report'); ?>">Wallet Validity Report</a> </li>
                    <li class = "d-none<?php if($page_name == 'question_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/question_reports_view'); ?>">Question Report</a> </li>
                    <li class = "d-none<?php if($page_name == 'quiz_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/quiz_report'); ?>">Quiz Report</a> </li>
                    <li class = "d-none <?php if($page_name == 'file_report') echo 'active'; ?>" > <a href="<?php echo site_url('admin/file_report'); ?>">Assignment Report</a> </li>
                    <li class = "d-none <?php if($page_name == '') echo 'active'; ?>" > <a href="<?php echo site_url('admin/data_entry_report?user_id=0'); ?>">Daily Data Entry Report</a> </li>
                    <li class = "d-none <?php if($page_name == 'admin_revenue') echo 'active'; ?>" > <a href="<?php echo site_url('admin/admin_revenue'); ?>"><?php echo get_phrase('admin_revenue'); ?></a> </li>
                    
                    
                </ul>
            </li>
            <?php
        }
        ?> 



        <li class="side-nav-item d-none">
            <a href="<?php echo site_url('admin/message'); ?>" class="side-nav-link <?php if ($page_name == 'message' || $page_name == 'message_new' || $page_name == 'message_read')echo 'active';?>">
                <i class="dripicons-message"></i>
                <span><?php echo get_phrase('message'); ?></span>
            </a>
        </li>

        <?php
        if(has_permission('settings')){
            ?>
            <li class="side-nav-item  <?php if ($page_name == 'settings/system_settings' || $page_name == 'settings/frontend_settings' || $page_name == 'settings/contact_details' || $page_name == 'payment_settings' || $page_name == 'instructor_settings' || $page_name == 'smtp_settings' || $page_name == 'manage_language' || $page_name == 'about' || $page_name == 'themes'): ?> active <?php endif; ?>">
                <a href="javascript: void(0);" class="side-nav-link">
                    <i class="dripicons-toggles"></i>
                    <span> <?php echo get_phrase('settings'); ?> </span>
                    <span class="menu-arrow"></span>
                </a>
                <ul class="side-nav-second-level" aria-expanded="false">
                    <li class = "<?php if($page_name == 'settings/basic_details') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/settings/basic_details'); ?>"><?php echo get_phrase('system_settings'); ?></a>
                    </li>
                    <li class = "<?php if($page_name == 'settings/contact_details') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/settings/contact_details'); ?>"><?php echo get_phrase('contact_settings'); ?></a>
                    </li>
                    <li class = "<?php if($page_name == 'settings/frontend_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/settings/frontend_settings'); ?>"><?php echo get_phrase('website_settings'); ?></a>
                    </li>
                    <li class = "d-none <?php if($page_name == 'payment_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/payment_settings'); ?>"><?php echo get_phrase('payment_settings'); ?></a>
                    </li>
                    <li class = "d-none <?php if($page_name == 'instructor_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/instructor_settings'); ?>"><?php echo get_phrase('instructor_settings'); ?></a>
                    </li>
                    <li class = "d-none <?php if($page_name == 'manage_language') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/manage_language'); ?>"><?php echo get_phrase('language_settings'); ?></a>
                    </li>
                    <li class = "d-none <?php if($page_name == 'smtp_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/smtp_settings'); ?>"><?php echo get_phrase('smtp_settings'); ?></a>
                    </li>
                    <li class = "d-none <?php if($page_name == 'theme_settings') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/theme_settings'); ?>"><?php echo get_phrase('theme_settings'); ?></a>
                    </li>
                    <li class = "d-none <?php if($page_name == 'about') echo 'active'; ?>">
                        <a href="<?php echo site_url('admin/about'); ?>"><?php echo get_phrase('about'); ?></a>
                    </li>
                </ul>
            </li>
            <?php
        }
        ?>



        



        <?php
        if(has_permission('app_version')){
            ?>
            <li class="side-nav-item">
                <a href="<?php echo site_url('admin/settings/app_version'); ?>" class="side-nav-link <?php if ($page_name == 'app_version') echo 'active';?>">
                    <i class="dripicons-device-mobile"></i>
                    <span><?php echo get_phrase('app_version'); ?></span>
                </a>
            </li>
            <?php
        }
        ?>
    </ul>
</div>
