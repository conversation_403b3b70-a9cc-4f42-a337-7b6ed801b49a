<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="page-title mb-0">
                        <i class="mdi mdi-file-upload title_icon"></i> 
                        Student Task Upload Details
                    </h4>
                    <button onclick="window.close()" class="btn btn-secondary">
                        <i class="mdi mdi-close"></i> Close
                    </button>
                </div>
                
                <!-- Task Upload Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="mdi mdi-information"></i> Task Information</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>Student:</strong></td>
                                        <td><?php echo htmlspecialchars($task_upload['student_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Lesson:</strong></td>
                                        <td><?php echo htmlspecialchars($task_upload['lesson_title']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Task:</strong></td>
                                        <td><?php echo htmlspecialchars($task_upload['task_title']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Submitted On:</strong></td>
                                        <td><?php echo date('d-M-Y H:i', strtotime($task_upload['datetime'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="mdi mdi-comment-text"></i> Remarks</h5>
                            </div>
                            <div class="card-body">
                                <?php if($task_upload['remarks']): ?>
                                    <p><?php echo nl2br(htmlspecialchars($task_upload['remarks'])); ?></p>
                                <?php else: ?>
                                    <p class="text-muted">No remarks provided by the student.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Uploaded Files -->
                <?php if(!empty($uploaded_files)): ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="mdi mdi-file-multiple"></i> 
                                    Uploaded Files (<?php echo count($uploaded_files); ?>)
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach($uploaded_files as $file): ?>
                                    <div class="col-md-4 mb-3">
                                        <div class="card border">
                                            <div class="card-body text-center">
                                                <?php if($file['type'] == 'image'): ?>
                                                    <div class="mb-2">
                                                        <img src="<?php echo $file['file']; ?>" 
                                                             class="img-fluid" 
                                                             style="max-width: 200px; max-height: 200px; cursor: pointer;"
                                                             alt="Uploaded Image"
                                                             onclick="openImageModal('<?php echo $file['file']; ?>')">
                                                    </div>
                                                <?php else: ?>
                                                    <div class="mb-2">
                                                        <i class="mdi mdi-file-document mdi-48px text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <h6 class="card-title"><?php echo htmlspecialchars($file['filename']); ?></h6>
                                                
                                                <a href="<?php echo $file['file']; ?>" 
                                                   class="btn btn-outline-primary btn-sm" 
                                                   target="_blank">
                                                    <i class="mdi mdi-download"></i> Download
                                                </a>
                                                
                                                <?php if($file['type'] == 'image'): ?>
                                                <button class="btn btn-outline-info btn-sm ml-1" 
                                                        onclick="openImageModal('<?php echo $file['file']; ?>')">
                                                    <i class="mdi mdi-magnify"></i> View
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="mdi mdi-file-outline mdi-48px text-muted"></i>
                                <h5 class="mt-2">No Files Uploaded</h5>
                                <p class="text-muted">The student has not uploaded any files for this task.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
                
            </div> <!-- end card body-->
        </div> <!-- end card -->
    </div><!-- end col-->
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" role="dialog" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid" alt="Image Preview">
            </div>
            <div class="modal-footer">
                <a id="modalDownloadLink" href="" class="btn btn-primary" target="_blank">
                    <i class="mdi mdi-download"></i> Download
                </a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function openImageModal(imageUrl) {
    $('#modalImage').attr('src', imageUrl);
    $('#modalDownloadLink').attr('href', imageUrl);
    $('#imageModal').modal('show');
}
</script>

<style>
.img-fluid {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.25rem;
}

.table td {
    padding: 0.5rem;
    vertical-align: top;
}

.card-header h5 {
    margin-bottom: 0;
}

.mdi-48px {
    font-size: 48px;
}

.card .card-body {
    padding: 1rem;
}

.modal-body img {
    max-width: 100%;
    height: auto;
}
</style>
