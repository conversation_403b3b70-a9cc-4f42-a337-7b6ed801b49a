<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h4 class="page-title">
                    <i class="mdi mdi-account-group title_icon"></i> 
                    <?php echo $page_title; ?>
                </h4>
                
                <!-- Course Selection Form -->
                <div class="justify-content-md-center">
                    <form class="row" action="<?php echo site_url('admin/student_report') ?>" method="get">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Select Course</label>
                                <select class="form-control select2" data-toggle="select2" name="course_id" id="course_id" required>
                                    <option value="">Choose a course...</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id'] ?>" 
                                                <?php if($selected_course_id == $course['id']) echo "selected" ?>>
                                            <?php echo htmlspecialchars($course['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-3" style="margin-top: 10px;">
                            <button type="submit" class="btn btn-primary" style="margin-top:19px;">
                                <i class="mdi mdi-filter"></i> Generate Report
                            </button>
                        </div>
                        
                        <?php if($selected_course_id): ?>
                        <div class="col-md-3" style="margin-top: 10px;">
                            <a href="<?php echo site_url('admin/student_report/export_excel/' . $selected_course_id) ?>" 
                               class="btn btn-success" style="margin-top:19px;">
                                <i class="mdi mdi-download"></i> Export Excel
                            </a>
                        </div>
                        <?php endif; ?>
                    </form>
                </div>
                
            </div> <!-- end card body-->
        </div> <!-- end card -->
    </div><!-- end col-->
</div>

<?php if($selected_course_id && !empty($students)): ?>
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-body">
                <h4 class="mb-3 header-title">
                    Student Activity Report - <?php echo htmlspecialchars($selected_course_title); ?>
                    <span class="badge badge-info ml-2"><?php echo count($students); ?> Students</span>
                </h4>
                
                <div class="table-responsive-sm mt-4">
                    <table id="student-report-table" class="table table-striped table-centered mb-0">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Student Details</th>
                                <th>Premium Status</th>
                                <th>Course Completion</th>
                                <th>Enrollment Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($students as $key => $student): ?>
                                <tr>
                                    <td><?php echo $key + 1; ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($student['name']); ?></strong>
                                            <br>
                                            <small class="text-muted">
                                                <i class="mdi mdi-email"></i> <?php echo htmlspecialchars($student['email']); ?>
                                            </small>
                                            <?php if($student['phone']): ?>
                                            <br>
                                            <small class="text-muted">
                                                <i class="mdi mdi-phone"></i> <?php echo htmlspecialchars($student['phone']); ?>
                                            </small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($student['premium']): ?>
                                            <span class="badge badge-success">
                                                <i class="mdi mdi-crown"></i> Premium
                                            </span>
                                        <?php else: ?>
                                            <span class="badge badge-secondary">
                                                <i class="mdi mdi-account"></i> Free
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar 
                                                <?php 
                                                    if($student['completion_percentage'] >= 80) echo 'bg-success';
                                                    elseif($student['completion_percentage'] >= 50) echo 'bg-warning';
                                                    else echo 'bg-danger';
                                                ?>" 
                                                role="progressbar" 
                                                style="width: <?php echo $student['completion_percentage']; ?>%"
                                                aria-valuenow="<?php echo $student['completion_percentage']; ?>" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                                <?php echo $student['completion_percentage']; ?>%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php echo date('d-M-Y', strtotime($student['enrollment_date'])); ?>
                                        <br>
                                        <small class="text-muted">
                                            <?php echo date('H:i', strtotime($student['enrollment_date'])); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <a href="<?php echo site_url('admin/student_report/detailed_report/' . $student['user_id'] . '/' . $student['course_id']); ?>" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="View Detailed Report">
                                            <i class="mdi mdi-eye"></i> View Details
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div> <!-- end card body-->
        </div> <!-- end card -->
    </div><!-- end col-->
</div>

<?php elseif($selected_course_id && empty($students)): ?>
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-body text-center">
                <div class="py-4">
                    <i class="mdi mdi-account-off mdi-48px text-muted"></i>
                    <h4 class="mt-3">No Students Found</h4>
                    <p class="text-muted">No students are enrolled in the selected course.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
$(document).ready(function() {
    // Initialize DataTable if students exist
    <?php if($selected_course_id && !empty($students)): ?>
    $('#student-report-table').DataTable({
        dom: 'Bfrtip',
        buttons: [
            'copyHtml5',
            'excelHtml5',
            'csvHtml5',
            'pdfHtml5'
        ],
        order: [[3, 'desc']], // Sort by completion percentage descending
        pageLength: 25,
        responsive: true
    });
    <?php endif; ?>
    
    // Initialize Select2
    $('.select2').select2({
        placeholder: "Choose a course...",
        allowClear: true
    });
});
</script>

<style>
.progress {
    background-color: #f8f9fa;
}

.badge {
    font-size: 0.75em;
}

.table td {
    vertical-align: middle;
}

.mdi-48px {
    font-size: 48px;
}
</style>
