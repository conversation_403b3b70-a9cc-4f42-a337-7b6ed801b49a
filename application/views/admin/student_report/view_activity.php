<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="page-title mb-0">
                        <i class="mdi mdi-clipboard-text title_icon"></i> 
                        Student Activity Details
                    </h4>
                    <button onclick="window.close()" class="btn btn-secondary">
                        <i class="mdi mdi-close"></i> Close
                    </button>
                </div>
                
                <!-- Activity Information -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="mdi mdi-information"></i> Activity Information</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>Student:</strong></td>
                                        <td><?php echo htmlspecialchars($activity['student_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Lesson:</strong></td>
                                        <td><?php echo htmlspecialchars($activity['lesson_title']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Activity:</strong></td>
                                        <td><?php echo htmlspecialchars($activity['lesson_file_title']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Activity Type:</strong></td>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php echo ucfirst($activity['activity_type']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Submitted On:</strong></td>
                                        <td><?php echo date('d-M-Y H:i', strtotime($activity['datetime'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="mdi mdi-file-document"></i> Submission Details</h5>
                            </div>
                            <div class="card-body">
                                <?php if($activity['title']): ?>
                                <div class="mb-3">
                                    <strong>Title:</strong>
                                    <p><?php echo htmlspecialchars($activity['title']); ?></p>
                                </div>
                                <?php endif; ?>
                                
                                <?php if($activity['description']): ?>
                                <div class="mb-3">
                                    <strong>Description:</strong>
                                    <p><?php echo nl2br(htmlspecialchars($activity['description'])); ?></p>
                                </div>
                                <?php endif; ?>
                                
                                <?php if($activity['file']): ?>
                                <div class="mb-3">
                                    <strong>Attached File:</strong>
                                    <br>
                                    <?php 
                                    $file_path = $activity['file'];
                                    if(is_file($file_path)):
                                        $file_ext = strtolower(pathinfo($activity['file'], PATHINFO_EXTENSION));
                                        $image_extensions = ['jpg', 'jpeg', 'png', 'gif'];
                                    ?>
                                        <?php if(in_array($file_ext, $image_extensions)): ?>
                                            <div class="mt-2">
                                                <img src="<?php echo base_url($file_path); ?>" 
                                                     class="img-fluid" 
                                                     style="max-width: 300px; max-height: 300px;"
                                                     alt="Activity Image">
                                            </div>
                                        <?php endif; ?>
                                        
                                        <div class="mt-2">
                                            <a href="<?php echo base_url($file_path); ?>" 
                                               class="btn btn-outline-primary btn-sm" 
                                               target="_blank">
                                                <i class="mdi mdi-download"></i> 
                                                Download File (<?php echo strtoupper($file_ext); ?>)
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-danger">File not found</span>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div> <!-- end card body-->
        </div> <!-- end card -->
    </div><!-- end col-->
</div>

<style>
.img-fluid {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.25rem;
}

.table td {
    padding: 0.5rem;
    vertical-align: top;
}

.card-header h5 {
    margin-bottom: 0;
}
</style>
