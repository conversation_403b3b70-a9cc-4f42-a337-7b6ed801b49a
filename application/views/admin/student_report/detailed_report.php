<?php 
$student = $report_data['student'];
$course = $report_data['course'];
$enrollment = $report_data['enrollment'];
$summary = $report_data['summary'];
$lessons = $report_data['lessons'];
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="page-title mb-0">
                        <i class="mdi mdi-account-details title_icon"></i> 
                        Detailed Student Report
                    </h4>
                    <a href="<?php echo site_url('admin/student_report?course_id=' . $course_id); ?>" 
                       class="btn btn-secondary">
                        <i class="mdi mdi-arrow-left"></i> Back to Report
                    </a>
                </div>
                
                <!-- Student & Course Info -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="mdi mdi-account"></i> Student Information</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>Name:</strong></td>
                                        <td><?php echo htmlspecialchars($student['name']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><?php echo htmlspecialchars($student['email']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td><?php echo htmlspecialchars($student['phone'] ?: 'Not provided'); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Premium Status:</strong></td>
                                        <td>
                                            <?php if($enrollment['premium']): ?>
                                                <span class="badge badge-success">
                                                    <i class="mdi mdi-crown"></i> Premium
                                                </span>
                                            <?php else: ?>
                                                <span class="badge badge-secondary">
                                                    <i class="mdi mdi-account"></i> Free
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Enrollment Date:</strong></td>
                                        <td><?php echo date('d-M-Y H:i', strtotime($enrollment['created_on'])); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card border-info">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0"><i class="mdi mdi-book-open"></i> Course Information</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless mb-0">
                                    <tr>
                                        <td><strong>Course:</strong></td>
                                        <td><?php echo htmlspecialchars($course['title']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Overall Progress:</strong></td>
                                        <td>
                                            <div class="progress" style="height: 25px;">
                                                <div class="progress-bar 
                                                    <?php 
                                                        if($summary['course_completion_percentage'] >= 80) echo 'bg-success';
                                                        elseif($summary['course_completion_percentage'] >= 50) echo 'bg-warning';
                                                        else echo 'bg-danger';
                                                    ?>" 
                                                    role="progressbar" 
                                                    style="width: <?php echo $summary['course_completion_percentage']; ?>%">
                                                    <?php echo $summary['course_completion_percentage']; ?>%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Summary Statistics -->
                <div class="row mt-3">
                    <div class="col-md-3">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <h3 class="text-success"><?php echo $summary['completed_lessons']; ?></h3>
                                <p class="mb-0">Lessons Completed</p>
                                <small class="text-muted">out of <?php echo $summary['total_lessons']; ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-primary">
                            <div class="card-body">
                                <h3 class="text-primary"><?php echo $summary['total_lessons']; ?></h3>
                                <p class="mb-0">Total Lessons</p>
                                <small class="text-muted">in this course</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <h3 class="text-info"><?php echo $summary['completed_lesson_videos']; ?></h3>
                                <p class="mb-0">Videos Completed</p>
                                <small class="text-muted">out of <?php echo $summary['total_lesson_videos']; ?></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <h3 class="text-warning"><?php echo $summary['total_lesson_videos']; ?></h3>
                                <p class="mb-0">Total Videos</p>
                                <small class="text-muted">in this course</small>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div> <!-- end card body-->
        </div> <!-- end card -->
    </div><!-- end col-->
</div>

<!-- Lessons Detail -->
<div class="row">
    <div class="col-xl-12">
        <div class="card">
            <div class="card-body">
                <h4 class="mb-3 header-title">
                    <i class="mdi mdi-playlist-check"></i> Lesson-wise Progress
                </h4>
                
                <div class="accordion" id="lessonsAccordion">
                    <?php foreach($lessons as $index => $lesson): ?>
                    <div class="card mb-2">
                        <div class="card-header" id="heading<?php echo $index; ?>">
                            <h5 class="mb-0">
                                <button class="btn btn-link d-flex justify-content-between align-items-center w-100 text-left" 
                                        type="button" 
                                        data-toggle="collapse" 
                                        data-target="#collapse<?php echo $index; ?>" 
                                        aria-expanded="<?php echo $index == 0 ? 'true' : 'false'; ?>" 
                                        aria-controls="collapse<?php echo $index; ?>">
                                    <span>
                                        <?php if($lesson['is_completed']): ?>
                                            <i class="mdi mdi-check-circle text-success"></i>
                                        <?php else: ?>
                                            <i class="mdi mdi-clock-outline text-warning"></i>
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($lesson['title']); ?>
                                    </span>
                                    <span class="badge badge-<?php echo $lesson['is_completed'] ? 'success' : 'warning'; ?>">
                                        <?php echo $lesson['completion_percentage']; ?>%
                                    </span>
                                </button>
                            </h5>
                        </div>

                        <div id="collapse<?php echo $index; ?>" 
                             class="collapse <?php echo $index == 0 ? 'show' : ''; ?>" 
                             aria-labelledby="heading<?php echo $index; ?>" 
                             data-parent="#lessonsAccordion">
                            <div class="card-body">
                                <?php if($lesson['summary']): ?>
                                <p class="text-muted mb-3"><?php echo htmlspecialchars($lesson['summary']); ?></p>
                                <?php endif; ?>
                                
                                <?php if(!empty($lesson['lesson_files'])): ?>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Content</th>
                                                <th>Type</th>
                                                <th>Status</th>
                                                <th>Activity</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach($lesson['lesson_files'] as $file): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($file['title']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge badge-secondary">
                                                        <?php
                                                        $display_type = $file['attachment_type'];
                                                        if ($file['attachment_type'] == 'url' && isset($file['lesson_type']) && $file['lesson_type'] == 'video') {
                                                            $display_type = 'video';
                                                        }
                                                        echo ucfirst($display_type);
                                                        ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if($file['is_completed']): ?>
                                                        <span class="badge badge-success">
                                                            <i class="mdi mdi-check"></i> Completed
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning">
                                                            <i class="mdi mdi-clock"></i> Pending
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($file['activity_link']): ?>
                                                        <a href="<?php echo $file['activity_link']['url']; ?>" 
                                                           class="btn btn-sm btn-outline-primary" 
                                                           target="_blank">
                                                            <i class="mdi mdi-eye"></i> 
                                                            <?php echo $file['activity_link']['title']; ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">No activity</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php else: ?>
                                <p class="text-muted">No content files in this lesson.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
            </div> <!-- end card body-->
        </div> <!-- end card -->
    </div><!-- end col-->
</div>

<script>
$(document).ready(function() {
    // Auto-expand first incomplete lesson
    <?php 
    $first_incomplete = -1;
    foreach($lessons as $index => $lesson) {
        if (!$lesson['is_completed']) {
            $first_incomplete = $index;
            break;
        }
    }
    if ($first_incomplete > 0): ?>
    $('#collapse0').removeClass('show');
    $('#collapse<?php echo $first_incomplete; ?>').addClass('show');
    <?php endif; ?>
});
</script>

<style>
.card-header .btn-link {
    text-decoration: none;
    color: #495057;
}

.card-header .btn-link:hover {
    text-decoration: none;
    color: #007bff;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.progress {
    background-color: #f8f9fa;
}

.accordion .card {
    border: 1px solid #dee2e6;
}

.accordion .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}
</style>
