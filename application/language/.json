{"system_settings_updated": "System settings updated", "404_page_not_found": "404 page not found", "courses": "Courses", "all_courses": "All courses", "search_for_courses": "Search for courses", "administrator": "Administrator", "total": "Total", "go_to_cart": "Go to cart", "your_cart_is_empty": "Your cart is empty", "log_in": "Log in", "sign_up": "Sign up", "oh_snap": "Oh snap", "this_is_not_the_web_page_you_are_looking_for": "This is not the web page you are looking for", "back_to_home": "Back to home", "about": "About", "privacy_policy": "Privacy policy", "terms_and_condition": "Terms and condition", "login": "<PERSON><PERSON>", "step": "Step", "how_would_you_rate_this_course_overall": "How would you rate this course overall", "write_a_public_review": "Write a public review", "describe_your_experience_what_you_got_out_of_the_course_and_other_helpful_highlights": "Describe your experience what you got out of the course and other helpful highlights", "what_did_the_instructor_do_well_and_what_could_use_some_improvement": "What did the instructor do well and what could use some improvement", "next": "Next", "previous": "Previous", "publish": "Publish", "delete": "Delete", "cancel": "Cancel", "system_settings": "System settings", "admin": "Admin", "welcome": "Welcome", "my_account": "My account", "settings": "Settings", "logout": "Logout", "visit_website": "Visit website", "navigation": "Navigation", "dashboard": "Dashboard", "categories": "Categories", "add_new_category": "Add new category", "instructors": "Instructors", "students": "Students", "enrolment": "Enrolment", "enrol_history(student)": "Enrol history(student)", "enrol_a_student": "Enrol a student", "enrol_history(instructor)": "Enrol history(instructor)", "enrol_instructor": "Enrol instructor", "report": "Report", "admin_revenue": "Admin revenue", "instructor_revenue": "Instructor revenue", "message": "Message", "addons": "Addons", "addon_manager": "Addon manager", "available_addons": "Available addons", "website_settings": "Website settings", "payment_settings": "Payment settings", "instructor_settings": "Instructor settings", "language_settings": "Language settings", "smtp_settings": "Smtp settings", "theme_settings": "Theme settings", "packages_&_pricing": "Packages & pricing", "coupon_code": "Coupon code", "payments": "Payments", "app_version": "App version", "website_name": "Website name", "website_title": "Website title", "website_keywords": "Website keywords", "website_description": "Website description", "author": "Author", "slogan": "<PERSON><PERSON><PERSON>", "system_email": "System email", "address": "Address", "phone": "Phone", "youtube_api_key": "Youtube api key", "get_youtube_api_key": "Get youtube api key", "vimeo_api_key": "Vimeo api key", "get_vimeo_api_key": "Get vimeo api key", "purchase_code": "Purchase code", "system_language": "System language", "student_email_verification": "Student email verification", "enable": "Enable", "disable": "Disable", "footer_text": "Footer text", "footer_link": "Footer link", "save": "Save", "update_product": "Update product", "file": "File", "update": "Update", "this_year": "This year", "active_course": "Active course", "pending_course": "Pending course", "heads_up": "Heads up", "congratulations": "Congratulations", "please_fill_all_the_required_fields": "Please fill all the required fields", "close": "Close", "are_you_sure": "Are you sure", "continue": "Continue", "admin_revenue_this_year": "Admin revenue this year", "number_courses": "Number courses", "number_of_lessons": "Number of lessons", "number_of_enrolment": "Number of enrolment", "number_of_student": "Number of student", "course_overview": "Course overview", "active_courses": "Active courses", "pending_courses": "Pending courses", "unpaid_instructor_revenues": "Unpaid instructor revenues", "view_coupon_code": "View coupon code", "package": "Package", "student": "Student", "price": "Price", "discount_%": "Discount %", "validity": "Validity", "actions": "Actions", "edit": "Edit", "hours": "Hours", "min": "Min", "last_name_can_not_be_empty": "Last name can not be empty", "quiz_title": "Quiz title", "number_of_questions": "Number of questions", "question": "Question", "submit_and_next": "Submit and next", "check_result": "Check result", "instruction": "Instruction", "no_instruction_found": "No instruction found", "checkout": "Checkout", "paypal": "<PERSON><PERSON>", "stripe": "Stripe", "review_the_course_materials_to_expand_your_learning": "Review the course materials to expand your learning", "you_got": "You got", "out_of": "Out of", "correct": "Correct", "submitted_answers": "Submitted answers", "take_again": "Take again", "registered_user": "Registered user", "provide_your_valid_login_credentials": "Provide your valid login credentials", "email": "Email", "password": "Password", "forgot_password": "Forgot password", "do_not_have_an_account": "Do not have an account", "registration_form": "Registration form", "sign_up_and_start_learning": "Sign up and start learning", "first_name": "First name", "last_name": "Last name", "already_have_an_account": "Already have an account", "provide_your_email_address_to_get_password": "Provide your email address to get password", "reset_password": "Reset password", "want_to_go_back": "Want to go back", "add_student": "Add student", "name": "Name", "enrolled_courses": "Enrolled courses", "unverified": "Unverified", "student_reset_password": "Student reset password", "login_credentials": "Login credentials", "finish": "Finish", "thank_you": "Thank you", "you_are_just_one_click_away": "You are just one click away", "submit": "Submit", "edit_payment": "Edit payment", "select_student": "Select student", "select_plan": "Select plan", "your_registration_has_been_successfully_done": "Your registration has been successfully done", "instructor": "<PERSON><PERSON><PERSON><PERSON>", "my_courses": "My courses", "go_to_wishlist": "Go to wishlist", "your_wishlist_is_empty": "Your wishlist is empty", "explore_courses": "Explore courses", "hi": "Hi", "welcome_back": "Welcome back", "my_wishlist": "My wishlist", "my_messages": "My messages", "purchase_history": "Purchase history", "user_profile": "User profile", "log_out": "Log out", "about_us": "About us", "shopping_cart": "Shopping cart", "courses_in_cart": "Courses in cart", "add_to_cart": "Add to cart", "added_to_cart": "Added to cart", "there_are_no_courses_on_your_cart": "There are no courses on your cart", "showing_on_this_page": "Showing on this page", "filter": "Filter", "all_category": "All category", "all": "All", "free": "Free", "paid": "Paid", "level": "Level", "beginner": "<PERSON><PERSON><PERSON>", "advanced": "Advanced", "intermediate": "Intermediate", "language": "Language", "ratings": "Ratings", "lessons": "Lessons", "show_more": "Show more", "show_less": "Show less", "none": "None", "add_new_course": "Add new course", "free_courses": "Free courses", "paid_courses": "Paid courses", "course_list": "Course list", "status": "Status", "active": "Active", "pending": "Pending", "..": "..", "title": "Title", "category": "Category", "lesson_and_section": "Lesson and section", "enrolled_student": "Enrolled student", "mark_as_pending": "Mark as pending", "view_course_on_frontend": "View course on frontend", "edit_this_course": "Edit this course", "section_and_lesson": "Section and lesson", "total_section": "Total section", "total_lesson": "Total lesson", "total_enrolment": "Total enrolment", "3499_inr": "3499 inr", "edit_course": "Edit course", "course_manager": "Course manager", "view_on_frontend": "View on frontend", "back_to_course_list": "Back to course list", "curriculum": "Curriculum", "basic": "Basic", "requirements": "Requirements", "outcomes": "Outcomes", "pricing": "Pricing", "media": "Media", "seo": "<PERSON><PERSON>", "add_new_section": "Add new section", "add_section": "Add section", "add_new_lesson": "Add new lesson", "add_lesson": "Add lesson", "add_new_quiz": "Add new quiz", "add_quiz": "Add quiz", "sort_sections": "Sort sections", "section": "Section", "sort_lessons": "Sort lessons", "sort_lesson": "Sort lesson", "update_section": "Update section", "edit_section": "Edit section", "delete_section": "Delete section", "update_lesson": "Update lesson", "lesson": "Lesson", "manage_quiz_questions": "Manage quiz questions", "update_quiz_information": "Update quiz information", "quiz": "Quiz", "course_title": "Course title", "enter_course_title": "Enter course title", "short_description": "Short description", "description": "Description", "select_a_category": "Select a category", "language_made_in": "Language made in", "check_if_this_course_is_top_course": "Check if this course is top course", "provide_requirements": "Provide requirements", "provide_outcomes": "Provide outcomes", "check_if_this_is_a_free_course": "Check if this is a free course", "course_price": "Course price", "enter_course_course_price": "Enter course course price", "check_if_this_course_has_discount": "Check if this course has discount", "discounted_price": "Discounted price", "this_course_has": "This course has", "discount": "Discount", "course_overview_provider": "Course overview provider", "youtube": "Youtube", "vimeo": "Vimeo", "html5": "Html5", "course_overview_url": "Course overview url", "course_thumbnail": "Course thumbnail", "meta_keywords": "Meta keywords", "meta_description": "Meta description", "lesson_type": "Lesson type", "select_type_of_lesson": "Select type of lesson", "video_url": "Video url", "text_file": "Text file", "pdf_file": "Pdf file", "document_file": "Document file", "image_file": "Image file", "lesson_provider": "Lesson provider", "for_web_application": "For web application", "select_lesson_provider": "Select lesson provider", "this_video_will_be_shown_on_web_application": "This video will be shown on web application", "analyzing_the_url": "Analyzing the url", "invalid_url": "Invalid url", "your_video_source_has_to_be_either_youtube_or_vimeo": "Your video source has to be either youtube or vimeo", "duration": "Duration", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "the_image_size_should_be": "The image size should be", "for_mobile_application": "For mobile application", "only": "Only", "type_video_is_acceptable_for_mobile_application": "Type video is acceptable for mobile application", "attachment": "Attachment", "schedule_date": "Schedule date", "summary": "Summary", "edit_lesson": "Edit lesson", "lesson_has_been_updated_successfully": "Lesson has been updated successfully", "add_payment": "Add payment", "invalid_login_credentials": "Invalid login credentials", "video": "Video", "lesson_has_been_added_successfully": "Lesson has been added successfully", "quiz_has_been_added_successfully": "Quiz has been added successfully", "quiz_has_been_updated_successfully": "Quiz has been updated successfully", "questions_of": "Questions of", "update_sorting": "Update sorting", "add_new_question": "Add new question", "questions_have_been_sorted": "Questions have been sorted", "question_title": "Question title", "number_of_options": "Number of options", "question_has_been_added": "Question has been added", "no_options_can_be_blank_and_there_has_to_be_atleast_one_answer": "No options can be blank and there has to be atleast one answer", "update_quiz_question": "Update quiz question", "option": "Option", "option_": "Option ", "question_has_been_updated": "Question has been updated", "notice": "Notice", "instructor_page": "Instructor page", "show_full_biography": "Show full biography", "total_student": "Total student", "reviews": "Reviews", "register_yourself": "Register yourself", "user_deleted_successfully": "User deleted successfully", "enrolment_form": "Enrolment form", "user": "User", "select_a_user": "Select a user", "course_to_enrol": "Course to enrol", "select_a_course": "Select a course", "enrol_student": "Enrol student", "enrol_history": "Enrol history", "enrol_histories": "Enrol histories", "photo": "Photo", "user_name": "User name", "enrolled_course": "Enrolled course", "enrolment_date": "Enrolment date", "course": "Course", "students_enrolled": "Students enrolled", "created_by": "Created by", "last_updated": "Last updated", "what_will_i_learn": "What will i learn", "curriculum_for_this_course": "Curriculum for this course", "view_more": "View more", "other_related_courses": "Other related courses", "about_the_instructor": "About the instructor", "student_feedback": "Student feedback", "average_rating": "Average rating", "buy_now": "Buy now", "includes": "Includes", "on_demand_videos": "On demand videos", "full_lifetime_access": "Full lifetime access", "access_on_mobile_and_tv": "Access on mobile and tv", "please_wait": "Please wait", "lesson_has_been_deleted_successfully": "Lesson has been deleted successfully", "question_has_been_deleted": "Question has been deleted", "quiz_transfer": "Quiz transfer", "invalid_lesson_url_and_duration": "Invalid lesson url and duration", "lessons_have_been_sorted": "Lessons have been sorted", "subject_transfer": "Subject transfer", "add_course": "Add course", "course_adding_form": "Course adding form", "course_has_been_added_successfully": "Course has been added successfully", "0_inr": "0 inr", "no_result_found": "No result found", "updated": "Updated", "sort_lessons_of": "Sort lessons of", "sub_categories": "Sub categories", "add_category": "Add category", "category_add_form": "Category add form", "category_code": "Category code", "category_title": "Category title", "parent": "Parent", "icon_picker": "Icon picker", "category_thumbnail": "Category thumbnail", "choose_thumbnail": "Choose thumbnail", "assignment": "Assignment", "choose_user_image": "Choose user image", "course_status_updated": "Course status updated", "mark_as_active": "<PERSON> as active", "add_new_package": "Add new package", "section(s)": "Section(s)", "amount": "Amount", "offer_price": "Offer price", "action": "Action", "section_has_been_deleted_successfully": "Section has been deleted successfully", "list_of_sections": "List of sections", "sections_have_been_sorted": "Sections have been sorted", "instructor_edit": "Instructor edit", "basic_info": "Basic info", "social_information": "Social information", "payment_info": "Payment info", "biography": "Biography", "user_image": "User image", "facebook": "Facebook", "twitter": "Twitter", "linkedin": "Linkedin", "paypal_client_id": "Paypal client id", "required_for_instructor": "Required for instructor", "paypal_secret_key": "<PERSON><PERSON> secret key", "stripe_public_key": "Stripe public key", "stripe_secret_key": "Stripe secret key", "enrol_history_teacher": "Enrol history teacher", "enrol_history_instructor": "Enrol history instructor", "enrol_histories_(instructor)": "Enrol histories (instructor)", "enrol_teacher": "Enrol teacher", "select_teacher": "Select teacher", "subject_to_enrol": "Subject to enrol", "select_subjects": "Select subjects", "private_messaging": "Private messaging", "private_message": "Private message", "new_message": "New message", "choose_an_option_from_the_left_side": "Choose an option from the left side", "please_enter_your_messsage": "Please enter your messsage", "sent_message": "Sent message", "wishlists": "Wishlists", "search_my_courses": "Search my courses", "you_do_not_have_permission_to_access_this_course": "You do not have permission to access this course", "student_edit": "Student edit", "student_edit_form": "Student edit form", "no_secret_key_found": "No secret key found", "data_deleted_successfully": "Data deleted successfully", "student_has_been_enrolled_to_that_course": "Student has been enrolled to that course", "email_duplication": "Email duplication", "setup_payment_informations": "Setup payment informations", "system_currency_settings": "System currency settings", "system_currency": "System currency", "select_system_currency": "Select system currency", "currency_position": "Currency position", "left": "Left", "right": "Right", "left_with_a_space": "Left with a space", "right_with_a_space": "Right with a space", "update_system_currency": "Update system currency", "setup_paypal_settings": "Setup paypal settings", "no": "No", "yes": "Yes", "mode": "Mode", "sandbox": "Sandbox", "production": "Production", "paypal_currency": "Paypal currency", "select_paypal_currency": "Select paypal currency", "client_id": "Client id", "secret_key": "Secret key", "update_paypal_keys": "Update paypal keys", "setup_stripe_settings": "Setup stripe settings", "test_mode": "Test mode", "on": "On", "off": "Off", "stripe_currency": "Stripe currency", "select_stripe_currency": "Select stripe currency", "test_secret_key": "Test secret key", "test_public_key": "Test public key", "live_secret_key": "Live secret key", "live_public_key": "Live public key", "update_stripe_keys": "Update stripe keys", "please_make_sure_that": "Please make sure that", "are_same": "Are same", "frontend_settings": "Frontend settings", "banner_title": "Banner title", "banner_sub_title": "Banner sub title", "cookie_status": "Cookie status", "inactive": "Inactive", "cookie_note": "Cookie note", "cookie_policy": "Cookie policy", "update_settings": "Update settings", "update_banner_image": "Update banner image", "upload_banner_image": "Upload banner image", "update_light_logo": "Update light logo", "upload_light_logo": "Upload light logo", "update_dark_logo": "Update dark logo", "upload_dark_logo": "Upload dark logo", "update_small_logo": "Update small logo", "upload_small_logo": "Upload small logo", "update_favicon": "Update favicon", "upload_favicon": "Upload favicon", "allow_public_instructor": "Allow public instructor", "instructor_revenue_percentage": "Instructor revenue percentage", "admin_revenue_percentage": "Admin revenue percentage", "multi_language_settings": "Multi language settings", "manage_language": "Manage language", "language_list": "Language list", "add_phrase": "Add phrase", "add_language": "Add language", "edit_phrase": "Edit phrase", "delete_language": "Delete language", "add_new_phrase": "Add new phrase", "add_new_language": "Add new language", "no_special_character_or_space_is_allowed": "No special character or space is allowed", "valid_examples": "Valid examples", "phrase_updated": "Phrase updated", "protocol": "Protocol", "smtp_host": "Smtp host", "smtp_port": "Smtp port", "smtp_username": "Smtp username", "smtp_password": "Smtp password", "get_new_themes": "Get new themes", "installed_themes": "Installed themes", "add_new_themes": "Add new themes", "active_theme": "Active theme", "theme_successfully_activated": "Theme successfully activated", "you_do_not_have_right_to_access_this_theme": "You do not have right to access this theme", "logo_updated": "Logo updated", "favicon_updated": "Favicon updated", "write_new_messages": "Write new messages", "recipient": "Recipient", "type_your_message": "Type your message", "version_saved_successfully": "Version saved successfully", "provide_a_section_name": "Provide a section name", "section_has_been_updated_successfully": "Section has been updated successfully", "course_updated_successfully": "Course updated successfully", "live_class": "Live class", "instructor_add": "Instructor add", "add_live_class": "Add live class", "quiz_report": "Quiz report", "no_data_found": "No data found", "data_added_successfully": "Data added successfully", "edit_category": "Edit category", "update_category": "Update category", "update_category_form": "Update category form", "data_updated_successfully": "Data updated successfully", "student_add": "Student add", "student_add_form": "Student add form", "section_has_been_added_successfully": "Section has been added successfully", "notification": "Notification", "notifications": "Notifications", "add_new_nofication": "Add new nofication", "batch": "<PERSON><PERSON>", "add_batch": "Add batch", "edit_batch": "Edit batch", "batchs": "<PERSON><PERSON>", "select_a_batch": "Select a batch", "user_added_successfully": "User added successfully", "user_update_successfully": "User update successfully", "batches_and_courses": "Batches and courses", "check_if_this_cource_for_external_students": "Check if this cource for external students", "course_categories": "Course categories", "courses_and_batches": "Courses and batches", "data_deleted": "Data deleted", "total_amount": "Total amount", "excel_upload": "Excel upload", "add_package": "Add package", "select_course": "Select course", "select_section": "Select section", "edit_package": "Edit package", "high_quality_videos": "High quality videos", "life_time_access": "Life time access", "add_premium": "Add premium", "success": "Success", "edit_premium": "Edit premium", "remove_premium": "Remove premium", "email_can_not_be_empty": "Email can not be empty", "first_name_can_not_be_empty": "First name can not be empty", "course_details": "Course details", "video_url_is_not_supported": "Video url is not supported", "note": "Note", "no_summary_found": "No summary found", "course_content": "Course content", "you_have_completed": "You have completed", "of_the_course": "Of the course", "you_can_download_the_course_completion_certificate_after_completing_the_course": "You can download the course completion certificate after completing the course", "well_done": "Well done", "you_are_now_eligible_to_download_the_course_completion_certificate": "You are now eligible to download the course completion certificate", "install_certificate_addon_first": "Install certificate addon first", "draft_courses": "Draft courses", "search_results": "Search results", "no_search_value_found": "No search value found", "manage_profile": "Manage profile", "facebook_link": "Facebook link", "twitter_link": "Twitter link", "linkedin_link": "Linkedin link", "a_short_title_about_yourself": "A short title about yourself", "the_image_size_should_be_any_square_image": "The image size should be any square image", "choose_file": "Choose file", "update_profile": "Update profile", "current_password": "Current password", "new_password": "New password", "confirm_new_password": "Confirm new password", "update_password": "Update password", "add_coupon_code": "Add coupon code", "coupon_code_has_been_added_successfully": "Coupon code has been added successfully", "student_has_already_been_enrolled_to_this_course": "Student has already been enrolled to this course", "file_report": "File report", "users_new": "Users new", "login_with_email": "Login with email", "email_id": "Email id", "login_with_phone": "Login with phone", "filter_by": "Filter by", "reset": "Reset", "completed": "Completed", "your": "Your", "rating": "Rating", "edit_rating": "Edit rating", "cancel_rating": "Cancel rating", "course_detail": "Course detail", "start_lesson": "Start lesson", "write_your_review_here": "Write your review here", "publish_rating": "Publish rating", "invalid_attachment": "Invalid attachment", "already_purchased": "Already purchased", "all_subjects": "All subjects", "view_lesson": "View lesson", "select_a_message_thread_to_read_it_here": "Select a message thread to read it here", "send": "Send", "profile": "Profile", "account": "Account", "add_information_about_yourself_to_share_on_your_profile": "Add information about yourself to share on your profile", "basics": "Basics", "add_your_twitter_link": "Add your twitter link", "add_your_facebook_link": "Add your facebook link", "add_your_linkedin_link": "Add your linkedin link", "my_subjects": "My subjects", "videos": "Videos", "materials": "Materials", "download": "Download", "video_has_been_locked": "Video has been locked", "update_user_photo": "Update user photo", "update_your_photo": "Update your photo", "upload_image": "Upload image", "by": "By", "remove": "Remove", "no_section_found": "No section found", "get_started": "Get started", "prev": "Prev", "syllabus": "Syllabus", "choose_syllabus": "Choose syllabus", "not_found": "Not found", "about_this_application": "About this application", "software_version": "Software version", "check_update": "Check update", "php_version": "Php version", "curl_enable": "Curl enable", "enabled": "Enabled", "purchase_code_status": "Purchase code status", "support_expiry_date": "Support expiry date", "customer_name": "Customer name", "get_customer_support": "Get customer support", "customer_support": "Customer support", "module": "<PERSON><PERSON><PERSON>", "chapter": "Chapter", "select_chapter": "Select chapter", "select_video": "Select video", "add_notification": "Add notification", "enrol_course": "Enrol course", "successfully_enrolled__to_the_course": "Successfully enrolled  to the course", "signup": "Signup", "register": "Register", "please_provide_your_details": "Please provide your details", "phone_number_not_registered!": "Phone number not registered!", "exams_list": "Exams list", "exams": "<PERSON><PERSON>", "phone_number_already_exists": "Phone number already exists", "credentials": "Credentials", "edit_your_account_settings": "Edit your account settings", "enter_current_password": "Enter current password", "enter_new_password": "Enter new password", "re-type_your_password": "Re-type your password", "password_reset_failed": "Password reset failed", "updated_successfully": "Updated successfully", "date": "Date", "total_price": "Total price", "payment_type": "Payment type", "no_records_found": "No records found", "hr": "Hr", "invalid_lesson_provider": "Invalid lesson provider", "mismatch_password": "Mismatch password", "please_check_your_mail_inbox_to_verify_your_email_address": "Please check your mail inbox to verify your email address", "please_check_your_email_for_new_password": "Please check your email for new password", "no_lesson_found": "No lesson found", "view_attachment": "View attachment", "view_material": "View material", "message_sent!": "Message sent!", "master_concept": "Master concept", "master_concept_add": "Master concept add", "master_concept_edit": "Master concept edit", "update_type": "Update type", "": "", "student_course": "Student course", "student_courses": "Student courses", "upcoming_live_class": "Upcoming live class", "upcoming_live_class_add": "Upcoming live class add", "upcoming_live_class_edit": "Upcoming live class edit", "deleted_successfully": "Deleted successfully", "review_add": "Review add", "review": "Review", "review_edit": "Review edit", "update_splash_card": "Update splash card", "update_review": "Update review", "ad_banner": "Ad banner", "ad_banner_add": "Ad banner add", "ad_banner_edit": "Ad banner edit", "update_banner": "Update banner", "student_name": "Student name", "amount_to_pay": "Amount to pay", "tests": "Tests", "test_category": "Test category", "test_category_add": "Test category add", "test_category_edit": "Test category edit", "test_add": "Test add", "select_level": "Select level", "select_type": "Select type", "test_edit": "Test edit", "test_questions": "Test questions", "is_for_all": "Is for all", "pdf_category": "Pdf category", "pdf_category_add": "Pdf category add", "pdf_category_edit": "Pdf category edit", "pdf_materials": "Pdf materials", "pdf_material": "Pdf material", "pdf_material_add": "Pdf material add", "pdf_material_edit": "Pdf material edit", "update_material": "Update material", "test_question_add": "Test question add", "available_addon": "Available addon", "all_available_addon": "All available addon", "correct_answer": "Correct answer", "test_question_edit": "Test question edit", "sort_review": "Sort review", "list_of_review": "List of review", "reviews_have_been_sorted": "Reviews have been sorted", "sort_course": "Sort course", "list_of_course": "List of course", "course_have_been_sorted": "Course have been sorted", "sort_master_concept": "Sort master concept", "list_of_master_concept": "List of master concept", "master_concept_have_been_sorted": "Master concept have been sorted", "sort_pdf_category": "Sort pdf category", "list_of_pdf_category": "List of pdf category", "pdf_category_have_been_sorted": "Pdf category have been sorted", "sort_pdf_material": "Sort pdf material", "pdf_material_have_been_sorted": "Pdf material have been sorted", "titlex": "Titlex", "study_materials": "Study materials", "lesson_note": "Lesson note", "lecture_note": "Lecture note", "practice_with_solution": "Practice with solution", "savesca": "Savesca", "contact_whatsapp": "Contact whatsapp", "contact_phone": "Contact phone", "contact_email": "Contact email", "contact_address": "Contact address", "select_a_section": "Select a section", "question_bank": "Question bank", "filter_questions": "Filter questions", "add_question": "Add question", "questions_list": "Questions list", "edit_question": "Edit question", "sort_quiz": "Sort quiz", "quiz_add": "Quiz add", "is_free": "Is free", "quiz_edit": "Quiz edit", "statusss": "Statusss", "list_of_quiz": "List of quiz", "quiz_have_been_sorted": "Quiz have been sorted", "quiz_list": "Quiz list", "question_bank_add": "Question bank add", "question_add": "Question add", "question_added_successfully": "Question added successfully", "select_a_lesson": "Select a lesson", "duration_per_question": "Duration per question", "duration_per_question(seconds)": "Duration per question(seconds)", "quiz_questions": "Quiz questions", "add_question_to_quiz": "Add question to quiz", "edit_coupon_code": "Edit coupon code", "update_coupon_code": "Update coupon code", "coupon_code_has_been_updated_successfully": "Coupon code has been updated successfully", "7900_inr": "7900 inr", "question_bank_edit": "Question bank edit", "3999_inr": "3999 inr", "feed": "Feed", "feeds": "Feeds", "feed_category": "Feed category", "feed_category_add": "Feed category add", "feed_add": "Feed add", "feed_title": "Feed title", "content": "Content", "image": "Image", "choose_image": "Choose image", "feed_category_edit": "Feed category edit", "update_feed": "Update feed", "feed_edit": "Feed edit", "audio_file": "Audio file", "video_banner": "Video banner", "video_banner_add": "Video banner add", "video_banner_edit": "Video banner edit", "frontend_settings_updated": "Frontend settings updated", "1800_inr": "1800 inr", "add_question_to_test": "Add question to test", "user_queries": "User queries", "feedback": "<PERSON><PERSON><PERSON>", "query": "Query", "1350_inr": "1350 inr", "1180_inr": "1180 inr", "1380_inr": "1380 inr", "phone_duplication": "Phone duplication", "something_went_wrong": "Something went wrong", "users": "Users", "no_of_times": "No of times", "no_of_times_per_user": "No of times per user", "start_date": "Start date", "end_date": "End date", "refer_a_friend": "Refer a friend", "refered_by": "Refered by", "is_practice_test": "Is practice test", "course_type": "Course type", "question_equation": "Question equation", "solution": "Solution", "solution_equation": "Solution equation", "subjects": "Subjects", "subject": "Subject", "add_subject": "Add subject", "select_a_subject": "Select a subject", "subjects_(questions)": "Subjects (questions)", "question_report": "Question report", "number_of_subjects": "Number of subjects", "todays_of_questions": "Todays of questions", "todays_number_of_questions": "Todays number of questions", "todays_questions": "Todays questions", "password_updated": "Password updated", "data_entry_report_": "Data entry report ", "is_model": "Is model", "activity": "Activity", "pdf": "Pdf", "batches": "Batches", "select_batch": "Select batch", "user_enrolled_successfully!": "User enrolled successfully!", "add_days": "Add days", "add_days_bulk": "Add days bulk", "start_day": "Start day", "end_day": "End day", "1250_inr": "1250 inr", "2450_inr": "2450 inr", "2399_inr": "2399 inr", "2499_inr": "2499 inr", "orders": "Orders", "add_order": "Add order", "select_user": "Select user", "approximate_delivery_date": "Approximate delivery date", "remarks": "Remarks", "edit_order": "Edit order", "brochure": "Brochure", "choose_brochure": "Choose brochure", "course_banner": "Course banner", "permission": "Permission", "assign_permission": "Assign permission", "sub_admin": "Sub admin", "all_course": "All course", "demo_class_link": "Demo class link", "sub_admin_add": "Sub admin add", "plus_one": "Plus one", "plus_two": "Plus two", "add_bank_account": "Add bank account", "assign_student": "Assign student", "assign_course_list": "Assign course list", "add_permission": "Add permission", "edit_permission": "Edit permission", "sub_admin_edit": "Sub admin edit", "phone_number_duplication": "Phone number duplication", "stories": "Stories", "edit_stories": "Edit stories", "update_story": "Update story", "add_stories": "Add stories", "download_url": "Download url", "video_index": "Video index", "add_video_index": "Add video index", "edit_video_index": "Edit video index", "update_video_index": "Update video index", "edit_assignment": "Edit assignment", "check_if_show_exam": "Check if show exam", "check_if_show_practice": "Check if show practice", "check_if_show_material": "Check if show material", "branch": "Branch", "edit_branch": "Edit branch", "branches": "Branches", "select_a_branch": "Select a branch", "branch_admin": "Branch admin", "assign_batch": "Assign batch", "assign_branch": "Assign branch", "batch_assigned_successfully!": "<PERSON>ch assigned successfully!", "assignment_report": "Assignment report", "zoom_id": "Zoom id", "zoom_password": "Zoom password", "lesson_batch_status": "Lesson batch status", "instructor_courses": "Instructor courses", "publish_result": "Publish result", "edit_branch_admin": "Edit branch admin", "select_branch": "Select branch", "online_course": "Online course", "unauthorized_login": "Unauthorized login", "150_inr": "150 inr", "preview_this_course": "Preview this course", "course_preview": "Course preview", "11799_inr": "11799 inr", "view": "View", "view_video": "View video", "back": "Back", "sec": "Sec", "5899_inr": "5899 inr", "check_if_online_course": "Check if online course", "_batch_students": " batch students", "check_if_free_lesson": "Check if free lesson", "phone/email_duplication": "Phone/email duplication", "get_enrolled": "Get enrolled", "successfully_enrolled": "Successfully enrolled", "payment_gateway": "Payment gateway", "make_payment": "Make payment", "payment": "Payment", "gateway": "Gateway", "order": "Order", "pay_by_paypal": "Pay by paypal", "pay_by_stripe": "Pay by stripe", "question_bank_upload": "Question bank upload", "data_uploaded_successfully": "Data uploaded successfully", "lead": "Lead", "lead_report": "Lead report", "source_report": "Source report", "lead_managers": "Lead managers", "lead_status": "Lead status", "lead_source": "Lead source", "telecallers": "Telecallers", "source": "Source", "lead_manager": "Lead manager", "remark": "Remark", "follow_up_date": "Follow up date", "select_a_lead_manager": "Select a lead manager", "select_a_telecaller": "Select a telecaller", "select_status": "Select status", "select_a_source": "Select a source", "lead_source_report": "Lead source report", "e-mail": "E-mail", "lead_manager_add": "Lead manager add", "lead_manager_add_form": "Lead manager add form", "lead_status_add": "Lead status add", "add_lead_status": "Add lead status", "lead_status_add_form": "Lead status add form", "telecaller": "Telecaller", "telecaller_add": "Telecaller add", "assign": "Assign", "lead_assing": "Lead assing", "assing_to_telecaller": "Assing to telecaller", "telecaller_assign_form": "Telecaller assign form", "select_a_lead": "Select a lead", "lead_add": "Lead add", "add_lead": "Add lead", "lead_add_form": "Lead add form", "class": "Class", "lead_added_successfully": "Lead added successfully", "quiz_category": "Quiz category", "quiz_category_add": "Quiz category add", "quiz_category_edit": "Quiz category edit", "lead_update_successfully": "Lead update successfully", "demo_videos": "Demo videos", "sort_demo_videos": "Sort demo videos", "demo_video_add": "Demo video add", "demo_video_edit": "Demo video edit", "job_announcement": "Job announcement", "last_date": "Last date", "job_announcements": "Job announcements", "add_new": "Add new", "job_announcement_add": "Job announcement add", "posted_on": "Posted on", "job_announcement_edit": "Job announcement edit", "age_limit": "Age limit", "no._of_vacancy": "No. of vacancy", "qualification": "Qualification", "list_of_demo_video": "List of demo video", "items_have_been_sorted": "Items have been sorted", "1_inr": "1 inr", "26000_inr": "26000 inr", "24000_inr": "24000 inr", "30000_inr": "30000 inr", "lead_deleted_successfully": "Lead deleted successfully", "current_affairs": "Current affairs", "current_affairs_edit": "Current affairs edit", "current_affairs_add": "Current affairs add", "inform_instructor": "Inform instructor", "status_changed_successfully": "Status changed successfully", "meta_title": "Meta title", "course_added_successfully": "Course added successfully", "please_wait_untill_admin_approves_it": "Please wait untill admin approves it", "course_edit": "Course edit", "add_bulk": "Add bulk", "added_successfully": "Added successfully", "view_tasks": "View tasks", "lesson_files": "Lesson files", "add_lesson_file": "Add lesson file", "add_task": "Add task", "tasks_-_lesson_a": "Tasks - lesson a", "add_new_task": "Add new task", "select_a_type": "Select a type", "answer_key": "Answer key", "answer": "Answer", "audio": "Audio", "edit_task": "Edit task", "tasks_-_percentage": "Tasks - percentage", "back_to_course": "Back to course", "exam": "Exam", "exam_and_question_bank": "Exam and question bank", "course_and_study_materials": "Course and study materials", "course_&_study_materials": "Course & study materials", "exam_&_question_bank": "Exam & question bank", "academic": "Academic", "students_&_instructors": "Students & instructors", "counselors": "Counselors", "lesson_files_-_lesson_a": "Lesson files - lesson a", "tasks_-_lesson_b": "Tasks - lesson b", "edit_lesson_file": "Edit lesson file", "add_lesson_bulk": "Add lesson bulk", "add_lessons": "Add lessons", "tasks_-_premium_1": "Tasks - premium 1", "lesson_files_-_premium_1": "Lesson files - premium 1", "lesson_files_-_free_1": "Lesson files - free 1", "tasks_-_free_1": "Tasks - free 1", "check_if_top_course": "Check if top course", "packages": "Packages", "select_category": "Select category", "packages_&_payments": "Packages & payments", "others": "Others", "liveclass": "Liveclass", "add_instructor": "Add instructor", "add_liveclass": "Add liveclass", "select_package": "Select package", "package_edit": "Package edit", "banner": "Banner", "counselor": "Counselor", "add_counselor": "Add counselor", "counselor_edit": "Counselor edit", "add_banner": "Add banner", "banner_edit": "Banner edit", "add_feed_category": "Add feed category", "add_enrollment": "Add enrollment", "add_feed": "Add feed", "enrol": "Enrol", "add_pdf_category": "Add pdf category", "add_pdf_material": "Add pdf material", "testimonial": "Testimonial", "add_testimonial": "Add testimonial", "testimonial_edit": "Testimonial edit", "stories_edit": "Stories edit", "show_live": "Show live", "show_material": "Show material", "show_exam": "Show exam", "show_practice": "Show practice", "add_job_announcement": "Add job announcement", "all_packages": "All packages", "liveclass_edit": "Liveclass edit", "update_live_class": "Update live class", "coupon_code_edit": "Coupon code edit", "test": "Test", "add_test": "Add test", "enrolled_successfully": "Enrolled successfully", "instructor_students": "Instructor students", "assign_students": "Assign students", "assign_instructor": "Assign instructor", "already_assigned": "Already assigned", "assigned_successfully": "Assigned successfully", "add_question_bank": "Add question bank", "accounting": "Accounting", "video_type": "Video type", "video_link": "Video link", "expense_category": "Expense category", "add_expense_category": "Add expense category", "edit_expense_category": "Edit expense category", "accounts": "Accounts", "add_account": "Add account", "balance": "Balance", "initial_balance": "Initial balance", "edit_account": "Edit account", "expense": "Expense", "add_expense": "Add expense", "debit_from": "Debit from", "expense_date": "Expense date", "choose_user": "Choose user", "reference_no": "Reference no", "edit_expense": "Edit expense", "crm": "Crm", "leads": "Leads", "irs_crm": "Irs crm", "question_bank_excel_upload": "Question bank excel upload", "list": "List", "income": "Income", "question_bulk_upload": "Question bulk upload", "transactions": "Transactions", "lead_source_edit": "Lead source edit", "add_lead_source": "Add lead source", "lead_source_add_form": "Lead source add form", "edit_lead_source": "Edit lead source", "lead_source_edit_form": "Lead source edit form", "lead_status_edit": "Lead status edit", "edit_lead_status": "Edit lead status", "lead_status_edit_form": "Lead status edit form", "basic_details": "Basic details", "basic_details_settings": "Basic details settings", "zoom_details": "Zoom details", "zoom_details_settings": "Zoom details settings", "zoom_api_key": "Zoom api key", "zoom_secret_key": "Zoom secret key", "razorpay_details": "Razorpay details", "razorpay_details_settings": "Razorpay details settings", "razorpay_api_key": "Razorpay api key", "razorpay_api_secret_key": "Ra<PERSON><PERSON><PERSON> api secret key", "contact_details": "Contact details", "contact_details_settings": "Contact details settings", "notification_key_details": "Notification key details", "notification_settings": "Notification settings", "notification_key": "Notification key", "app_version_details": "App version details", "app_version_settings": "App version settings", "android_version": "Android version", "ios_version": "Ios version", "select_source": "Select source", "select_laed_source": "Select laed source", "select_lead_source": "Select lead source", "select_cre": "Select cre", "followup_date": "Followup date", "lead_history": "Lead history", "instructor_salary": "Instructor salary", "instructor_salary_settings": "Instructor salary settings", "contact_settings": "Contact settings", "change_lead_status": "Change lead status", "lead_status_change": "Lead status change", "check_if_also_offline_course": "Check if also offline course", "students_&_users": "Students & users", "accountants": "Accountants", "accountant": "Accountant", "add_accountant": "Add accountant", "salary_settings": "Salary settings", "select_instructor": "Select instructor", "accountant_edit": "Accountant edit", "student_report": "Student report", "academic_coordinator": "Academic coordinator", "add_academic_coordinator": "Add academic coordinator", "academic_coordinator_edit": "Academic coordinator edit", "lead_bulk_upload": "Lead bulk upload", "cre_salary": "Cre salary", "some_of_the_leads_are_already_in_the_list!": "Some of the leads are already in the list!", "leads_bulk_upload_status": "Leads bulk upload status", "counselor_salary": "Counselor salary", "live": "Live", "start_live_class": "Start live class", "start_hosting": "Start hosting", "package_features": "Package features", "add_feature": "Add feature", "value": "Value", "tasks_-_premium_test_1": "Tasks - premium test 1", "lesson_files_-_premium_test_1": "Lesson files - premium test 1", "back_to_packages": "Back to packages", "features": "Features", "add_new_feature": "Add new feature", "tasks_-_premium_test_2": "Tasks - premium test 2", "package_lessons": "Package lessons", "tasks_-_premium_test_3": "Tasks - premium test 3", "lesson_files_-_premium_test_2": "Lesson files - premium test 2", "lesson_files_-_premium_test_3": "Lesson files - premium test 3", "tasks_-_premium_test_4": "Tasks - premium test 4", "lesson_files_-_premium_test_4": "Lesson files - premium test 4", "tasks_-_premium_test_5": "Tasks - premium test 5", "tasks_-_premium_test_6": "Tasks - premium test 6", "lesson_files_-_premium_test_5": "Lesson files - premium test 5", "lesson_files_-_premium_test_6": "Lesson files - premium test 6", "sort_task": "Sort task", "list_of_task": "List of task", "task_have_been_sorted": "Task have been sorted", "tasks_-_premium_test_7": "Tasks - premium test 7", "quiz_sort": "Quiz sort", "lesson_files_-_premium_test_7": "Lesson files - premium test 7", "tasks_-_premium_test_8": "Tasks - premium test 8", "lesson_files_-_premium_test_8": "Lesson files - premium test 8", "tasks_-_premium_test_9": "Tasks - premium test 9", "lesson_files_-_grammar_for_oet": "Lesson files - grammar for oet", "tasks_-_grammar_for_oet": "Tasks - grammar for oet", "lesson_files_-_premium_test_9": "Lesson files - premium test 9", "tasks_-_premium_test_10": "Tasks - premium test 10", "tasks_-_premium_test_13": "Tasks - premium test 13", "lesson_files_-_premium_test_10": "Lesson files - premium test 10", "tasks_-_premium_test_11": "Tasks - premium test 11", "lesson_files_-_premium_test_11": "Lesson files - premium test 11", "tasks_-_premium_test_12": "Tasks - premium test 12", "lesson_files_-_premium_test_12": "Lesson files - premium test 12", "lesson_files_-_premium_test_13": "Lesson files - premium test 13", "tasks_-_premium_test_14": "Tasks - premium test 14", "lesson_files_-_premium_test_14": "Lesson files - premium test 14", "tasks_-_premium_test_15": "Tasks - premium test 15", "lesson_files_-_premium_test_15": "Lesson files - premium test 15", "tasks_-_premium_test_16": "Tasks - premium test 16", "lesson_files_-_premium_test_16": "Lesson files - premium test 16", "tasks_-_premium_test_17": "Tasks - premium test 17", "lesson_files_-_premium_test_17": "Lesson files - premium test 17", "tasks_-_premium_test_18": "Tasks - premium test 18", "lesson_files_-_premium_test_18": "Lesson files - premium test 18", "tasks_-_premium_test_19": "Tasks - premium test 19", "lesson_files_-_premium_test_19": "Lesson files - premium test 19", "tasks_-_premium_test_20": "Tasks - premium test 20", "lesson_files_-_premium_test_20": "Lesson files - premium test 20", "tasks_-_premium_test_30": "Tasks - premium test 30", "lesson_files_-_test_lesson": "Lesson files - test lesson", "tasks_-_test_lesson": "Tasks - test lesson", "lesson_files_-_test_lessons": "Lesson files - test lessons", "tasks_-_test_lessons": "Tasks - test lessons", "lesson_files_-_introduction_to_listening": "Lesson files - introduction to listening", "section_have_been_sorted": "Section have been sorted", "lesson_files_-_neurology_1": "Lesson files - neurology 1", "tasks_-_introduction_to_listening": "Tasks - introduction to listening", "lesson_files_-_neurology_": "Lesson files - neurology ", "lesson_files_-_introduction_to_reading": "Lesson files - introduction to reading", "lesson_files_-_introduction_to_writing": "Lesson files - introduction to writing", "lesson_files_-_introduction_to_speaking": "Lesson files - introduction to speaking", "lesson_files_-_cardiology_": "Lesson files - cardiology ", "lesson_files_-_respiratory": "Lesson files - respiratory", "lesson_files_-_test": "Lesson files - test", "live_class_type": "Live class type", "tasks_-_introduction_to_writing": "Tasks - introduction to writing", "tasks_-_practice_test_1": "Tasks - practice test 1", "tasks_-_practice_test_2": "Tasks - practice test 2", "tasks_-_practice_test_3": "Tasks - practice test 3", "tasks_-_practice_test_4": "Tasks - practice test 4", "tasks_-_practice_test_5": "Tasks - practice test 5", "tasks_-_premium_test_21": "Tasks - premium test 21", "tasks_-_practice_test_6": "Tasks - practice test 6", "tasks_-_practice_test_7": "Tasks - practice test 7", "tasks_-_practice_test_8": "Tasks - practice test 8", "tasks_-_practice_test_9": "Tasks - practice test 9", "tasks_-_intermediate_practice": "Tasks - intermediate practice", "lesson_files_-_grammar_for_ielts": "Lesson files - grammar for ielts", "lesson_files_-_introduction": "Lesson files - introduction", "lesson_files_-_german_-_a1": "Lesson files - german - a1", "tasks_-_premium_test_22": "Tasks - premium test 22", "tasks_-_premium_test_23": "Tasks - premium test 23", "tasks_-_premium_test_24": "Tasks - premium test 24", "tasks_-_premium_test_25": "Tasks - premium test 25", "lesson_files_-_the_code,_news,_and_acvpu_scale": "Lesson files - the code, news, and acvpu scale", "lesson_files_-_waterlow_score,_denial_&amp;_collusion,_vip_score,_negative_pressure_dressing": "Lesson files - waterlow score, denial &amp; collusion, vip score, negative pressure dressing", "lesson_files_-_the_yellow_card_scheme_and_patient_safety,_pca,_administration_of_medication": "Lesson files - the yellow card scheme and patient safety, pca, administration of medication", "lesson_files_-_five_stages_-_dabda": "Lesson files - five stages - dabda", "lesson_files_-_calculations": "Lesson files - calculations", "lesson_files_-_universal_precutions,_cohorting,_life_cycle_viruses": "Lesson files - universal precutions, cohorting, life cycle viruses", "lesson_files_-_management": "Lesson files - management", "lesson_files_-_phases_of_wound_healing,_medications": "Lesson files - phases of wound healing, medications", "lesson_files_-_controlled_medicines,_type_of_blood_components": "Lesson files - controlled medicines, type of blood components", "lesson_files_-_antidotes_etc": "Lesson files - antidotes etc", "tasks_-_practice_test_10": "Tasks - practice test 10", "tasks_-_practice_test_15": "Tasks - practice test 15", "enrolments": "Enrolments", "enquiry": "Enquiry", "contacts": "Contacts", "select_counselor": "Select counselor", "package_amount": "Package amount", "student_details": "Student details", "student_overview": "Student overview", "extend_package": "Extend package", "expiry_date": "Expiry date", "date_updated_successfully!": "Date updated successfully!", "tasks_-_practice_test_11": "Tasks - practice test 11", "tasks_-_practice_test_12": "Tasks - practice test 12", "tasks_-_practice_test_14": "Tasks - practice test 14", "tasks_-_premium_test_26": "Tasks - premium test 26", "tasks_-_practice_test_16": "Tasks - practice test 16", "tasks_-_practice_test_13": "Tasks - practice test 13", "tasks_-_practice_test_17": "Tasks - practice test 17", "tasks_-_practice_test_18": "Tasks - practice test 18", "tasks_-_practice_test_19": "Tasks - practice test 19", "tasks_-_practice_test_20": "Tasks - practice test 20", "tasks_-_practice_test_21": "Tasks - practice test 21", "tasks_-_practice_test_22": "Tasks - practice test 22", "tasks_-_premium_test_27": "Tasks - premium test 27", "tasks_-_premium_test_28": "Tasks - premium test 28", "tasks_-_premium_test_29": "Tasks - premium test 29", "is_batch_type": "Is batch type", "batches_-_exam_batch_": "Batches - exam batch ", "batch_added_successfully": "<PERSON><PERSON> added successfully", "updated_successfully!": "Updated successfully!", "batch_lessons_-_exam_batch_": "Batch lessons - exam batch ", "lesson_files_-_example_title": "Lesson files - example title", "back_to_lessons": "Back to lessons", "back_to_courses": "Back to courses", "tasks_-_example_title": "Tasks - example title", "batches]_-_exam_batch_": "Batches] - exam batch ", "add_batch_student": "Add batch student", "add_batch_lesson": "Add batch lesson", "batches]_-_medicine": "Batches] - medicine", "add_batch_students": "Add batch students", "add_students": "Add students", "add_batch_lesson_schedule": "Add batch lesson schedule", "add_lesson_schedule": "Add lesson schedule", "add": "Add", "lesson_added_successfully": "Lesson added successfully", "student_added_successfully": "Student added successfully", "edit_lesson_schedule": "Edit lesson schedule", "transfer_lesson": "Transfer lesson", "tasks_-_introduction_to_reading": "Tasks - introduction to reading", "tasks_-_introduction_to_speaking": "Tasks - introduction to speaking", "transfered_successfully": "Transfered successfully", "batches_-_exam_batch": "Batches - exam batch", "batch_lessons_-_exam_batch": "Batch lessons - exam batch", "batches_-_oet_exam_batch": "Batches - oet exam batch", "batch_lessons_-_oet_exam_batch": "Batch lessons - oet exam batch", "lesson_files_-_intermediate_practice": "Lesson files - intermediate practice", "lesson_files_-_introduction_": "Lesson files - introduction ", "tasks_-_practice_test_23": "Tasks - practice test 23", "tasks_-_practice_test_24": "Tasks - practice test 24", "tasks_-_practice_test_25": "Tasks - practice test 25", "tasks_-_practice_test_26": "Tasks - practice test 26", "tasks_-_practice_test_27": "Tasks - practice test 27", "batch_lessons_-_": "Batch lessons - ", "back_to_lesson": "Back to lesson", "sort_videos": "Sort videos", "videos_have_been_sorted": "Videos have been sorted", "enter_zoom_id": "Enter zoom id", "enter_zoom_password": "Enter zoom password", "lesson_files_-_garmmar_for_oet": "Lesson files - garmmar for oet", "tasks_-_introduction_to_oet": "Tasks - introduction to oet", "lesson_files_-_introduction_to_oet": "Lesson files - introduction to oet", "batch_lessons_-_oet_regular_batch": "Batch lessons - oet regular batch", "batches_-_oet_regular_batch": "Batches - oet regular batch", "tasks_-_practice_test_28": "Tasks - practice test 28", "tasks_-_antidotes_etc": "Tasks - antidotes etc", "batches_-_nursing": "Batches - nursing", "batch_lessons_-_general": "Batch lessons - general", "transfer_course": "Transfer course", "tasks_-_neurology": "Tasks - neurology", "lesson_files_-_premium_test_29": "Lesson files - premium test 29", "tasks_-_neurology_": "Tasks - neurology ", "tasks_-_introduction": "Tasks - introduction", "user_type": "User type", "add_user_type": "Add user type", "edit_user_type": "Edit user type", "select_a_user_type": "Select a user type", "lesson_files_-_testing": "Lesson files - testing", "lesson_files_-_lesson_demo": "Lesson files - lesson demo", "lesson_files_-_deo3": "Lesson files - deo3", "bulk_upload_students": "Bulk upload students", "student_bulk_upload": "Student bulk upload", "lesson_files_-_demolesson": "Lesson files - demolesson", "lesson_files_-_sample": "Lesson files - sample", "lesson_files_-_demo3lesson": "Lesson files - demo3lesson", "student_upload": "Student upload", "lesson_files_-_day1": "Lesson files - day1", "lesson_files_-_day45": "Lesson files - day45", "add_topic": "Add topic", "provide_a_topic_name": "Provide a topic name", "topic": "Topic", "update_topic": "Update topic", "edit_topic": "Edit topic", "delete_topic": "Delete topic", "lesson_files_-_lesson_1": "Lesson files - lesson 1", "list_of_lesson": "List of lesson", "banners": "Banners", "lesson_files_-_lessondemo": "Lesson files - lessondemo", "lesson_files_-_demolesson1": "Lesson files - demolesson1", "lesson_files_-_demolesson2": "Lesson files - demolesson2", "lesson_files_-_integrated_circute": "Lesson files - integrated circute", "lesson_files_-_diode": "Lesson files - diode", "lesson_files_-_motherboard": "Lesson files - motherboard", "lesson_files_-_processor": "Lesson files - processor", "lesson_files_-_resister": "Lesson files - resister", "lesson_files_-_new_lesson": "Lesson files - new lesson", "batches_-_diploma_in_hospital_administration": "Batches - diploma in hospital administration", "batch_lessons_-_diploma_in_hospital_administration": "Batch lessons - diploma in hospital administration", "lesson_files_-_test_less_ma": "Lesson files - test less ma", "batches_-_language_training": "Batches - language training", "batch_lessons_-_language_training": "Batch lessons - language training", "batch_lessons_-_softskills": "Batch lessons - softskills", "lesson_files_-_lessom1": "Lesson files - lessom1", "lesson_files_-_lessonnnn": "Lesson files - lessonnnn", "lesson_files_-_demo_lesson2": "Lesson files - demo lesson2", "lesson_files_-_rrrr": "Lesson files - rrrr", "lesson_files_-_topic_dem2": "Lesson files - topic dem2", "lesson_files_-_test_material": "Lesson files - test material", "lesson_files_-_test_lesson_for_material": "Lesson files - test lesson for material", "lesson_files_-_file1": "Lesson files - file1", "lesson_files_-_lesson2": "Lesson files - lesson2", "lesson_files_-_topic2_lesson": "Lesson files - topic2 lesson", "lesson_files_-_lesson1": "Lesson files - lesson1", "lesson_files_-_lesson3": "Lesson files - lesson3", "pont_details": "Pont details", "pont_settings": "Pont settings", "registration": "Registration", "video_watched": "Video watched", "material_watched": "Material watched", "lesson_completion": "Lesson completion", "quiz_attended": "Quiz attended", "live_class_attended": "Live class attended", "point_details_settings": "Point details settings", "ponit_settings": "Ponit settings", "profile_completion": "Profile completion", "select_lesson": "Select lesson", "lesson_files_-_lesson5": "Lesson files - lesson5", "lesson_files_-_lesson4": "Lesson files - lesson4", "lesson_files_-_lesson6": "Lesson files - lesson6", "lesson_files_-_pdf_premium": "Lesson files - pdf premium", "lesson_files_-_lesson10": "Lesson files - lesson10", "batches_-_test_course": "Batches - test course", "lesson_files_-_training_lesson": "Lesson files - training lesson", "lesson_files_-_training_lesson_2": "Lesson files - training lesson 2", "batches_-_personal_training": "Batches - personal training", "batch_lessons_-_personal_training": "Batch lessons - personal training", "lesson_files_-_example_premium": "Lesson files - example premium", "practice": "Practice", "notes": "Notes", "lesson_files_-_": "Lesson files - ", "lesson_files_-_module_1": "Lesson files - module 1", "lesson_files_-_module_2": "Lesson files - module 2", "lesson_files_-_module11": "Lesson files - module11", "lesson_files_-_module_3": "Lesson files - module 3", "lesson_files_-_video": "Lesson files - video", "lesson_files_-_pdf": "Lesson files - pdf", "lesson_files_-_dummy": "Lesson files - dummy", "lesson_files_-_module4": "Lesson files - module4", "lesson_files_-_module_5": "Lesson files - module 5", "lesson_files_-_module_6": "Lesson files - module 6", "lesson_files_-_module_7": "Lesson files - module 7", "lesson_files_-_module_8": "Lesson files - module 8", "lesson_files_-_module_9": "Lesson files - module 9", "lesson_files_-_module_10": "Lesson files - module 10", "lesson_files_-_module_11": "Lesson files - module 11", "lesson_files_-_module_12": "Lesson files - module 12", "lesson_files_-_module_13": "Lesson files - module 13", "batch_lessons_-_demo": "Batch lessons - demo", "cards": "Cards", "course_card": "Course card", "requirement": "Requirement", "card": "Card", "lesson_files_-_test2": "Lesson files - test2", "lesson_files_-_test_3": "Lesson files - test 3", "lesson_files_-_test_4": "Lesson files - test 4", "lesson_files_-_test_5": "Lesson files - test 5", "add_course_card": "Add course card", "course_card_form": "Course card form", "points": "Points", "course_card_added_successfully": "Course card added successfully", "server_error": "Server error", "lesson_files_-_module2": "Lesson files - module2", "points_settings": "Points settings", "global_settings": "Global settings", "modeule_wise_ponit_settings": "Modeule wise ponit settings", "modeule_wise_ponits": "Modeule wise ponits", "module_wise_ponits": "Module wise ponits", "global_ponts": "Global ponts", "module_wise_ponts": "Module wise ponts", "module_wise_points": "Module wise points", "global_points": "Global points", "lesson_files_-_lesson_2": "Lesson files - lesson 2", "batches_-_test": "Batches - test", "activity_type": "Activity type", "record": "Record", "write": "Write", "upload": "Upload", "select_type_of_activity": "Select type of activity", "student_history": "Student history", "module_category": "Module category", "add_module_category": "Add module category", "activity_attended": "Activity attended", "user_module_status_report": "User module status report", "total_modules": "Total modules", "completed_modules": "Completed modules", "progress": "Progress", "wallet_balance_report": "Wallet balance report", "total_points": "Total points", "total_used": "Total used", "wallet_validity_report": "Wallet validity report", "wallet_expiry_settings": "Wallet expiry settings", "module_details": "Module details", "lesson_files_-_daily_task": "Lesson files - daily task", "lesson_files_-_dalily_task": "Lesson files - dalily task", "activity_file": "Activity file", "activity_report": "Activity report", "activity_records": "Activity records", "enroled_students": "Enroled students", "activity_reports": "Activity reports", "activity_name": "Activity name", "user_activity_title": "User activity title", "lesson_name": "Lesson name", "point_expiry_duration": "Point expiry duration", "redeem_cash_limit_settings": "Redeem cash limit settings", "redeem_cash_limit": "Redeem cash limit", "lesson_files_-_module10": "Lesson files - module10", "lesson_files_-_module_4": "Lesson files - module 4", "wallet_expiry": "Wallet expiry", "details": "Details", "expired": "Expired", "lesson_files_-_testing_lesson": "Lesson files - testing lesson", "point_settings": "Point settings", "daily_task_already_added_to_this_section": "Daily task already added to this section", "sort_category": "Sort category", "list_of_category": "List of category", "category_have_been_sorted": "Category have been sorted", "news": "News", "add_news": "Add news", "lesson_files_-_introduction_to_hypnotism_": "Lesson files - introduction to hypnotism ", "lesson_files_-_chapter_1": "Lesson files - chapter 1", "lesson_files_-_basic_hypnotism": "Lesson files - basic hypnotism", "lesson_files_-_relaxation_technique": "Lesson files - relaxation technique", "lesson_files_-_self-hypnosis": "Lesson files - self-hypnosis", "lesson_files_-_sensory_words": "Lesson files - sensory words", "lesson_files_-_suggestibility_tests": "Lesson files - suggestibility tests", "prefered_language": "Prefered language", "select_a_language": "Select a language", "skill_level": "Skill level", "select_skill_level": "Select skill level", "lesson_files_-_am_self_introduction": "Lesson files - am self introduction", "total_expired": "Total expired", "lesson_files_-_lesson_2l": "Lesson files - lesson 2l", "lesson_files_-_introduction_to_transformers_and_hugging_face": "Lesson files - introduction to transformers and hugging face", "lesson_files_-_text_summarization_techniques": "Lesson files - text summarization techniques", "lesson_files_-_question_answering_with_transformers": "Lesson files - question answering with transformers", "lesson_files_-_named_entity_recognition_(ner)_and_sequence_tagging": "Lesson files - named entity recognition (ner) and sequence tagging", "lesson_files_-_lessonn2": "Lesson files - lessonn2", "lesson_files_-_lesson_3": "Lesson files - lesson 3", "lesson_files_-_1": "Lesson files - 1", "lesson_files_-_lesson_4": "Lesson files - lesson 4", "lesson_files_-_task": "Lesson files - task", "habit_category": "Habit category", "add_habit_category": "Add habit category", "icon": "Icon", "choose_icon": "Choose icon", "habit_category_added_successfully": "Habit category added successfully", "habit": "Habit", "add_habit": "Add habit", "habit_added_successfully": "Habit added successfully", "select_habit": "Select habit", "time_period": "Time period", "include_in_level": "Include in level", "have_up_level_course": "Have up level course", "habits": "Habits", "batch_lessons_-_nlp_beginner": "Batch lessons - nlp beginner", "global__points_settings": "Global  points settings", "batches_-_nlp_beginner": "Batches - nlp beginner", "lesson_files_-_history": "Lesson files - history", "batches_-_test_course_1": "Batches - test course 1", "batch_lessons_-_test_course_1": "Batch lessons - test course 1", "lesson_files_-_lesson_5": "Lesson files - lesson 5", "lesson_files_-_task_1": "Lesson files - task 1", "select_a_$category": "Select a $category", "lesson_files_-_module_01": "Lesson files - module 01", "lesson_files_-_class_01": "Lesson files - class 01", "lesson_files_-_class_02": "Lesson files - class 02", "lesson_files_-_class_3._active_in_class_and_home": "Lesson files - class 3. active in class and home", "lesson_files_-_class_4._not_taking_&amp;_note_making": "Lesson files - class 4. not taking &amp; note making", "lesson_files_-_class_5._boosting_memory_mnemonics_1": "Lesson files - class 5. boosting memory mnemonics 1", "lesson_files_-_class_6._boosting_memory_mnemonics_2": "Lesson files - class 6. boosting memory mnemonics 2", "lesson_files_-_class_7._linking_method_story_presidents": "Lesson files - class 7. linking method story presidents", "lesson_files_-_class_8._linking_methods_prime_minister": "Lesson files - class 8. linking methods prime minister", "lesson_files_-_class_9._body_location_method": "Lesson files - class 9. body location method", "lesson_files_-_class_10._location_method_to_learn_faster": "Lesson files - class 10. location method to learn faster", "lesson_files_-_class_11._peg_system_&amp;#40;sun_list&amp;#41;": "Lesson files - class 11. peg system &amp;#40;sun list&amp;#41;", "lesson_files_-_class_12._home_work_effectiveness_part_1": "Lesson files - class 12. home work effectiveness part 1", "lesson_files_-_class_13._home_work_effectiveness_part_2": "Lesson files - class 13. home work effectiveness part 2", "lesson_files_-_class_14._exam_preparation_technique_part_1": "Lesson files - class 14. exam preparation technique part 1", "lesson_files_-_class_15._exam_preparation_&amp;_exam_fear_part_2": "Lesson files - class 15. exam preparation &amp; exam fear part 2", "lesson_files_-_class_16._tips_for_a_nice_answer_sheet": "Lesson files - class 16. tips for a nice answer sheet", "lesson_files_-_class_17._brain_gym_activities_1": "Lesson files - class 17. brain gym activities 1", "lesson_files_-_class_18._brain_gym_activities_2": "Lesson files - class 18. brain gym activities 2", "lesson_files_-_class_19._brain_gym_activities_3": "Lesson files - class 19. brain gym activities 3", "lesson_files_-_1._fast_learning_method:_an_introduction_": "Lesson files - 1. fast learning method: an introduction ", "lesson_files_-_2._how_to_use_this_course": "Lesson files - 2. how to use this course", "lesson_files_-_3._learn_anything_faster": "Lesson files - 3. learn anything faster", "lesson_files_-_4._be_an_active_student": "Lesson files - 4. be an active student", "lesson_files_-_5._effective_note_making_strategy_": "Lesson files - 5. effective note making strategy ", "lesson_files_-_6._boost_your_memory_p1": "Lesson files - 6. boost your memory p1", "lesson_files_-_7._boost_your_memory_p2": "Lesson files - 7. boost your memory p2", "lesson_files_-_8._linking_method_to_study_faster_p1": "Lesson files - 8. linking method to study faster p1", "lesson_files_-_9._linking_method_to_study_faster_p2": "Lesson files - 9. linking method to study faster p2", "lesson_files_-_10._body_location_method_to_study_faster": "Lesson files - 10. body location method to study faster", "lesson_files_-_11._location_method_to_study_faster_": "Lesson files - 11. location method to study faster ", "lesson_files_-_12._peg_system_to_improve_memory_": "Lesson files - 12. peg system to improve memory ", "lesson_files_-_13._homework_strategy_p1": "Lesson files - 13. homework strategy p1", "lesson_files_-_14._homework_strategy_p2": "Lesson files - 14. homework strategy p2", "lesson_files_-_15._exam_winning_technique_p1": "Lesson files - 15. exam winning technique p1", "lesson_files_-_16._exam_winning_technique_p2": "Lesson files - 16. exam winning technique p2", "lesson_files_-_17._scoring_good_marks_in_exam": "Lesson files - 17. scoring good marks in exam", "lesson_files_-_18._brain_gym_activity_p1": "Lesson files - 18. brain gym activity p1", "lesson_files_-_19._brain_gym_activity_p2": "Lesson files - 19. brain gym activity p2", "lesson_files_-_20._brain_gym_activity_p3": "Lesson files - 20. brain gym activity p3", "show_lock": "Show lock", "lesson_files_-_4_tear_effect": "Lesson files - 4 tear effect", "lesson_files_-_finger_drop": "Lesson files - finger drop", "lesson_files_-_true_or_lie": "Lesson files - true or lie", "lesson_files_-_mirroring_image": "Lesson files - mirroring image", "lesson_files_-_invisible_playing_cards": "Lesson files - invisible playing cards", "lesson_files_-_elimination": "Lesson files - elimination", "lesson_files_-_numerical_book_reading_bonus_tip_": "Lesson files - numerical book reading bonus tip ", "lesson_files_-_mentalism_intro": "Lesson files - mentalism intro", "is_life_time_course": "Is life time course", "lesson_files_-_numerical_book_reading_(_bonus_tip_)": "Lesson files - numerical book reading ( bonus tip )", "lesson_files_-_1_to_100": "Lesson files - 1 to 100", "lesson_files_-_swengali_pad": "Lesson files - swengali pad", "lesson_files_-_psychic_key": "Lesson files - psychic key", "lesson_files_-_one_to_six_and_lott": "Lesson files - one to six and lott", "lesson_files_-_invisible_touch": "Lesson files - invisible touch", "lesson_files_-_four_tear": "Lesson files - four tear", "lesson_files_-_forcing_calculator": "Lesson files - forcing calculator", "lesson_files_-_epic_book": "Lesson files - epic book", "lesson_files_-_center_tear": "Lesson files - center tear", "lesson_files_-_30_cards": "Lesson files - 30 cards", "minimum_package_amount": "Minimum package amount", "lesson_files_-_coin_card": "Lesson files - coin card", "lesson_files_-_celebrity_farsana": "Lesson files - celebrity farsana", "lesson_files_-_any_book_forcing": "Lesson files - any book forcing", "lesson_files_-_any_book_any_word": "Lesson files - any book any word", "lesson_files_-_1_to_100_numerical_trick": "Lesson files - 1 to 100 numerical trick", "lesson_files_-_invisible_heal": "Lesson files - invisible heal", "purchase_report": "Purchase report", "select_payment_type": "Select payment type", "select_purchase_status": "Select purchase status", "select": "Select", "registered_date": "Registered date", "purchase_status": "Purchase status", "package_name": "Package name", "payment_method": "Payment method", "payment_date": "Payment date", "amount_paid": "Amount paid", "payment_from": "Payment from", "course_progress": "Course progress", "cash_limit_settings": "Cash limit settings", "minimum_package_amount_to_apply_coin": "Minimum package amount to apply coin", "coin_redeem_cash_limit": "Coin redeem cash limit", "lesson_files_-_celebrity_card": "Lesson files - celebrity card", "total_payments": "Total payments", "today_payments": "Today payments", "today_amount": "Today amount", "lesson_files_-_seven_of_heap_": "Lesson files - seven of heap ", "lesson_files_-_heart_beet": "Lesson files - heart beet", "enrolled_not_purchased": "Enrolled not purchased", "registered_not_enrolled": "Registered not enrolled", "lesson_files_-_mobile_password": "Lesson files - mobile password", "registered_not_purchased": "Registered not purchased", "purchase_expired": "Purchase expired", "today_purchase_expiring": "Today purchase expiring", "today_enrolments": "Today enrolments", "lesson_files_-_marked_card": "Lesson files - marked card", "lesson_files_-_eye_blind_effect": "Lesson files - eye blind effect", "lesson_files_-_currency_serial_number": "Lesson files - currency serial number", "lesson_files_-_invisible_heat": "Lesson files - invisible heat", "add_reels": "Add reels", "reels": "<PERSON><PERSON>", "reel_edit": "<PERSON><PERSON> edit", "show_anwers": "Show anwers", "notification_for": "Notification for", "today_regitrations": "Today regitrations", "lesson_files_-_add_on": "Lesson files - add on", "lesson_files_-_tele_kynesthetic_3_effects.": "Lesson files - tele kynesthetic 3 effects.", "lesson_files_-_coin_heat_": "Lesson files - coin heat ", "lesson_files_-_digital_forcing_bang_": "Lesson files - digital forcing bang ", "not_enrolled_students": "Not enrolled students", "not_purchased_students": "Not purchased students", "enrolled_-_not_purchased_students": "Enrolled - not purchased students", "registered_-_not_purchased_students": "Registered - not purchased students", "today": "Today", "this_month": "This month", "overview": "Overview", "user_insights_report": "User insights report", "sales_admins": "Sales admins", "add_sales_admin": "Add sales admin", "sales_admin_edit": "Sales admin edit", "sales_admin": "Sales admin", "discount_amount%": "Discount amount%", "discount_amount": "Discount amount", "lesson_files_-_4_effect": "Lesson files - 4 effect", "lesson_files_-_elimination_video_": "Lesson files - elimination video ", "lesson_files_-_digital_forcing_bag_": "Lesson files - digital forcing bag ", "lesson_files_-_invisible_thread": "Lesson files - invisible thread", "lesson_files_-_routine_examples": "Lesson files - routine examples", "lesson_files_-_happy_or_sad": "Lesson files - happy or sad", "lesson_files_-_pro_thumber_": "Lesson files - pro thumber ", "lesson_files_-_mobile_mentalism_": "Lesson files - mobile mentalism ", "lesson_files_-_pre_show": "Lesson files - pre show", "lesson_files_-_swami_gimmick": "Lesson files - swami gimmick", "lesson_files_-_pre_predictions": "Lesson files - pre predictions", "lesson_files_-_3_effects": "Lesson files - 3 effects", "lesson_files_-_real_mentalism_reveal": "Lesson files - real mentalism reveal", "course_completed_students_-_": "Course completed students - ", "course_completed_students_-_premium_-_mentalism": "Course completed students - premium - mentalism", "course_completed_students": "Course completed students", "course_completed_students_-_mentalism": "Course completed students - mentalism", "course_completed_students_-_smart_study_habits_&amp;_tricks": "Course completed students - smart study habits &amp; tricks", "vimeo_url": "Vimeo url", "manual_payment_amount": "Manual payment amount", "razourpay_amount": "Razourpay amount", "razourpay_payments": "Razourpay payments", "manual_payments": "Manual payments", "razorpay_payments": "Razorpay payments", "feel_title": "Feel title", "lesson_files_-_mentalism_demo": "Lesson files - mentalism demo", "course_exam": "Course exam", "course_exam_list": "Course exam list", "add_course_exam": "Add course exam", "exam_title": "Exam title", "course_exam_edit": "Course exam edit", "exam_questions": "Exam questions", "question_edit": "Question edit", "lesson_files_-_5_happy_news_about_public_speaking_fear.": "Lesson files - 5 happy news about public speaking fear.", "lesson_files_-_why_do_you_want_to_become_a_public_speaker?": "Lesson files - why do you want to become a public speaker?", "lesson_files_-_why_fear?": "Lesson files - why fear?", "lesson_files_-_what_is_the_science_behind_public_speaking?_": "Lesson files - what is the science behind public speaking? ", "lesson_files_-_how_can_anyone_overcome_it?": "Lesson files - how can anyone overcome it?", "lesson_files_-_why_you_can?": "Lesson files - why you can?", "lesson_files_-_steps_to_wire_stage_fear": "Lesson files - steps to wire stage fear", "lesson_files_-_how_rewire?": "Lesson files - how rewire?", "lesson_files_-_psychological_tips_1": "Lesson files - psychological tips 1", "lesson_files_-_psychological_tips_2": "Lesson files - psychological tips 2", "lesson_files_-_psychological_tips_3": "Lesson files - psychological tips 3", "lesson_files_-_tips and tricks": "Lesson files - tips and tricks", "lesson_files_-_power_of_non-verbal_communication_": "Lesson files - power of non-verbal communication ", "lesson_files_-_body_language_": "Lesson files - body language ", "lesson_files_-_facial_expression_": "Lesson files - facial expression ", "lesson_files_-_power_of_gestures_": "Lesson files - power of gestures ", "lesson_files_-_voice_tone_and_modulation_": "Lesson files - voice tone and modulation ", "lesson_files_-_body_movement_": "Lesson files - body movement ", "lesson_files_-_how_to_start_a_speech": "Lesson files - how to start a speech", "lesson_files_-_powerful_start": "Lesson files - powerful start", "lesson_files_-_body_of_the_speech": "Lesson files - body of the speech", "lesson_files_-_concluding the speech": "Lesson files - concluding the speech", "auto_notifications": "Auto notifications", "add_auto_notification": "Add auto notification", "select_day": "Select day", "update_new_nofication": "Update new nofication", "update_auto_notification": "Update auto notification", "update_notification": "Update notification", "lesson_files_-_2._why_you_want_to_be_a_public_speaker?": "Lesson files - 2. why you want to be a public speaker?", "lesson_files_-_4._what_is_the_science_behind_public_speaking_fear_?_": "Lesson files - 4. what is the science behind public speaking fear ? ", "lesson_files_-_5._how_can_anyone_overcome_stage_fear_?": "Lesson files - 5. how can anyone overcome stage fear ?", "lesson_files_-_7._steps_to_wire_public_speaking_fear_in_your_brain": "Lesson files - 7. steps to wire public speaking fear in your brain", "lesson_files_-_8._how_rewire?": "Lesson files - 8. how rewire?", "lesson_files_-_9._psychological_tips_to_overcome_public_speaking_fear_1": "Lesson files - 9. psychological tips to overcome public speaking fear 1", "lesson_files_-_10._psychological_tips_to_overcome_public_speaking_fear_2": "Lesson files - 10. psychological tips to overcome public speaking fear 2", "lesson_files_-_11._psychological_tips_to_overcome_public_speaking_fear_3": "Lesson files - 11. psychological tips to overcome public speaking fear 3", "lesson_files_-_12._instant_tips_and_tricks_to_build_confidence": "Lesson files - 12. instant tips and tricks to build confidence", "lesson_files_-_1._5_happy_news_about_public_speaking_fear.": "Lesson files - 1. 5 happy news about public speaking fear.", "lesson_files_-_3._why_fear?": "Lesson files - 3. why fear?", "lesson_files_-_6._why_you_can?": "Lesson files - 6. why you can?", "lesson_files_-_13._power_of_non-verbal_communication_": "Lesson files - 13. power of non-verbal communication ", "lesson_files_-_14._body_language_": "Lesson files - 14. body language ", "lesson_files_-_15._facial_expression_": "Lesson files - 15. facial expression ", "lesson_files_-_16._power_of_gestures_": "Lesson files - 16. power of gestures ", "lesson_files_-_17._voice_tone_and_modulation_": "Lesson files - 17. voice tone and modulation ", "lesson_files_-_18._body_movement_": "Lesson files - 18. body movement ", "lesson_files_-_19._how_to_start_a_speech": "Lesson files - 19. how to start a speech", "lesson_files_-_20._powerful_start": "Lesson files - 20. powerful start", "lesson_files_-_21._body_of_the_speech": "Lesson files - 21. body of the speech", "lesson_files_-_22._concluding_the_speech": "Lesson files - 22. concluding the speech", "lesson_files_-_daily_task_": "Lesson files - daily task ", "lesson_files_-_week_1": "Lesson files - week 1", "lesson_files_-_week_1_": "Lesson files - week 1 ", "lesson_files_-_day_1": "Lesson files - day 1", "lesson_files_-_week_2": "Lesson files - week 2", "lesson_files_-_week_3": "Lesson files - week 3", "lesson_files_-_week_4_": "Lesson files - week 4 ", "lesson_files_-_week_4": "Lesson files - week 4", "transfer_to_courses": "Transfer to courses", "batch_lessons_-_batch_course": "Batch lessons - batch course", "batches_-_batch_course": "Batches - batch course", "send_type": "Send type", "batches_-_test_cours_e": "Batches - test cours e", "lesson_files_-_1_to_9": "Lesson files - 1 to 9", "batches_-_premium_30_days_online_public_speaking_mastery_": "Batches - premium 30 days online public speaking mastery ", "course_completed_students_-_30_days_online_public_speaking_mastery_": "Course completed students - 30 days online public speaking mastery ", "lesson_files_-_intro": "Lesson files - intro", "lesson_files_-_charter_1": "Lesson files - charter 1", "lesson_files_-_what_is_hypnotism": "Lesson files - what is hypnotism", "lesson_files_-_hypnosis_introduction": "Lesson files - hypnosis introduction", "lesson_files_-_welcome_to_hypnotism_": "Lesson files - welcome to hypnotism ", "lesson_files_-_who_is_this_course_for_?": "Lesson files - who is this course for ?", "lesson_files_-_introduction_-_about_the_skillage_app": "Lesson files - introduction - about the skillage app", "lesson_files_-_history_of_hypnosis": "Lesson files - history of hypnosis", "lesson_files_-_how_hypnotism_works": "Lesson files - how hypnotism works", "lesson_files_-_role_of_the_min_in_hypnotism": "Lesson files - role of the min in hypnotism", "lesson_files_-_myths_in_hypnotism": "Lesson files - myths in hypnotism", "lesson_files_-_hypnosis_and_the_brain": "Lesson files - hypnosis and the brain", "lesson_files_-_introduction_to_self_hypnotism_and_mindfulness": "Lesson files - introduction to self hypnotism and mindfulness", "lesson_files_-_meditation_and_relaxation_technique": "Lesson files - meditation and relaxation technique", "lesson_files_-_can_everyone_be_hypnotized_": "Lesson files - can everyone be hypnotized ", "lesson_files_-_suggestibility_test": "Lesson files - suggestibility test", "lesson_files_-_steps_in_hypnotism": "Lesson files - steps in hypnotism", "lesson_files_-_induction": "Lesson files - induction", "lesson_files_-_types_of_induction": "Lesson files - types of induction", "lesson_files_-_how_can_you_hypnotized_anyone": "Lesson files - how can you hypnotized anyone", "lesson_files_-_coin_on_the_hand": "Lesson files - coin on the hand", "lesson_files_-_pedulam_method": "Lesson files - pedulam method", "lesson_files_-_memory_loss": "Lesson files - memory loss", "lesson_files_-_hands_lock": "Lesson files - hands lock", "lesson_files_-_precautions_and_ethics_in_hypnotism_": "Lesson files - precautions and ethics in hypnotism ", "lesson_files_-_who_should_not_be_hypnotized": "Lesson files - who should not be hypnotized", "lesson_files_-_hypnotism_and_age_maters": "Lesson files - hypnotism and age maters", "lesson_files_-_induction_real_example": "Lesson files - induction real example", "lesson_files_-_feedback": "Lesson files - feedback", "lesson_files_-_pendulum_method": "Lesson files - pendulum method", "lesson_files_-_webinar": "Lesson files - webinar", "lesson_files_-_21._maths": "Lesson files - 21. maths", "student_activity_report": "Student activity report", "detailed_student_report_-_abdul_basheer": "Detailed student report - abdul basheer"}