<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Dashboard extends MY_Controller{
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */

    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('package_m');
        $this->load->model('enrol_m');
    }

    public function index() {

        $this->data['page_title']   = 'Dashboard';
        $this->data['page_name']    = 'dashboard/index';
        $this->data['user_details'] = $this->user_m->get(['id' => get_user_id()])->row_array();
        $this->data['user_email']   = $this->data['user_details']['user_email'];
        $this->data['categories']   =  $this->category_m->get_parent_categories();
        $this->load->view('app/index', $this->data);
    }
    
    
    public function save_email(){
        if($_POST){ 
            
            $id = $this->input->post('id');
            $data['user_email'] =    $this->input->post('email');
            $data['whatsapp']   =    $this->input->post('whatsapp');  
            
            $this->db->where('id',$id);
            $this->db->update('users',$data);
            
            $this->session->set_flashdata('success_message', 'Saved successfully!');
        }
        redirect(base_url('app/dashboard/'),'refresh');
    
    }

    /*
     * Logout
     */
    public function logout(){
    if ($this->session && !empty($this->session->userdata('user_id'))) {
        $this->session->sess_destroy();  
    }
    redirect('/login');
}

    
    
}