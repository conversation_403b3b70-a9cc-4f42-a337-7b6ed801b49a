<?php
class Course extends App_Controller{
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */

    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('user_m');
        $this->load->model('instructor_m');
        $this->load->model('student_m');
        $this->load->model('package_m');
        $this->load->model('enrol_m');
        $this->load->model('payment_m');
        $this->load->model('payment_info_m');
        $this->load->model('lesson_m');
        $this->load->model('user_lesson_m'); 
        $this->load->model('lesson_files_m');
        $this->load->model('video_m');
        $this->load->model('tasks_m');
        $this->load->model('quiz_m');
        $this->load->model('liveclass_m');
        $this->load->model('instructor_enrol_m');
        $this->load->model('instructor_students_m');
        $this->load->model('batch_lesson_m');
        $this->load->model('batch_lesson_files_m');
        $this->load->model('batch_tasks_m');
    }
    
    
    
    /* Enrolled Courses */
    public function my_course(){
        // set primary course

        $this->_make_primary_default();
        
        $user = $this->db->get_where('users', ['id' => $this->user_id])->row();
        $course_id          = $user->course_id;
        $this->data['course_details']   = $this->db->get_where('course', ['id' => $course_id])->row_array();
        if($user->role_id==2){
            $this->data['enrolled']    = $this->enrol_m->get_user_enrolled_courses($this->user_id);
            foreach($this->data['enrolled'] as $key=> $enrolled_course){
                $this->data['enrolled'][$key]['course_name'] = $enrolled_course['course_name']. '  ' . $enrolled_course['package_name'];
            }
        }else if($user->role_id==3){
            $this->data['enrolled']    = $this->instructor_enrol_m->get_instructor_enrolled_courses($this->user_id);;
        }
        //$this->data['course_details'] = $this->enrol_m->get_user_enrolled_courses($_SESSION['user_id']);
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['page_name']        = 'course/my_course';
        $this->data['page_title']       = 'My courses';
        $this->load->view('app/index', $this->data);
    }
    
    
    /*Course details*/
    public function course_details($course_slug){
        $this->data['slug']             = $course_slug;
        $this->data['course_details']   = $this->category_m->get(['slug' => $course_slug])->row_array();
        $this->data['courses']          = array_column($this->course_m->get(['category_id' => $this->data['course_details']['id']])->result_array(),'id');
        $this->data['packages']         = $this->package_m->get_package_list($this->data['courses']);
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['page_name']        = 'course/course_details';
        $this->data['page_title']       = 'Course Details';
        $this->load->view('app/index', $this->data);
    }
    
    public function enrol_student(){
        $user_id = $this->user_id;
        $package_id = $this->uri->segment(5);
        $course_id = $this->uri->segment(6);
        $slug = $this->uri->segment(7);
       
        $enrol = $this->enrol_m->enrol_course($user_id, $course_id, $package_id);

        if($enrol==false){
            $this->session->set_flashdata('error', 'You are already enrolled in this course.');
            redirect('app/course/course_details/'.$slug);
        }else if($enrol>0){
            $this->student_m->update(['course_id' => $course_id], ['id' => $user_id]);
            // log_message("error","wfnj ".print_r($this->db->last_query(),true));
            $this->session->set_flashdata('flash_message', 'You have been enrolled in this course.');
            redirect('app/course/course_details/'.$slug);
        }
    }
    
    
    
    //Lesson
    public function lessons_by_course($course_id,$student_id=""){
        $this->data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
        if($this->data['user_details']['role_id']==2){
            $this->data['lessons']      = $this->lesson_m->get_lesson_data($course_id, $this->user_id);
            // log_message("error","dfvkn ".print_r( $this->data['lessons'],true));
            $this->data['page_name']    = 'course/lesson';
        }else if($this->data['user_details']['role_id']==3){
            $this->data['lessons']      = $this->lesson_m->get_instructor_lesson_data($course_id,$student_id,$this->user_id);
            $this->data['page_name']    = 'course/instructor_lesson';
            $this->data['student_id']    = $student_id;
        }
        $this->data['course_name']  = $this->db->get_where('course',['id'=>$course_id])->row()->title;
        $this->data['course_id']    = $course_id;
        $this->data['page_title']   = 'Lessons By Course';
        $this->load->view('app/index', $this->data);
    }
    
    public function lesson_data($course_id,$student_id=""){
        $data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
        $data['lessons'] = $this->batch_lesson_m->get_lesson_data_test($course_id, $this->user_id,$is_premium);
        $data['course_name']  = $this->db->get_where('course',['id'=>$course_id])->row()->title;
        $data['page_name']    = 'course/lesson';
        $data['batch_lesson_files_confirming']  = 1;
        $data['course_id']    = $course_id;
        $data['page_title']   = 'Lessons By Course';
        $this->load->view('app/index', $data);
        // log_message('error','$trainer_course: '.print_r($lessons,true));
    }
    
    //instructor_students
    public function instructor_students($course_id){
        $this->data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['students']     = $this->instructor_students_m->get_instructor_students($this->user_id, $course_id);
        log_message("error","sdfn ".print_r($this->db->last_query(),true));
        $this->data['page_name']    = 'course/instructor_students';
        $this->data['course_name']  = $this->db->get_where('course',['id'=>$course_id])->row()->title;
        $this->data['course_id']    = $course_id;
        $this->data['page_title']   = 'Instructor Course';
        $this->load->view('app/index', $this->data);
    }
    
    
    //create_individual_live_class
    public function create_individual_live_class($student_id,$course_id){
        $this->data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['students']     = $this->instructor_students_m->get_instructor_students($this->user_id, $course_id);
        $this->data['page_name']    = 'course/create_individual_live_class';
        $this->data['course_name']  = $this->db->get_where('course',['id'=>$course_id])->row()->title;
        $this->data['student_id']   = $student_id;
        $this->data['course_id']    = $course_id;
        $this->data['page_title']   = 'Liveclass Add';
        $this->load->view('app/index', $this->data);
    }
    
    //add individual liveclass
    public function add_individual_live_class(){
        log_message("error","222#### ".print_r("dcdc",true));
        $instructor_id = $this->user_id;
        $user = $this->instructor_m->get(['id' => $instructor_id])->row();
        $data = [
            'title'     => $this->input->post('title'),
            'live_type' => 2,
            'student_id'=> $this->input->post('student_id'),
            'zoom_id'   => $user->zoom_id ?? '',
            'password'  => $user->zoom_password ?? '',
            'fromDate'  => date('Y-m-d',strtotime($this->input->post('fromDate'))),
            'toDate'    => date('Y-m-d',strtotime($this->input->post('toDate'))),
            'fromTime'  => date('H:i:s', strtotime($this->input->post('fromTime'))),
            'toTime'    => date('H:i:s', strtotime($this->input->post('toTime'))),
            'course_id' => $user->course_id,
            'role_id'   => 2,
            'created_by' => $instructor_id,
            'created_on' => date('Y-m-d H:i:s'),
        ];
        $insert_id = $this->liveclass_m->insert($data);
        redirect('app/course/instructor_students/'.$user->course_id);
    }
    
    
    // mark lesson as completed
    public function mark_lesson_as_completed(){
        $lesson_id = $this->input->post('lesson_id');
        $this->user_lesson_m->mark_lesson_as_completed_student($lesson_id, $this->user_id);
          // Send a success response
       
    }
    
    public function player($lesson_id){
        $this->data['user_details'] = $this->student_m->get(['id' => $this->user_id])->row_array();
        $this->data['videos'] = $this->lesson_files_m->get_videos_data($lesson_id, $this->user_id);
        $this->data['tasks']  = $this->tasks_m->get_tasks_data($lesson_id, $this->user_id);
        
        $this->data['user_id']          =  $this->user_id;
        $this->data['page_name']        = 'course/player';
        $this->data['page_title']       = 'Lesson Details';
        $this->load->view('app/index', $this->data);
    }
    
    // mark lesson as completed instructor
    public function mark_lesson_as_completed_instructor(){
        $lesson_id = $this->input->post('lesson_id');
        $student_id = $this->input->post('student_id');
        $this->user_lesson_m->mark_lesson_as_completed_instructor($lesson_id, $student_id, $this->user_id);
    }
    
    
    /*Course details*/
    public function lesson_details($lesson_id){
        $this->data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['videos'] = $this->lesson_files_m->get_videos_data($lesson_id, $this->user_id);
        $this->data['tasks']  = $this->tasks_m->get_tasks_data($lesson_id, $this->user_id);
        $this->data['user_id']          =  $this->user_id;
        $this->data['page_name']        = 'course/lesson_details';
        $this->data['page_title']       = 'Lesson Details';
        $this->load->view('app/index', $this->data);
    }
    
    public function batch_lesson_details($lesson_id){
         $data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
         $data['videos'] = $this->batch_lesson_files_m->get_videos_data($lesson_id, $this->user_id);
        //  print_r($data['videos']);die();
         $data['tasks'] = $this->batch_tasks_m->get_tasks_data($lesson_id, $this->user_id);
         $data['user_id']          =  $this->user_id;
         $data['page_name']        = 'course/lesson_details';
         $data['page_title']       = 'Lesson Details';
         $this->load->view('app/index', $data);
    }
    public function lesson_file_view($task_id,$type){
        $this->data['user_details'] = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['tasks']  = $this->tasks_m->get_task_file_data($task_id,$type);
        $this->data['user_id']          =  $this->user_id;
        $this->data['page_name']        = 'course/lesson_files_view';
        $this->data['page_title']       = 'Lesson Details';
        $this->load->view('app/index', $this->data);
    }
    
     public function lesson_file_show(){
        $file = $this->uri->segment('4');
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['file']             = urldecode($file);
        $this->data['user_id']          =  $this->user_id;
        $this->data['page_name']        = 'course/lesson_files_show';
        $this->data['page_title']       = 'Lesson Details';
        $this->load->view('app/index', $this->data);
    }
    
    
    public function package_details($course_id){
        $this->data['packages']         = $this->package_m->get_package_list_data($course_id, $this->user_id);
        $this->data['user_id']          =  $this->user_id;
        $this->data['page_name']        = 'course/package_details';
        $this->data['page_title']       = 'Package Details';
        $this->load->view('app/index', $this->data);
    }
    
    //PACKAGE SPECIFICATIONS
    public function package_specifications($package_id){
        $this->data['specifications']   = $this->package_features_m->get(['package_id' => $package_id])->result_array();
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['course_name']      = $this->course_m->get(['id'=>$course_id])->row()->title;
        $this->data['course_id']        = $course_id;
        $this->data['user_id']          = $this->user_id;
        $this->data['page_name']        = 'course/specification';
        $this->data['page_title']       = 'Package Details';
        $this->load->view('app/index', $this->data);
    }
    
    
    /*Course details*/
    public function lesson_video($video_id){
        $video_url = $this->lesson_files_m->get(['id' => $video_id])->row()->video_url;
        $video_details = $this->video_m->getVideoDetails($video_url);
        $this->data['video_id']         = $video_details['video_id'];
        $this->data['page_name']        = 'course/lesson_video';
        $this->data['page_title']       = 'Lesson Video';
        $this->load->view('app/index', $this->data);
    }
    
    
    ///Switch course
    
    public function switch_course() {
        $userId = $this->user_id;
        $data['course_id'] = $this->input->post('course_id');
        $this->student_m->update($data, ['id' => $userId]);
        
        $message = 'Course successfully changed!';
        $this->session->set_flashdata('success', $message);
    
        // Return a JSON response with the message
        $response = array('status' => 'success', 'message' => $message);
        echo json_encode($response);
    }
    
    
    
    //live class
    public function liveclass_by_course($course_id){
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        if($this->data['user_details']['role_id'] == 2){
            $this->data['liveclasses']   = $this->liveclass_m->get_liveclass_data($course_id, $this->user_id);
            $this->data['page_name']        = 'course/live_classes';
        }if($this->data['user_details']['role_id'] == 3){
            $this->data['liveclasses']  = $this->liveclass_m->zoom_teacher_get($this->user_id, $course_id);
            $this->data['page_name']        = 'course/instructor_live_classes';
        }
        $this->data['course_name']      = $this->course_m->get(['id'=>$course_id])->row()->title;
        $this->data['course_id']        = $course_id;
        $this->data['user_id']          = $this->user_id;
        $this->data['page_title']       = 'Liveclass Details';
        $this->load->view('app/index', $this->data);
    }
    
    
    //live class
    public function practices_by_course($course_id){
        $this->data['practices']        = $this->quiz_m->get_practices_data($course_id,$this->user_id);
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['course_name']      = $this->course_m->get(['id'=>$course_id])->row()->title;
        $this->data['course_id']        = $course_id;
        $this->data['user_id']          = $this->user_id;
        $this->data['page_name']        = 'course/practices';
        $this->data['page_title']       = 'Practice Details';
        $this->load->view('app/index', $this->data);
    }
    //live class
    public function exams_by_course($course_id){
        $this->data['exams']            = $this->quiz_m->get_exam_data($course_id,$this->user_id);
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['course_name']      = $this->course_m->get(['id'=>$course_id])->row()->title;
        $this->data['course_id']        = $course_id;
        $this->data['user_id']          = $this->user_id;
        $this->data['page_name']        = 'course/exams';
        $this->data['page_title']       = 'Exams Details';
        $this->load->view('app/index', $this->data);
    }
    //live class
    public function materials_by_course($course_id){
        $this->data['materials']        = $this->lesson_files_m->get_materials_data($course_id, 0, $this->user_id);
        $this->data['user_details']     = $this->user_m->get(['id' => $this->user_id])->row_array();
        $this->data['course_name']      = $this->course_m->get(['id'=>$course_id])->row()->title;
        $this->data['course_id']        = $course_id;
        $this->data['user_id']          = $this->user_id;
        $this->data['page_name']        = 'course/materials';
        $this->data['page_title']       = 'Materials Details';
        $this->load->view('app/index', $this->data);
    }
    
    public function materials_by_course_view(){
        $file = $this->uri->segment('4');
        $this->data['file']             = urldecode($file);
        $this->data['user_id']          = $this->user_id;
        $this->data['page_name']        = 'course/lesson_files_show';
        $this->data['page_title']       = 'Materials Details';
        $this->load->view('app/index', $this->data);

    }

    private function _make_primary_default(){
        $student = $this->student_m->get(['id' => $this->user_id])->row();

        $enrolled_courses = array_column($this->enrol_m->get(['user_id' => $this->user_id], ['course_id'])->result_array(), 'course_id');

        if (empty($student->course_id) OR !in_array($student->course_id, $enrolled_courses)){
            if (!empty($enrolled_courses)){
                $this->student_m->update(['course_id' => $enrolled_courses[0]]);
            }
        }
    }
    
    

}