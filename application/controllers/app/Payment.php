<?php

use Razorpay\Api\Api;
use Razorpay\Api\Errors\SignatureVerificationError;

class Payment extends App_Controller{
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */

    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('package_m');
        $this->load->model('payment_m');
        $this->load->model('payment_info_m');
        $this->load->model('enrol_m');
        $this->load->model('lesson_m');
        $this->load->model('user_lesson_m'); 
        $this->load->model('lesson_files_m');
        $this->load->model('video_m');
        $this->load->model('tasks_m');
        $this->load->model('quiz_m');
        $this->load->model('liveclass_m');
        $this->load->model('coupon_code_m');
    }
    
    
    //make payment
    public function make_payment()
    {
        ini_set('display_errors', 1);
        $user_id = $this->session->userdata('user_id');
        $package_id = $this->uri->segment('4');
        $coupon_applied = $this->uri->segment('5');

        if($this->payment_m->is_payment_completed($user_id,$package_id)==1){
            $payment_status = 1;
            $payment_message = 'Payment Successful!';
            redirect(base_url()."app/payment/payment_info_status/?payment_status={$payment_status}&message={$payment_message}&user_id={$user_id}&package_id={$package_id}", 'refresh');
        }

        $user = $this->student_m->get(['id' => $user_id])->row_array();
        $api = new Api(get_settings('razorpay_api_key'), get_settings('razorpay_api_secret_key'));
        /**
         * You can calculate payment amount as per your logic
         * Always set the amount from backend for security reasons
         */

        $package_details = $this->package_m->get(['id' => $package_id])->row_array();
        
        if($coupon_applied==1){
            $coupon_details = $this->coupon_code_m->get_coupon_applied_amount($user_id,$package_id);
            $package_amount = $coupon_details['amount'];
            // $package_amount = $package_details['amount'] - $package_details['discount'];
        }else{
           $package_amount = $package_details['amount'] - $package_details['discount'];
        }
        

        $create_order = array(
            'amount'          => $package_amount * 100, // 2000 rupees in paise
            'currency'        => 'INR',
            'payment_capture' => 1 // auto capture
        );

        $razorpayOrder = $api->order->create($create_order);

        $order_details = [
            'razorpay_order_id' => $razorpayOrder['id'],
            'auto_capture' 		=> $create_order['payment_capture'],
            'amount' 			=> $create_order['amount']/100,
            'website_name' 		=> get_settings('system_name'),
            'name' 				=> $user['first_name'].' '.$user['last_name'],
            'email' 			=>  get_settings('system_email'),
            'phone' 			=> $user['phone'] ?? get_settings('phone'),
            'package_id'        => $package_id,
            'coupon_id'         => $coupon_details['coupon_id']
        ];

        $order_id = $this->payment_m->create_payment($order_details, $user_id);

        $_SESSION['razorpay_order_id'] = $razorpayOrder['id'];
        $data = $this->prepareData($order_details,$razorpayOrder['id']);
        $data['user_id'] = $user_id;
        $data['package_id'] = $package_id;

        $this->load->view('app/payment/razorpay_payment',array('data' => $data));
    }
    
    
    
    public function payment_info_status(){
        $this->data['user_id'] = $this->input->get('user_id');
        $this->data['payment_status'] = $this->input->get('payment_status');
        $this->data['message'] = $this->input->get('message');
        $this->data['package_id'] = $this->input->get('package_id');
        log_message("error","sv ".print_r($this->input->get(),true));
        $this->load->view('app/payment/payment_info_status',array('data' => $this->data));
    }
    
    
    
     public function prepareData($order_details,$razorpayOrderId)
    {
        return array(
            "key" => get_settings('razorpay_api_key'),
            "amount" => $order_details['amount'],
            "name" => $order_details['website_name'],
            "description" => $order_details['description'],
            "image" => base_url("uploads/system/logo-dark.png"),
            "prefill" => array(
                "name"  => $order_details['name'],
                "email"  => $order_details['email'],
                "contact" => $order_details['phone'],
            ),
//			"prefill" => array(
//				"name"  => $this->input->post('name'),
//				"email"  => $this->input->post('email'),
//				"contact" => $this->input->post('contact'),
//			),
            "notes"  => array(
                "address"  => "",
                "merchant_order_id" => rand(),
            ),
            "theme"  => array(
                "color"  => "#123369"
            ),
            "order_id" => $razorpayOrderId,
        );
    }
    
    
    public function verify_payment(){
        $user_id = $this->input->get('user_id');
        $success = true;
        $error = "payment_failed";
        if (empty($_POST['razorpay_payment_id']) === false) {
            $api = new Api(get_settings('razorpay_api_key'), get_settings('razorpay_api_secret_key'));
            try {
                $attributes = array(
                    'razorpay_order_id' => $_SESSION['razorpay_order_id'],
                    'razorpay_payment_id' => $_POST['razorpay_payment_id'],
                    'razorpay_signature' => $_POST['razorpay_signature']
                );
                $api->utility->verifyPaymentSignature($attributes);
            } catch(SignatureVerificationError $e) {
                $success = false;
                $error = 'Razorpay_Error : ' . $e->getMessage();
            }
        }
        if ($success === true) {
            /**
             * Call this function from where ever you want
             * to save save data before of after the payment
             */
            $order_details = [
                'payment_id_raz' => $_POST['razorpay_payment_id'],
                'payment_captured'	  => 1,
                'order_status'	  => 'completed'
            ];
            $this->payment_m->complete_payment($order_details,$_SESSION['razorpay_order_id']);
            
            $stud = $this->db->get_where('users', array('id' => $user_id))->row();
            $order = $this->db->get_where('create_order', array('order_id'=>$_SESSION['razorpay_order_id']))->row();
            $package = $this->db->get_where('package',array('id' => $order->package_id))->row();
 
            $data = array(
                'user_id'               => $user_id,
                'package_id'            => $order->package_id,
                'amount_paid'           => $order->amount,
                'discount'              => $package->discount,
                'user_phone'            => $stud->phone,
                'user_email'            => $stud->user_email ?? "",
                'coupon_id'             => $order->coupon_id,
                'razorpay_payment_id'   => $_POST['razorpay_payment_id'],
                'razorpay_order_id'     => $_SESSION['razorpay_order_id'],
                'razorpay_signature'    => $_POST['razorpay_signature'],
                'is_upgrade'            => '',
                'payment_date'          => date('Y-m-d H:i:s'),
                'updated_on'            => date('Y-m-d H:i:s'),
                'created_by'            => $this->session->userdata('user_id'),
                'updated_on'            => $this->session->userdata('user_id'),
                'created_on'            => date('Y-m-d H:i:s')
            );
            $this->payment_info_m->insert($data);
            $enrol = $this->enrol_m->enrol_course($user_id, $package->course_id, $order->package_id);
            
            $payment_status = 1;
            $payment_message = 'Payment Successful!';
            $package_id  = $order->package_id;
            redirect(base_url()."app/payment/payment_info_status/?payment_status={$payment_status}&message={$payment_message}&user_id={$user_id}&package_id={$package_id}");
        }
        else {
            $order_details = [
                'payment_id_raz' => '',
                'payment_captured'	  => 0,
                'order_status'	  => 'failed'
            ];
            $this->payment_m->complete_payment($order_details,$_SESSION['razorpay_order_id']);
            $this->coupon_code_m->update_coupon_code_status($order->coupon_id,$order->package_id,$user_id);

            $payment_status = 0;
            $payment_message = 'Payment Failed!';
            redirect(base_url()."app/payment/payment_info_status/?payment_status={$payment_status}&message={$payment_message}&user_id={$user_id}&package_id={$package_id}");
        }
    }
    
    
    public function cancel_payment(){
        $user_id = $this->input->get('user_id');
        $package_id = $this->input->get('package_id');
        $order_details = [
            'payment_id_raz' => '',
            'payment_captured'	  => 0,
            'order_status'	  => 'cancelled'
        ];
        $this->payment_m->complete_payment($order_details,$_SESSION['razorpay_order_id']);
        log_message('error', $this->db->last_query());

        $payment_status = 0;
        $payment_message = 'Payment Cancelled!';
        redirect(base_url()."app/payment/payment_info_status/?payment_status={$payment_status}&message={$payment_message}&user_id={$user_id}&package_id={$package_id}");
    }

    
    public function coupon_code($param="", $course_id, $package_id){
        if($param=='view'){
            $this->data['package_id']  = $package_id;
            $this->data['course_id']   =  $course_id;
            $this->db->select('package.*, course.title as course_title');
            $this->db->join('course', 'package.course_id=course.id','left');
            $this->db->where('package.id',$package_id);
            $this->data['package']    = $this->db->get('package')->row_array();
            $this->data['user_id']     = $this->session->userdata('user_id');
            $this->data['page_name']   = 'course/apply_coupon_code';
            $this->data['page_title']  = 'Coupon code Apply';
            
        }else if($param=='apply'){
            $user_id        = $this->session->userdata('user_id');
            $course_id      = $course_id;
            $package_type   = $this->input->post('package_type');
            $coupon_code    = $this->input->post('coupon_code');
            
            $this->data['coupon'] = $this->coupon_code_m->plans_by_coupon_get($course_id,$user_id,$package_type,$coupon_code);
            $this->db->select('package.*, course.title as course_title');
            $this->db->join('course', 'package.course_id=course.id','left');
            $this->db->where('package.id',$package_id);
            $this->data['package']    = $this->db->get('package')->row_array();
            
            if($this->data['coupon']['valid']==1){
                $after_one_minute = 
                
                $data['user_id'] = $user_id;
                $data['coupon_id'] = $this->data['coupon']['coupon_id'];
                $data['amount'] = $this->data['coupon']['offer_price'];
                $data['status'] = 'pending';
                $data['course_id'] = $course_id;
                $data['package_id'] = $package_id;
                $data['expires_on'] = date('Y-m-d');
                $data['datetime'] = date('Y-m-d H:i:s');
                $this->db->insert('coupon_code_apply',$data);
                
            }

            
            $this->data['course_id'] = $course_id;
            $this->data['package_id'] = $package_id;
            $this->data['page_name']   = 'course/coupon_code';
            $this->data['page_title']  = 'Coupon Code';;
        }
        
         $this->load->view('app/index', $this->data);
    }
   
    
}