<?php
require APPPATH . 'controllers/api/Api.php';
class User_goals extends Api{
    public function __construct() {
        parent::__construct();
        $this->load->model('quiz_m');
        $this->load->model('test_m');
        $this->load->model('quiz_attempt_m');
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('enrol_m');
        $this->load->model('payment_info_m');
        $this->load->model('habit_category_m');
        $this->load->model('user_goals_m');
        $this->load->model('user_goals_activity_m');
        $this->load->model('lesson_files_m');
        $this->load->model('module_completion_history_m');
    }
    
    public function habit_catogories_get()
    {
        if ($this->token_data) {
            $categories = $this->habit_category_m->get()->result_array();
            $category_data = [];
            foreach($categories as $key => $category){
                $category_data[$key]['id'] = $category['id'];
                $category_data[$key]['title'] = $category['title'] ?? '';
                $category_data[$key]['icon'] = $category['icon'] ? base_url($category['icon']) : '';
            }
            
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => $category_data];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    /* User Goals*/
    public function my_goals_get()
    {
        if ($this->token_data) {
            $goals = $this->user_goals_m->get(['user_id' => $this->user_id])->result_array();
            
            $user_goals = [];
            foreach($goals as $key => $goal){
                $category = $this->habit_category_m->get(['id' => $goal['habit_category_id']])->row();
                $user_goals[$key]['id'] = $goal['id'];
                $user_goals[$key]['title'] = $goal['title'] ?? '';
                $user_goals[$key]['description'] = $goal['description'] ?? '';
                $user_goals[$key]['time_period'] = $goal['time_period'] ?? '';
                $user_goals[$key]['start_date'] = $goal['start_date'] ? date('d-m-Y', strtotime($goal['start_date'])) : '';
                $user_goals[$key]['end_date'] = $goal['end_date'] ? date('d-m-Y', strtotime($goal['end_date'])) : '';
                
                $start_date = strtotime($goal['start_date']);
                $end_date = strtotime($goal['end_date']);
                
                // Calculate the difference in seconds, then convert to days
                $total_days = ($end_date - $start_date) / (60 * 60 * 24) + 1; // Adding 1 to include both dates
                $completed_days = $this->user_goals_activity_m->get(['goal_id' => $goal['id'], 'user_id' => $this->user_id])->num_rows();
    
                $user_goals[$key]['category_name'] = $category->title ?? '';
                $user_goals[$key]['category_icon'] = $category->icon ? base_url($category->icon) : '';
                $user_goals[$key]['total_days'] = $total_days;
                $user_goals[$key]['completed_days'] = $completed_days;
                $user_goals[$key]['progress'] = $total_days >0 ? round($completed_days/$total_days * 100) : 0;
            }
            
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => $user_goals];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function create_goal_get()
    {
        if ($this->token_data) {
        
            $start_date = $this->input->get('start_date');
            $end_date   = $this->input->get('end_date');
            
            $data['title']      = $this->input->get('title');
            $data['user_id']    = $this->user_id;
            $data['habit_category_id'] = $this->input->get('habit_category_id');
            $data['description']= $this->input->get('description');
            $data['time_period']= $this->input->get('time_period');
    
            $data['start_date'] = $this->input->get('start_date') ? \DateTime::createFromFormat('d/m/Y', $this->input->get('start_date'))->format('Y-m-d') : '';
            $data['end_date'] = $this->input->get('end_date') ? \DateTime::createFromFormat('d/m/Y', $this->input->get('end_date'))->format('Y-m-d') : '';
    
            
            
            $data['created_by'] = $this->user_id;
            $data['created_at'] = date('Y-m-d H:i:s');
    
            $this->user_goals_m->insert($data);
            
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function update_goal_get()
    {
        if ($this->token_data) {
            $id = $this->input->get('id');
            
            $data['title']       = $this->input->get('title');
            $data['description'] = $this->input->get('description');
            $data['time_period'] = $this->input->get('time_period');
            $data['start_date']  = $this->input->get('start_date') ? \DateTime::createFromFormat('d/m/Y', $this->input->get('start_date'))->format('Y-m-d') : '';
            $data['end_date']    = $this->input->get('end_date') ? \DateTime::createFromFormat('d/m/Y', $this->input->get('end_date'))->format('Y-m-d') : '';
            $data['updated_at']  = date('Y-m-d H:i:s');
            $data['updated_by']  = $this->user_id;
            
            $this->user_goals_m->update($data, ['id' => $id]);
            
            $this->response = ['status' => 1,'message' => 'success' , 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function delete_goal_get()
    {
        if ($this->token_data) {
            $id = $this->input->get('id');
            
            $this->user_goals_m->delete(['id' => $id]);
            $this->response = ['status' => 1,'message' => 'success' , 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function goal_details_get()
    {
        if ($this->token_data) {
            $goal_id = $this->input->get('goal_id');
            $lesson_file_id = $this->input->get('lesson_file_id');
            $data = $this->user_goals_activity_m->get_goal_details($goal_id, $this->user_id, $lesson_file_id) ?? [];
            $this->response = ['status' => 1,'message' => 'success' , 'data' => $data];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function mark_my_goal_get()
    {
        if ($this->token_data) {
            $goal_id = $this->input->get('goal_id');
            $lesson_file_id = $this->input->get('lesson_file_id');
            $already_marked = $this->user_goals_activity_m->get(['goal_id' => $goal_id, 'lesson_file_id' => $lesson_file_id, 'user_id' => $this->user_id, 'date' => date('Y-m-d')])->num_rows();
            if($already_marked == 0){
                $data = [
                    'goal_id'    => $goal_id,
                    'user_id'    => $this->user_id,
                    'lesson_file_id' => $lesson_file_id,
                    'date'       => date('Y-m-d'),
                    'created_on' => date('Y-m-d H:i:s'),
                    'updated_on' => date('Y-m-d H:i:s'),
                ];
                
                $this->user_goals_activity_m->insert($data);
                
                $lesson_id = $this->db->get_where('lesson_files',['id' => $lesson_file_id])->row()->lesson_id;
                $this->lesson_files_m->is_lesson_completed($this->user_id, $lesson_id);
                $module_progress = $this->lesson_files_m->get_module_progress($this->user_id,$lesson_id);
                
                $this->response  = [
                    'status' => 1,
                    'message' => 'Success',
                    'module_completed_status' => $module_progress['completed_status'],
                    'module_completed_points' => $module_progress['completed_points'],
                    'module_completed_show' => $module_progress['completed_status']==1 && $this->module_completion_history_m->get(['user_id' => $this->user_id, 'module_id' => $module_id])->num_rows()==0 ? 1 : 0,
                    'module_completed_text' => 'You earned ' . $module_progress['completed_points']. ' points to your Skillage Wallet, you can redeem it through your Wallet.'
                ];
            
            }else{
                $this->response = ['status' => 0, 'message' => 'Already marked', 'data' => []];
            }
        }
        
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function mark_goal_bulk_get()
    {
        if ($this->token_data) {
            $goal_ids = json_decode($this->input->get('goal_ids'));
    
            foreach($goal_ids as $goal_id){
                $already_marked = $this->user_goals_activity_m->get(['goal_id' => $goal_id, 'user_id' => $this->user_id, 'date' => date('Y-m-d')])->num_rows();
    
                if($already_marked == 0){
                    $data = [
                        'goal_id'    => $goal_id,
                        'user_id'    => $this->user_id,
                        'date'       => date('Y-m-d'),
                        'created_at' => date('Y-m-d H:i:s'),
                        'created_by' => $this->user_id,
                        'updated_at' => date('Y-m-d H:i:s'),
                        'updated_by' => $this->user_id
                    ];
                    
                    $this->user_goals_activity_m->insert($data);
                    
                }
            }
            
            $this->response = ['status' => 1, 'message' => 'success', 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }

}