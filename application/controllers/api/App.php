<?php
require APPPATH . 'controllers/api/Api.php';
class App extends Api{
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */

    public function __construct() {
        parent::__construct();
        $this->load->model('api_m');
        $this->load->model('user_m');
        $this->load->model('banner_m');
        $this->load->model('category_m');
        $this->load->model('course_m');
        $this->load->model('enrol_m');
        $this->load->model('package_m');
        $this->load->model('testimonial_m');
        $this->load->model('feed_category_m');
        $this->load->model('libraries/wati_m');
        $this->load->model('Lesson_m');
        $this->load->model('news_m');
        $this->load->model('create_order_m');
    }

    /*
     * Login Using Phone
     */
    public function login_get() {
        // $this->output->enable_profiler(TRUE);
        $user_id = $this->api_m->login();
        
        if ($user_id > 0){
            $validity = 1;
            $message = 'OTP Send Successfully!';
        }else{
            $validity = 0;
            $message = "You don't have an account. Please register to continue.!";
        }
        $this->response = ['status' => 1, 'message' => $message, 'validity' => $validity, 'user_id' => $user_id ?? 0];
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    

    /*
     * Verify OTP
     */
    public function verify_otp_get() {
        $userdata = $this->api_m->verify_otp();
        if (!$userdata) {
            $this->response = ['status' => 0, 'message' => 'Invalid OTP', 'userdata' => []];
        }else{
            $userdata['token'] = $this->generate_token($userdata);
            $this->response = ['status' => 1, 'message' => 'Login Success!', 'userdata' => $userdata , 'points' =>  get_point_settings('registration')];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    

    /*
     * Register
     */
    public function register_get(){
        $user_id = $this->api_m->register();
        if ($user_id > 0){
            $user_data = $this->user_m->get(['id' => $user_id])->row_array();
            $userdata = $this->api_m->userdata($user_id);
            $user_data['token'] = $this->generate_token($userdata);
            $validity = 1;
            $message = 'Registered Successfully!'; 
            $coins_count = get_point_settings('registration');
            
            $point['user_id'] = $user_id;
            $point['type_id'] = 1;
            $point['points'] =  get_point_settings('registration');
            $point['date'] = date('Y-m-d');
            $expiry_days = get_wallet_expiry_settings('registration');
            $expiry_date = date('Y-m-d', strtotime(date('Y-m-d') . ' + ' . $expiry_days . ' days'));
            $point['expiry_date'] = $expiry_date;
            $point['created_on'] = date('Y-m-d H:i:s');
            $point['updated_on'] = date('Y-m-d H:i:s');
            $this->db->insert('user_points',$point);
        }else{
            $validity = 0;
            $message = 'Phone number already exists!';
            $user_data = [];
        }
        $this->response = ['status' => 1, 'message' => $message, 'validity' => $validity, 'user_id' => $user_id, 'coins_count' => $coins_count, 'user_data' => $user_data];
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    
    /*
    * Register verify otp
    */
    public function register_verify_otp_get(){
        $otp = $this->input->get('otp');
        $code = $this->input->get('code');
        $phone = $this->input->get('phone');
        $device_id = $this->input->get('device_id');
        
        $user = $this->user_m->get(['verification_code' => $otp, 'country_code' => $code, 'phone' => $phone, 'validity' => 0]);
        // log_message('error',print_r($this->db->last_query(),true));
        if($user->num_rows() == 0) {
            $this->response = ['status' => 0, 'message' => 'Invalid OTP'];
        }else{
            // $userdata['token'] = $this->generate_token($userdata);
            $this->response = ['status' => 1, 'message' => 'Otp Verified!'];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    
    
    public function apply_certificate_get(){ 
        $course_id   = $this->input->get('course_id');
        $course_id   = 2;
        $user_id   = 2;
        // $auth_token   = $this->input->get('auth_token');
        // $logged_in_user_details = json_decode($this->token_data_get($auth_token), true);
        
        $certificate_generate = $this->lesson_m->generate_certificate_pdf($user_id, $course_id);
        
        $certificate_path = base_url($certificate_generate);
        
  
        $response = ['status' => 1, 'message' => 'Success','data' => $certificate_path];
        
        
        
        // if($logged_in_user_details['user_id'] > 0) {
        //     $generated_certificate = $this->db->where(['course_id' => $course_id, 'user_id' => $logged_in_user_details['user_id']])->from('generated_certificates')->get()->num_rows();
        //     if($generated_certificate ==0){
                
        //         $is_completed = $this->api_model->is_course_completed($course_id,$logged_in_user_details['user_id']);
        //         // log_message('error','is_completed '.print_r($is_completed,true));
        //         if($is_completed == 1){
        //             $response = ['status' => 1, 'message' => 'Success'];
        //             $certificate_generate = $this->crud_model->generate_certificate_pdf($logged_in_user_details['user_id'], $course_id);
        //         }else{
        //             $response = ['status' => 0, 'message' => 'Lesson Not Compelted'];
        //         }
                
        //     }else{
        //         $response = ['status' => 0, 'message' => 'Already Applied'];
        //     }
        // }else{
        //     $response = ['status' => 0, 'message' => 'Authentication failed'];
        // }
        $this->set_response($response, REST_Controller::HTTP_OK);
    }
    
    
    
    
    
    /*
    *  Register check phone
    */
    public function register_check_phone_get(){
        $code = $this->input->get('code');
        $phone = $this->input->get('phone');
        $phone_num = $code.$phone;
        //check user
        $user = $this->user_m->get(['country_code' => $code, 'phone' => $phone]);
        
        if ($user->num_rows() > 0) {
            $user = $user->row();
            if($user->validity == 0){
                $otp = $this->generate_otp($code,$phone);
                $this->api_m->store_otp($code,$phone,$otp);
                $this->send_otp_message($phone_num, $otp);
                $validity = 0;
                $new_user = 1;
            }elseif($user->validity == 1){
                $validity = 1;
                $new_user = 0;
            }
            
            $this->response = ['status' => 1, 'message' => 'User Found', 'new_user' => $new_user, 'validity' => $validity];
        }else{
            $otp = $this->generate_otp($code,$phone);
            $this->api_m->store_otp($code,$phone,$otp);
            $this->send_otp_message($phone_num, $otp);
            $this->response = ['status' => 0, 'message' => 'No User Found', 'new_user' => 1, 'validity' => 0];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    

    /*
     * Home Page Data
     */
    public function home_page_data_get() {
        if ($this->token_data) {
            $banners = $this->api_m->get_app_banners();
            $categories = $this->category_m->get_app_parent_categories();
            $userdata = $this->api_m->userdata($this->user_id);
            $userdata['token'] = $this->generate_token($userdata);
            // $testimonial = $this->testimonial_m->get_app_testimonial();
            $languages = $this->api_m->languages();
            $privacy_policy = $this->db->get_where('frontend_settings',['key' => 'privacy_policy'])->row()->value;
            $news = $this->news_m->get()->result_array();
            $data = [
                'banners' => $banners ?? [],
                'languages' => $categories,
                // 'categories' => $categories,
                'privacy_policy' => $privacy_policy,
                'news' => $news,
                'language_title' => "Explore our Courses",
                'language_image' => base_url("languages/language_image.png"),
                'userdata' => $userdata,
            ];
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => $data];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    

    // user data
    public function userdata_get(){
        if($this->token_data) {
            $this->response = $this->api_m->userdata($this->user_id);
        }else{
            $this->response = ['status' => 0, 'message' => 'Authentication Failed!'];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }

    // Notification token add
    public function add_notification_token_get()
    {
        $notification_token = $this->input->get('notification_token');
        $check_existing = $this->db->get_where('users',['notification_token' => $notification_token])->row();
        
        if(!empty($check_existing))
        {
             $this->user_m->update([
                    'notification_token' => ''
                ], ['id' => $check_existing->id]);
        }
        
        if ($this->token_data) 
        {
            $this->user_m->update([
                    'notification_token' => $this->input->get('notification_token'),'updated_on' => date('Y-m-d H:i:s')
                ], ['id' => $this->user_id]);
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => []];
        }
        
        
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }

    // update referral code
    public function update_referral_code_get() {
        if ($this->token_data) {
            $invited_id = $this->input->get('invitedid');
            $dynamic_link = $this->input->get('mylink');

            $this->user_m->update([
                'referred_by' => $invited_id,
                'dynamic_link' => $dynamic_link,
            ], ['id' => $this->user_id]);
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }

    // upload user image
    public function upload_user_image_post() {
        if ($this->token_data) {
            $this->user_m->upload_user_image_app($this->user_id);
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }

    // update user data
    public function update_userdata_post() {
        if ($this->token_data) {
            $name = $this->input->post('name');
            $phone = $this->input->post('phone');
            $code  = $this->input->post('code');
            $device_id = $this->input->post('device_id');
            $phone_full = $code.$phone;
            $user_email   = $this->input->post('user_email');
            $qualification = $this->input->post('qualification');
            $place = $this->input->post('place');
            $gender = $this->input->post('gender');
            $updated_on = date('Y-m-d H:i:s');

            $user = $this->user_m->get(['phone' => $phone, 'id !=' => $this->user_id]);
            $userdata = $this->user_m->get(['id' => $this->user_id])->row_array();
            if($user->num_rows() == 0) {
                $this->user_m->update(['name' => $name,'country_code' => $code, 'phone' => $phone, 'device_id' => $device_id, 'user_email' => $user_email, 'gender' => $gender, 'place' => $place, 'qualification' => $qualification, 'updated_by' => $this->user_id, 'updated_on' => $updated_on], ['id' => $this->user_id]);
                $validity = $this->db->get_where('user_points',['user_id' => $this->user_id, 'type_id' => 2])->num_rows();
                if($validity==0){
                    $point['user_id'] = $this->user_id;
                    $point['type_id'] = 2;
                    $point['points'] =  get_point_settings('profile_completion');
                    $point['date'] = date('Y-m-d');
                    $expiry_days = get_wallet_expiry_settings('profile_completion');
                    $expiry_date = date('Y-m-d', strtotime(date('Y-m-d') . ' + ' . $expiry_days . ' days'));
                    $point['expiry_date'] = $expiry_date;
                    $point['created_on'] = date('Y-m-d H:i:s');
                    $point['updated_on'] = date('Y-m-d H:i:s');
                    $this->db->insert('user_points',$point);
                }

                $existing_phone = $userdata['country_code'] . $userdata['phone'];
                if($existing_phone != $phone_full){
                    $phone_number_changed = 1;
                    $message = 'Phone Number Changed';
                }else{
                    $phone_number_changed = 0;
                    $message = 'Success';
                }
                $status =  1;
            }else{
                $message = 'Mobile Number Already Exist for Another User';
                $phone_number_changed = 0;
                $status  =  0;
            }
            $this->response = ['status' => $status, 'message' => $message, 'phone_number_changed' => $phone_number_changed, 'data' => []];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    
    /*
     * Contact us
    */
    public function contact_us_get() {
        if ($this->token_data) {

            $data = [
                'email' => '<EMAIL>',
                'phone' => '919895679723',
                'whatsapp' => '919895679723',
                'address' => '',
                'about' => "",
            ];
            $this->response = ['status' => 1, 'message' => 'Success', 'data' => $data];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    // wallet
    public function wallet_get() {
        if ($this->token_data) {
            $data = $this->api_m->get_wallet_data($this->user_id);
            $this->response = ['status' => 1, 'message' => 'Success', 'wallet' => [$data]];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
     public function wallet_history_get() {
        if ($this->token_data) {
            $data = $this->api_m->get_wallet_history_data($this->user_id);
            $this->response = ['status' => 1, 'message' => 'Success', 'wallet' => [$data]];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    // Testimonial list
    public function testimonial_get() {
        if ($this->token_data) {
            $this->load->model('testimonial_m');
            $course_id =  $this->user_m->get(['id' => $this->user_id])->row()->course_id;
            $course_name = $this->course_m->get(['id' => $course_id])->row()->title;
            $data = $this->testimonial_m->get(['course_id' => $course_id])->result_array();
            foreach ($data as &$testimonial) {
                if($testimonial['photo']){
                    $testimonial['photo'] = base_url($testimonial['photo']);
                }
                $testimonial['course_name'] = $course_name;
            }
            $this->response = ['status' => 1, 'message' => 'Success', 'Testimonial' => $data];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }

    // feed list
    public function feed_get() {
        if ($this->token_data) {
            $this->load->model('feed_m');
            // $course_id =  $this->user_m->get(['id' => $this->user_id])->row()->course_id;
            $data = $this->feed_m->get_app_feeds($course_id, $this->user_id);
            $this->response = ['status' => 1, 'message' => 'Success', 'feeds' => $data];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    public function like_feed_get(){
        if ($this->token_data) {
            $this->load->model('feed_m');
            $feed_id =  $this->input->get('feed_id');
            $data = $this->feed_m->like_feed($feed_id, $this->user_id);
            $this->response = ['status' => 1, 'message' => 'Success'];
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    //Notification
    public function notifications_get(){
        if($this->token_data){
            $this->load->model('notification_m');
            
            $user_id    = $this->user_id;
            $course_id  = $this->input->get('course_id');
            $data       = $this->notification_m->get_app_notification($course_id, $user_id);
            $this->response = $data;
        }
        return $this->set_response($this->response, REST_Controller::HTTP_OK);
    }
    
    // Send OTP message
    private function send_otp_message($phone, $otp){
        if ($otp == '1234'){
            return false;
        }
        $otp    = urlencode ( $otp );
        $fields = array(
            'username' => 'prism',
            'password' => 'stallion123',
            'sendername' => 'TRGNMD',
             'mobileno'=>$phone,
             'message'=>$otp
            );
        $url = "https://2factor.in/API/V1/5f32c941-ad59-11ea-9fa5-0200cd936042/SMS/$phone/$otp/ApplicationOTP";
        //open connection
        $ch = curl_init();

        //set options
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $fields);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false

        //execute post
        $result = curl_exec($ch);

        //close connection
        curl_close($ch);

        //write to file
        $fp = fopen('uploads/result.pdf', 'w');  //make sure the directory markdown.md is in and the result.pdf will go to has proper permissions
        fwrite($fp, $result);
        fclose($fp);
        //echo "sms sent successfully";
        // echo $message;
        return $result;
    }
    
    // Generate OTP
    private function generate_otp($code,$phone){
        $digits = 4;
        $phone_full = $code.$phone;

        if (in_array($phone_full, ['919946801100', '918129043753', '919946432377', '919645714591'])){
            $otp = '1234';
        }else{
            $otp  = rand(pow(10, $digits-1), pow(10, $digits)-1);
        }

        $this->user_m->update(['verification_code' => $otp], ['phone' => $phone]);
        // log_message("error","kjjhhgg ".print_r($this->db->last_query(),true));
        return $otp;
    }
    
    
    
    public function crone_job_fetch_order_get(){
        // Get pending orders
        $user_id  = $this->input->get('user_id');
        $where['order_status'] = 'pending';
        if($user_id > 0){
            $where['user_id'] = $user_id;
        }
        $response = $this->create_order_m->fetch_order_automate($where);
        return $this->set_response($response, REST_Controller::HTTP_OK);
    }

    
    
}