<?php
require APPPATH . '/libraries/TokenHandler.php';
//include Rest Controller library
require APPPATH . 'libraries/REST_Controller.php';
class Api extends REST_Controller{
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    protected $token;
    protected $token_data;
    protected $user_id;

    protected $response;
    protected $cache_prefix = 'skillage_api_';

    public function __construct() {
        parent::__construct();
        $this->load->database();

        // creating object of TokenHandler class at first
        $this->tokenHandler = new TokenHandler();
        $this->token_data   = $this->token_data();
        if (!$this->token_data){
            $this->response = ['status' => 0, 'message' => 'Authentication Failed!', 'data' => []];
        }
        $this->user_id = $this->token_data['user_id'];
        header('Content-Type: application/json');
        
        log_message('error', "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]");
        log_message('error', json_encode($_REQUEST));

        // create the api log
        // $this->create_log();
    }
    
    protected function getOrSetCache($key, $callback, $ttl = 600)
    {
        $this->load->driver('cache', ['adapter' => 'file']); // Use file adapter
        $full_key = $this->cache_prefix . $key;
    
        $data = $this->cache->file->get($full_key);
    
        if ($data === FALSE) {
            if (is_callable($callback)) {
                $data = call_user_func($callback);
            } elseif (is_array($callback)) {
                $data = $callback;
            } else {
                show_error('Callback must be callable or an array.');
            }
    
            $this->cache->file->save($full_key, $data, $ttl);
        }
    
        return $data;
    }

    
    protected function deleteCache($key)
    {
        return $this->cache->file->delete($key);
    }



    public function app_version_force_get() {
       
        // $response = $this->getOrSetCache('app_version', function () {
            $response = $this->get_app_version();
        // }, 1000);
        return $this->set_response($response, REST_Controller::HTTP_OK);
    }
    
    private function get_app_version(){
        return [
                    'ios_forceupdate_version' => get_settings('ios_forceupdate_version'),
                    'ios_payment_version' => get_settings('ios_payment_version'),
                    'android_forceupdate_version' => get_settings('android_forceupdate_version'),
                    'android_payment_version' => get_settings('android_payment_version'),
               ];
    }


    //Handle the user Auth Token
    protected function token_data() {
        $auth_token = $this->input->method() == 'post' ? $this->input->post('auth_token') : $this->input->get('auth_token');

        if (!empty($auth_token) && $auth_token != 'token') {
            try {
                return $this->tokenHandler->DecodeToken($auth_token);
            } catch (Exception $e) {
                echo 'catch';
                http_response_code('401');
                echo json_encode(["status" => false, "message" => $e->getMessage()]);
                return false;
            }
        } else {
            return false;
        }
    }
    
    
    

    //Generate auth token using userdata
    protected function generate_token($userdata){
        $encode_data['id'] = $userdata['id'];
        $encode_data['user_id'] = $userdata['id'];
        $encode_data['role_id'] = $userdata['role_id'];
        $encode_data['role_label'] = $userdata['role_label'];
        $encode_data['phone'] = $userdata['phone'];
        return $this->tokenHandler->GenerateToken($encode_data);
    }

    // create a log for each api call
    private function create_log(){
        $this->load->model('user_log_m');
        $this->user_log_m->insert([
            'user_id'       => $this->user_id,
            'request_url'   => "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]",
            'request_json'  => json_encode($_REQUEST),
            'datetime'      => date('Y-m-d H:i:s'),
        ]);
    }
}