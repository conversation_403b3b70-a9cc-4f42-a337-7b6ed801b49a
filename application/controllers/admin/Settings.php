<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Settings extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('lead_source_m');
        $this->load->model('lead_status_m');
        $this->load->model('settings_m');
        $this->load->model('frontend_settings_m');
    }


    /*
     * Basic Details
     */
    public function basic_details(){
        if ($this->input->post()){
              $this->settings_m->update_basic_details();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/basic_details/');
        } 
        
        $this->data['page_title']   = 'Basic Details';
        $this->data['page_name']    = 'settings/basic_details';
        $this->load->view('admin/index', $this->data);
    }
    
    
    public function redeem_details(){
        if ($this->input->post()){
              $this->settings_m->update_redeem_details();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/basic_details/');
        } 
        
        $this->data['page_title']   = 'Basic Details';
        $this->data['page_name']    = 'settings/basic_details';
        $this->load->view('admin/index', $this->data);
    }
     
    
    /*zoom details*/
    
    public function zoom_details(){
        if ($this->input->post()){
              $this->settings_m->update_zoom_details();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/zoom_details/');
        } 
        
           
        $this->data['page_title']   = 'Zoom Details';
        $this->data['page_name']    = 'settings/zoom_details';
        $this->load->view('admin/index', $this->data);
    }
    
    /*Razorpay*/
    public function razorpay(){
        if ($this->input->post()){
              $this->settings_m->update_razorpay();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/razorpay/');
        } 
        
        $this->data['page_title']   = 'Razorpay Details';
        $this->data['page_name']    = 'settings/razorpay_details';
        $this->load->view('admin/index', $this->data);
    }
    
    /*contact details*/
    public function contact_details(){
        if ($this->input->post()){
              $this->settings_m->update_contact_details();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/contact_details/');
        } 
        $this->data['page_title']   = 'Contact Details';
        $this->data['page_name']    = 'settings/contact_details';
        $this->load->view('admin/index', $this->data);
    }
    
    /*Notification Key*/
    public function notification_key(){
        if ($this->input->post()){
              $this->settings_m->update_notification_key();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/notification_key/');
        }
        $this->data['page_title']   = 'notification key Details';
        $this->data['page_name']    = 'settings/notification_key';
        $this->load->view('admin/index', $this->data);
    }
    
    /*App version*/
    public function app_version(){
        if ($this->input->post()){
              $this->settings_m->update_app_version();
              $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
             redirect('admin/settings/app_version/');
        }
        $this->data['page_title']   = 'App version Details';
        $this->data['page_name']    = 'settings/app_version';
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*Website Settings*/
    public function frontend_settings($param1 = "") {
        if($param1 == 'frontend_update') {
          $this->frontend_settings_m->update_frontend_settings();
          $this->session->set_flashdata('flash_message', get_phrase('frontend_settings_updated'));
          redirect('admin/settings/frontend_settings');
        }
    
        if($param1 == 'banner_image_update') {
          $this->frontend_settings_m->update_frontend_banner();
          $this->session->set_flashdata('flash_message', get_phrase('banner_image_update'));
          redirect('admin/settings/frontend_settings');
        }
        
        if($param1 == 'light_logo') {
          $this->frontend_settings_m->update_light_logo();
          $this->session->set_flashdata('flash_message', get_phrase('logo_updated'));
          redirect('admin/settings/frontend_settings');
        }
        
        if($param1 == 'dark_logo') {
          $this->frontend_settings_m->update_dark_logo();
          $this->session->set_flashdata('flash_message', get_phrase('logo_updated'));
          redirect('admin/settings/frontend_settings');
        }
        
        if($param1 == 'small_logo') {
          $this->frontend_settings_m->update_small_logo();
          $this->session->set_flashdata('flash_message', get_phrase('logo_updated'));
          redirect('admin/settings/frontend_settings');
        }
        
        if($param1 == 'favicon') {
          $this->frontend_settings_m->update_favicon();
          $this->session->set_flashdata('flash_message', get_phrase('favicon_updated'));
          redirect('admin/settings/frontend_settings') ;
        }
        
        $this->data['page_title']   = 'Website Settings';
        $this->data['page_name']    = 'settings/frontend_settings';
        $this->load->view('admin/index', $this->data);
    }
    
    
    
    
}