<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Accounts extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        
        $this->load->model('accounts_m');
    }

    /*
     * Index Page
     */
    public function index(){
        $this->data['list_items']   = $this->accounts_m->get_accounts_with_balance();
        $this->data['page_title']   = 'Accounts';
        $this->data['page_name']    = 'accounts/index';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Add Task
     */
    public function add(){
        if ($this->input->post()) {
            $data['title'] = $this->input->post('title');
            $data['description'] = $this->input->post('description');
            $data['balance'] = $this->input->post('balance');

            $data['created_on'] = date('Y-m-d H:i:s');
            $data['updated_on'] = date('Y-m-d H:i:s');

            $this->accounts_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
        }
        redirect('admin/accounts/index/');
    }

    /*
     * Edit Task
     */
    public function edit($id = 0){
        if ($this->input->post() && $id){
            $data['title'] = $this->input->post('title');
            $data['description'] = $this->input->post('description');
            $data['balance'] = $this->input->post('balance');
            $data['updated_on']     = date('Y-m-d H:i:s');

            $this->accounts_m->update($data, ['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
        }
        redirect('admin/accounts/index/');
    }

    /*
     * Delete Lesson
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->accounts_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/accounts/index/');
    }

    /**
     * Make Primary Account
     */
    public function make_primary(){
        if ($this->input->get('account_id') > 0){
            $this->accounts_m->make_primary($this->input->get('account_id'));
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
        }
        redirect($this->input->server('HTTP_REFERER'));
    }
}