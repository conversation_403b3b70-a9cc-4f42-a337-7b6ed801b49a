<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Module_category extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('module_category_m');
    }

    /*
     * Index Page
     */
    public function index(){
        $this->data['list_items']   = $this->module_category_m->get()->result_array();
        $this->data['page_title']   = 'Module Category';
        $this->data['page_name']    = 'module_category/index';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Add Academic_coordinator
     */
    public function add(){
        if ($this->input->post()){

            $data['title']         = $this->input->post('title');
            $data['status']        = 1;
            $data['created_by']    = get_user_id();
            $data['updated_by']    = get_user_id();
            $data['created_on']    = date('Y-m-d H:i:s');
            $data['updated_on']    = date('Y-m-d H:i:s');
            
            $this->module_category_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/module_category/index/');
            
        }
      
        
        $this->data['page_title']   = 'Add Module Category';
        $this->data['page_name']    = 'module_category/add';
        $this->load->view('admin/index', $this->data);
    }

    // /*
    //  * Edit Academic_coordinator
    //  */
    public function edit($id){
        
            if ($this->input->post()){
                $data['title']         = $this->input->post('title');
                $data['updated_by']    = get_user_id();
                $data['updated_on'] = date('Y-m-d H:i:s');
                $this->module_category_m->update($data, ['id' => $id]);
                redirect('admin/user_type/index/');
            }
            
        
        $this->data['edit_data']    = $this->module_category_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'Edit Module Category';
        $this->data['page_name']    = 'module_category/edit';
        $this->load->view('admin/index', $this->data);
    }

    // /*
    //  * Delete Academic_coordinator
    //  */
    public function delete($id = 0){
        if ($id > 0){
            $this->module_category_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/module_category/index/');
    }
}