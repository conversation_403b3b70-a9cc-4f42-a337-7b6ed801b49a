<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Points_settings extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('lesson_m');
        $this->load->model('lesson_files_m');
        $this->load->model('point_settings_m');
        $this->load->model('module_wise_points_m');
    }
    
    
    
    
    
    /*point details*/
    public function global_settings(){
        if($this->input->post()){
            $this->point_settings_m->update_point_details();
            // log_message("error","kbkebk ".print_r($this->db->last_query(),true));
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
            redirect('admin/points_settings/global_settings/');
        } 
        $this->data['page_title']   = 'Global Points';
        $this->data['page_name']    = 'points_settings/global_settings';
        $this->load->view('admin/index', $this->data);
    }
    
    /*wallet expiry*/
    public function wallet_expiry_settings(){
        if($this->input->post()){
            $this->point_settings_m->update_expiry_details();
            log_message("error","wall ".print_r($this->db->last_query(),true));
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
            redirect('admin/points_settings/wallet_expiry_settings/');
        } 
        $this->data['page_title']   = 'Wallet Expiry Settings';
        $this->data['page_name']    = 'points_settings/wallet_expiry_settings';
        $this->load->view('admin/index', $this->data);
    }

    
    public function module_wise_settings(){
        if($this->input->get()){
            $lessons = $this->lesson_m->get(['course_id' => $this->input->get('course_id')],['id as lesson_id', 'title as lesson_title'])->result_array();
            foreach($lessons as $key=> $lesson){
                $lessons[$key]['lesson_points'] = $this->module_wise_points_m->get(['item_id' => $lesson['lesson_id'], 'type' => 1])->row()->points;
                $lessons[$key]['lesson_files'] = $this->lesson_files_m->get(['lesson_id' => $lesson['lesson_id']],['id as lesson_file_id', 'title as lesson_file_title', 'lesson_type', 'attachment_type'])->result_array();
            }
            $this->data['lessons'] =$lessons;
        } 
        $this->data['course_data'] = $this->course_m->get()->result_array();
        $this->data['page_title']   = 'Module Wise Points';
        $this->data['page_name']    = 'points_settings/module_wise_settings';
        $this->load->view('admin/index', $this->data);
    }
    
    
    // update course start date
    public function save_points(){
        if($this->input->post()){
            $item_id = $this->input->post('item_id');
            $type = $this->input->post('type');
            $points = $this->input->post('points');

            // check if already
            $where = [
                'item_id' => $item_id,
                'type' => $type,
            ];
            $module_wise_points = $this->module_wise_points_m->get($where);

            if ($module_wise_points->num_rows() > 0){
                $update_data = [
                    'points' => $points,
                    'updated_on' => date('Y-m-d H:i:s')
                ];
                $this->module_wise_points_m->update($update_data, $where);
            }else{
                $insert_data = [
                    'item_id' => $item_id,
                    'type' => $type,
                    'points' => $points,
                    'created_on' => date('Y-m-d H:i:s'),
                    'updated_on' => date('Y-m-d H:i:s')
                ];
                $this->module_wise_points_m->insert($insert_data);
            }
        }
        echo true;
    }
    
    
    
}    