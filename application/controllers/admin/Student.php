<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Student extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('package_m');
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('enrol_m');
        $this->load->model('lead_source_m');
        $this->load->model('lead_status_m');
        $this->load->model('lead_history_m');
        $this->load->model('counselor_m');
        $this->load->model('package_m');
        $this->load->model('payment_info_m');
        $this->load->model('accounts_m');
        $this->load->model('user_type_m');
        $this->load->model('lesson_files_m');
    }

    /*
     * Index Page
     */
    public function index(){
        $this->db->select('users.*');
        if(!empty($this->input->get('search_key'))){
            $search_key = $this->input->get('search_key');
            $this->db->group_start();
            $this->db->like('users.name', $search_key);
            $this->db->or_like('users.phone', $search_key);
            $this->db->or_like('users.email', $search_key);
            $this->db->or_like('users.user_email', $search_key);
            $this->db->group_end();
        }
        
        if(!empty($this->input->get('start_date'))){
            $start_date = $this->input->get('start_date') ?? date('Y-m-d');
            $this->db->where('date(users.created_on) >=', $start_date);
        }
        
        if(!empty($this->input->get('end_date'))){
            $end_date = $this->input->get('end_date') ?? date('Y-m-d');;
            $this->db->where('date(users.created_on) <=', $end_date);
        }
        
        // Check if course_id is provided in the request
        if($this->input->get('course_id') > 0){
            $course_id = $this->input->get('course_id');
        
            // Join with the enrol table and add the course_id condition
            $this->db->join('enrol', 'enrol.user_id = users.id', 'inner');
            $this->db->where('enrol.course_id', $course_id);
        }

        $this->db->where('role_id',2);
        $this->db->group_by('users.id');
        $this->data['list_items']   = $this->db->get('users')->result_array(); 
        log_message("error","jhjb ". print_r($this->db->last_query(), true));
        
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $this->data['page_title']   = 'Student';
        $this->data['page_name']    = 'student/index';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Add Student
     */
    public function add(){
        if ($this->input->post()){
            
            
            // Check if the phone number already exists in the database
            $phone = $this->input->post('country_code').$this->input->post('phone');
            if ($this->student_m->check_phone_duplication('create', $phone)) {
                $this->session->set_flashdata('error_message', 'Phone number already exists.');
                redirect('admin/student/add');
            }
            $data['name']           = $this->input->post('name');
            $data['category_id']    = $this->input->post('category_id');
            $data['course_id']      = $this->input->post('course_id');
            $data['lead_status']    = 1;
            $data['cre_id']         = $this->input->post('cre_id');
            // $data['lead_source']    = $this->input->post('lead_source');
            // $data['followup_date']  = $this->input->post('followup_date');
            $data['biography']      = $this->input->post('biography');
            $data['user_type']      = $this->input->post('user_type');
            $data['country_code']   = $this->input->post('country_code');
            $data['phone']          = $this->input->post('phone');
            $data['phone_full']     = $this->input->post('country_code').$this->input->post('phone');
            $data['user_email']     = $this->input->post('email');
            $data['username']       = $this->input->post('username');
            $data['password']       = sha1($this->input->post('password'));
            $data['created_on']     = date('Y-m-d H:i:s');
            $data['updated_on']     = date('Y-m-d H:i:s');
            
            $user_id = $this->student_m->insert($data);
            $this->student_m->upload_user_image($user_id);
            
            if($this->input->post('course_id')>0){
                $this->enrol_m->enrol_course($user_id,$this->input->post('course_id'));
            }

            if ($user_id > 0){
                $data2['lead_id'] 		= $user_id;
                $data2['cre_id'] 		= $this->input->post('cre_id');
                $data2['lead_status'] 	= 1;
                // $data2['lead_source']   = $this->input->post('lead_source');
                // $data2['followup_date'] = date('Y-m-d', strtotime($this->input->post('followup_date')));
                $data2['remarks']       = $this->input->post('note');
                $data2['created_on'] 	= date('Y-m-d H:i:s');
                $data2['updated_on'] 	= date('Y-m-d H:i:s');
                $this->lead_history_m->insert($data2);
            }
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/student/index/');
        }

        $this->data['categories']   = $this->category_m->get_parent_categories();
        // $this->data['lead_source']  = $this->lead_source_m->get()->result_array();
        $this->data['counselors']   = $this->counselor_m->get()->result_array();
        // $this->data['packages']     = $this->package_m->get()->result_array();
        $this->data['page_title']   = 'Add Student';
        $this->data['page_name']    = 'student/add';
        $this->load->view('admin/index', $this->data);
    }
    
    /*Add  payment_info*/
    public function add_manual_payment($user_id,$package_id,$amount,$phone){
        $account_id = $this->accounts_m->get_primary_account_id();

        // get package details
        $package = $this->package_m->get(['id' => $package_id])->row();
        $package_duration = $package->duration ?? 100;
        $expiry_date = date('Y-m-d', strtotime(date('Y-m-d'). " + {$package_duration} days"));
        
        $data['user_id'] = $user_id;
        $data['package_id'] = $package_id;
        $data['amount_paid'] = $amount;
        $data['account_id'] = $account_id;
        $data['razorpay_payment_id'] =  'man_'.uniqid();
        $data['user_phone'] =  $phone;
        $data['payment_date'] = date('Y-m-d H:i:s');
        $data['package_duration'] = $package_duration;
        $data['expiry_date'] = $expiry_date;
        $data['created_on'] = date('Y-m-d H:i:s');
        $data['updated_on'] = date('Y-m-d H:i:s');
        $data['created_by'] = get_user_id();
        $data['updated_by'] = get_user_id();
        
        
        $this->payment_info_m->insert($data);
    }
    

     /*
      * Edit Student
      */
    public function edit($id = 0){
        if ($id == 0){
            redirect('admin/student/index/');
        }else{
                if ($this->input->post()){
                    
                    // Check if the phone number already exists in the database
                $phone = $this->input->post('phone');
                $user_id =  $this->uri->segment(4);
                if ($this->student_m->check_phone_duplication('update',$phone,$user_id)) {
                    $this->session->set_flashdata('error_message', 'Phone number already exists.');
                    redirect('admin/student/edit/'.$user_id);
                }
                
                $data['name'] = $this->input->post('name');
                $data['category_id'] = $this->input->post('category_id');
                $data['course_id'] = $this->input->post('course_id');
                $data['biography'] = $this->input->post('biography');
                $data['country_code'] = $this->input->post('country_code');
                $data['phone'] = $phone;
                $data['phone_full']     = $this->input->post('country_code').$phone;
                $data['user_email']       = $this->input->post('email');
                $data['username']    = $this->input->post('username');
                $data['password']    = sha1($this->input->post('password'));
                $data['user_type']           = $this->input->post('user_type');
                $data['cre_id']         = $this->input->post('cre_id');
                $data['updated_on'] = date('Y-m-d H:i:s');
                $this->student_m->update($data, ['id' => $id]);
                
                $this->student_m->upload_user_image($id);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/student/index/');
            }
        }
        $this->data['edit_data']    = $this->student_m->get(['id' => $id])->row_array();
        $this->data['categories']   = $this->category_m->get_parent_categories();
        $this->data['counselors']   = $this->counselor_m->get()->result_array();
        $this->data['page_title']   = 'Student Edit';
        $this->data['page_name']    = 'student/edit';
        $this->load->view('admin/index', $this->data);
    }
    
     /*
      * Delete Student
      */
    public function delete($id = 0){
        if ($id > 0){
            $this->student_m->delete(['id' => $id]);
            $this->enrol_m->delete(['user_id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/student/index/');
    }
    
    
     /*
      * Change Device Student
      */
    public function change_device($id = 0){
        if ($id > 0){
            $data['status'] = 0;
            $this->student_m->change_device($data, ['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
        }
        redirect('admin/student/index/');
    }


    /*
     * Get students by course
     */
    public function ajax_get_students_by_course(){
        $course_id = $this->input->get('course_id');
        $students = $this->enrol_m->get_students_by_course($course_id);
        log_message("error","sdvbj ".print_r($this->db->last_query(),true));
        echo "<option value=''>Select Student</option>";
        foreach ($students as $student){
            echo "<option value='{$student['user_id']}'>{$student['name']} - {$student['phone']}</option>";
        }
    }
    
    public function ajax_change_user_premium_status(){
        $res = false;
        if($_POST){
            $data['premium'] = $_POST['status'];
            $result = $this->student_m->update($data, ['id' =>  $_POST['id']]);
            if($result){
                $res = true;
            }
        }
        echo $res;
    }
    
    
    
    
    /*
     * Bulk Upload Student
     */
    public function excel_upload(){
        if ($this->input->post()){
            $this->load->library('excel');
            $path = $_FILES["file"]["tmp_name"];
            $object = PHPExcel_IOFactory::load($path);
            foreach($object->getWorksheetIterator() as $worksheet) {
                $highestRow = $worksheet->getHighestRow();
                $highestColumn = $worksheet->getHighestColumn();
                $k=0;
                for($row=3; $row<=$highestRow; $row++) {
                    $name = $worksheet->getCellByColumnAndRow(1, $row)->getValue();
                    $phone = $worksheet->getCellByColumnAndRow(2, $row)->getValue();
                    


            // if ($this->student_m->check_phone_duplication('create', $phone)) {
            //     $this->session->set_flashdata('error_message', 'Phone number already exists.');
            //     redirect('admin/student/add');
            // }

                    $validity = $this->student_m->check_phone_duplication('on_create', $phone);
                    log_message("error","phondupl ".print_r($this->db->last_query(),true));
                    if ($validity == false) {
                        $this->session->set_flashdata('error_message', get_phrase('email_duplication'));
                    }else {
                        $data['first_name'] = html_escape($name);
                        $data['last_name'] = html_escape(" ");
                        $data['email'] = html_escape($phone);
                        // $data['branch'] = html_escape(" ");
                        $data['phone'] = html_escape($phone);
                        $data['password'] = sha1(html_escape($phone));
                        
                        $data['course']  =html_escape($_GET["course_id"]);
                        
                        
                        
                        // $social_link['facebook'] = html_escape(" ");
                        // $social_link['twitter'] = html_escape(" ");
                        // $social_link['linkedin'] = html_escape(" ");
                        // $data['social_links'] = json_encode($social_link);
                        // $data['biography'] = " ";
                        // $data['role_id'] = 2;
                        // $data['date_added'] = strtotime(date("Y-m-d H:i:s"));
                        // $data['wishlist'] = json_encode(array());
                        // $data['watch_history'] = json_encode(array());
                        // $data['status'] = 0;
                        
                        // Add paypal keys
                        // $paypal_info = array();
                        // $paypal['production_client_id']  = html_escape(" ");
                        // $paypal['production_secret_key'] = html_escape(" ");
                        // array_push($paypal_info, $paypal);
                        // $data['paypal_keys'] = json_encode($paypal_info);
                        
                        // Add Stripe keys
                        $stripe_info = array();
                        $stripe_keys = array(
                            'public_live_key' => html_escape(" "),
                            'secret_live_key' => html_escape(" ")
                        );
                        array_push($stripe_info, $stripe_keys);
                        $data['stripe_keys'] = json_encode($stripe_info);
                        $user_id=$this->db->insert('users', $data);
                        
                        if($user_id) {
                            // $dataab['course_id'] = $_GET["course_id"];
                            $dataab['user_id']   = $user_id;
                            
                            if ($this->db->get_where('enrol', $dataab)->num_rows() > 0) {
                                $dataa['course_id'] = $_POST["course_id"];
                                $dataa['user_id']   = $user_id;
                                $dataa['date_added'] = strtotime(date('D, d-M-Y'));
                                $this->db->where('user_id', $user_id);
                                $this->db->update('enrol', $dataa);
                            } else {
                                $dataa['course_id'] = $_POST["course_id"];
                                $dataa['user_id']   = $user_id;
                                $dataa['date_added'] = strtotime(date('D, d-M-Y'));
                                $this->db->insert('enrol', $dataa);
                            }
                        }
                    }
                    
                    // Check if the phone number already exists in the database
                    $phone = $this->input->post('country_code').$this->input->post('phone');
                    if ($this->student_m->check_phone_duplication('on_create', $phone)) {
                        $this->session->set_flashdata('error_message', 'Phone number already exists.');
                        redirect('admin/student/excel_upload');
                    }
                    $data['name']           = $this->input->post('name');
                    $data['category_id']    = $this->input->post('category_id');
                    $data['course_id']      = $this->input->post('course_id');
                    $data['lead_status']    = 1;
                    $data['cre_id']         = $this->input->post('cre_id');
                    // $data['lead_source']    = $this->input->post('lead_source');
                    // $data['followup_date']  = $this->input->post('followup_date');
                    // $data['biography']      = $this->input->post('biography');
                    $data['user_type']           = $this->input->post('user_type');
                    $data['country_code']   = $this->input->post('country_code');
                    $data['phone']          = $phone;
                    $data['phone_full']     = $this->input->post('country_code').$phone;
                    $data['created_on']     = date('Y-m-d H:i:s');
                    $data['updated_on']     = date('Y-m-d H:i:s');
                    
                    $user_id = $this->student_m->insert($data);
                    $this->student_m->upload_user_image($user_id);
                    
                    if($this->input->post('package_id')>0){
                        $package_id    =  $this->input->post('package_id');
                        $amount        =  $this->input->post('package_amount');
                        
                        $this->add_manual_payment($user_id,$package_id,$amount,$phone);
                    }
        
                    if ($user_id > 0){
                        $data2['lead_id'] 		= $user_id;
                        $data2['cre_id'] 		= $this->input->post('cre_id');
                        $data2['lead_status'] 	= 1;
                        $data2['lead_source']   = $this->input->post('lead_source');
                        $data2['followup_date'] = date('Y-m-d', strtotime($this->input->post('followup_date')));
                        $data2['remarks']       = $this->input->post('note');
                        $data2['created_on'] 	= date('Y-m-d H:i:s');
                        $data2['updated_on'] 	= date('Y-m-d H:i:s');
                        $this->lead_history_m->insert($data2);
                    }
                    
                }
            }

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/student/excel_upload/');
        }

        $this->data['categories']   = $this->category_m->get_parent_categories();
        $this->data['lead_source']  = $this->lead_source_m->get()->result_array();
        $this->data['counselors']   = $this->counselor_m->get()->result_array();
        // $this->data['packages']     = $this->package_m->get()->result_array();
        $this->data['page_title']   = 'Bulk Upload Students';
        $this->data['page_name']    = 'student/excel_upload';
        $this->load->view('admin/index', $this->data);
    }
    
  
    
    
    
    
}