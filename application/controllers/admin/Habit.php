<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Habit extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('habit_category_m');
        $this->load->model('habit_m');
        $this->load->model('user_goals_m');
    }

    public function index(){
        $this->data['categories']   = $this->habit_category_m->get()->result_array();
        if($_GET['category_id'] > 0){
            $this->db->where('habit_category_id', $_GET['category_id']);
        }
        $this->data['list_items']   = $this->user_goals_m->get_habit();
        $this->data['page_title']   = 'Habit';
        $this->data['page_name']    = 'habit/index';
        $this->load->view('admin/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data['title']             = $this->input->post('title');
            $data['habit_category_id'] = $this->input->post('habit_category');
            $data['description']       = $this->input->post('description');
            $data['time_period']       = $this->input->post('time_period');
            $data['start_date']        = $this->input->post('start_date');
            $data['end_date']          = $this->input->post('end_date');
            $data['created_on']        = date('Y-m-d H:i:s');

            // $this->habit_m->insert($data);
            $this->user_goals_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('habit_added_successfully'));

            redirect('admin/habit/index/');
        }
    }


    public function edit($id = 0){
        if ($this->input->post()){
            $data['title']             = $this->input->post('title');
            $data['habit_category_id'] = $this->input->post('habit_category');
            $data['description']       = $this->input->post('description');
            $data['time_period']       = $this->input->post('time_period');
            $data['start_date']        = $this->input->post('start_date');
            $data['end_date']          = $this->input->post('end_date');
            $data['updated_on']  = date('Y-m-d H:i:s');

            $this->user_goals_m->update($data, ['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully!'));


            redirect('admin/habit/index/');
        }
    }


    public function delete($id = 0){
        if ($id > 0){
            $this->user_goals_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/habit/index/');
        }
    }



}