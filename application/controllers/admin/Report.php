<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Report extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('package_m');
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('enrol_m');
        $this->load->model('report_m');
        $this->load->model('api_m');
        $this->load->model('lesson_m');
        $this->load->model('lesson_files_m');
        $this->load->model('module_completion_history_m');
        $this->load->model('quiz_attempt_m');
        $this->load->model('payment_info_m');
    }

    public function student_report(){
        $this->data['report']       = $this->report_m->get_student_report($this->input->get('course_id'));
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $this->data['page_title']   = 'Student Report';
        $this->data['page_name']    = 'report/student_report';
        $this->load->view('admin/index', $this->data);
    }
    
    public function module_status_report(){
        $this->data['report']       = $this->report_m->get_module_status_report($this->input->get('course_id'));
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $this->data['page_title']   = 'User Module Status Report';
        $this->data['page_name']    = 'report/module_status_report';
        $this->load->view('admin/index', $this->data);
    }
    
    public function wallet_balance_report(){
        $this->data['report']       = $this->report_m->get_wallet_balance_report();
        $this->data['page_title']   = 'Wallet Balance Report';
        $this->data['page_name']    = 'report/wallet_balance_report';
        $this->load->view('admin/index', $this->data);
    }
    
    public function wallet_validity_report(){
        $this->data['wallet']  = $this->report_m->get_wallet_report($this->input->get('course_id'));
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $this->data['page_title']   = 'Wallet Validity Report';
        $this->data['page_name']    = 'report/wallet_validity_report';
        $this->load->view('admin/index', $this->data);
    }
    
    public function activity_report(){
        $this->db->select('id, title');
        $this->data['courses'] = $this->db->get('course')->result_array();
        $this->data['page_title']   = 'Activity Report';
        $this->data['page_name']    = 'report/activity_report';
        $this->load->view('admin/index', $this->data);
        
    }
    
    public function uploads_activity($std_id, $course_id){
        $this->db->select("users.name as student_name,lesson.title as lesson_name,lesson_files.title as Activity_name,user_activity.*");
        $this->db->where("user_activity.user_id", $std_id);
        $this->db->join('users', 'user_activity.user_id = users.id');
        $this->db->join('lesson', 'user_activity.lesson_id = lesson.id');
        $this->db->join('lesson_files', 'lesson_files.id = user_activity.activity_id');
        $this->db->where('lesson.course_id',$course_id);
        $this->data['uploaded_activitys'] = $this->db->get('user_activity')->result_array();
        
        $this->data['page_title']   = 'Activity Reports';
        $this->data['page_name']    = 'report/uploaded_activitys';
        $this->load->view('admin/index', $this->data);
        
    }
    public function get_students($course_id){
        $lesson_ids =  array_column($this->db->get_where('lesson', array('course_id' => $course_id))->result_array(),'id');
        // $lesson_id = $lesson_id[0]['id'];
        
        $this->db->select("users.id, users.name, user_activity.id as activity_exist");
        $this->db->where("enrol.course_id", $course_id);
        $this->db->join('users', 'enrol.user_id = users.id');
        $this->db->join('lesson', 'enrol.course_id = lesson.course_id', 'left');
        $this->db->join('user_activity', 'user_activity.user_id = enrol.user_id','left');
        $this->db->where_in('user_activity.lesson_id', $lesson_ids);
        $this->db->group_by('enrol.user_id');
        
        // Add conditional select to prioritize data from 'enrol' table over 'user_activity' table
        $this->db->select("CASE WHEN user_activity.id IS NOT NULL THEN user_activity.id ELSE NULL END AS activity_exist", FALSE);
        
        $this->data['students'] = $this->db->get('enrol')->result_array();

        $this->data['page_title']   = 'Activity Report';
        $this->data['course_id']   = $course_id;
        $this->data['page_name']    = 'report/students_name';
        $this->load->view('admin/index', $this->data);

    }
    
    
    
    public function user_purchase_report(){
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        
        $this->db->select('users.id, users.name, users.phone, users.user_email, users.created_on as registered_on');
        $this->db->join('enrol', 'users.id = enrol.user_id', 'left'); // Join to ensure only enrolled students are selected
        $this->db->join('course', 'enrol.course_id = course.id', 'left');
        
        $this->db->where('users.role_id', 2); // Only fetch students (role_id = 2)
        
        if (!empty($_GET['registered_date'])) {
            $this->db->where('DATE(users.created_on)', $_GET['registered_date']);
        }
        if (!empty($_GET['course_id'])) {
            $this->db->where('course.id', $_GET['course_id']); // Filter by course
        }
        $this->db->group_by('users.id');
        
        $students = $this->db->get('users')->result_array();
        
        $filtered_students = []; // Store only students matching the filter
        
        foreach ($students as $key => $student) {
            $this->db->select('enrol.id as enrol_id, enrol.course_id, enrol.created_on as enrol_date, course.title as course_name');
            $this->db->join('course', 'enrol.course_id = course.id');
            $this->db->where('enrol.user_id', $student['id']);
            
            if (!empty($_GET['course_id'])) {
                $this->db->where('course.id', $_GET['course_id']);
            }
            
            $enrol_data = $this->db->get('enrol')->result_array();
            $has_paid_purchase = false;
            $has_any_purchase = false;
        
            foreach ($enrol_data as $key2 => $value) {
                $this->db->select('payment_info.id as payment_id, payment_info.package_id, payment_info.amount_paid, payment_info.payment_date, payment_info.expiry_date, payment_info.razorpay_payment_id, package.title as package_name');
                $this->db->join('package', 'payment_info.package_id = package.id', 'left');
                $this->db->join('course', 'package.course_id = course.id', 'left');
                $this->db->where('course.id', $value['course_id']);
                $this->db->where('payment_info.user_id', $student['id']);
        
                if (!empty($_GET['purchase_date'])) {
                    $this->db->where('DATE(payment_info.payment_date)', $_GET['purchase_date']);
                }
                
                if (!empty($_GET['payment_through'])) {
                    $this->db->where('payment_info.payment_through', $_GET['payment_through']);
                }
                
                if (!empty($_GET['payment_type'])) {
                    if ($_GET['payment_type'] === 'manual') {
                        $this->db->where('LEFT(payment_info.razorpay_payment_id, 3) =', 'man');
                    } elseif ($_GET['payment_type'] === 'online') {
                        $this->db->where('LEFT(payment_info.razorpay_payment_id, 3) =', 'pay');
                    }
                }
        
                $purchase_data = $this->db->get('payment_info')->result_array();
                if (!empty($purchase_data)) {
                    $has_any_purchase = true;
                    $has_paid_purchase = true;
                }
        
                $enrol_data[$key2]['purchase'] = $purchase_data;
                $progress = $this->db->get_where('student_course_progress', ['user_id' => $student['id'], 'course_id' => $value['course_id']])->row();
                $enrol_data[$key2]['course_progress'] = $progress ? $progress->course_progress : 0;
            }
        
            // Apply Purchase Status Filter Correctly
            if (!empty($_GET['purchase_status'])) {
                if($_GET['purchase_status'] === 'paid' && !$has_paid_purchase) {
                    continue; // Skip students without paid purchases
                } elseif ($_GET['purchase_status'] === 'not_paid' && $has_any_purchase) {
                    continue; // Skip students who have made any purchase
                }
            }
        
            $student['enrol_data'] = $enrol_data;
            $filtered_students[] = $student; // Add student only if they pass the filter
        }
        
        $students = $filtered_students; // Update the students array with filtered data
        $this->data['report']       = $students;
        $this->data['page_title']   = 'Purchase Report';
        $this->data['page_name']    = 'report/purchase_report';
        $this->load->view('admin/index', $this->data);
    }
    
    
    
    public function exam_report(){
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        
        $this->data['report']       = $exam_report;
        $this->data['page_title']   = 'Exam Report';
        $this->data['page_name']    = 'report/exam_report';
        $this->load->view('admin/index', $this->data);
    }
    
    
    public function quiz_report(){
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        
        
        $this->db->select("user_id");
        $this->db->where('quiz_id', $_GET["quiz_id"]);
        $this->db->group_by('user_id');
        $attended_users = $this->db->get('quiz_answer')->result_array();
        $user_ids = array_column($attended_users, 'user_id');
    
        $this->db->where('quiz_id', $_GET["quiz_id"]);
        $question_count = $this->db->get('quiz_questions')->num_rows();
     
        $quiz = $this->db->get_where('lesson_files', ['id' => $_GET["quiz_id"], 'attachment_type' => 'practice'])->row_array();

        $users = [];
        if ($user_ids != NULL) {
            $this->db->select('id, name, phone, email');
            $this->db->where_in('id', $user_ids);
            $users = $this->db->get('users')->result_array();
        }
    
        $report = [];
        foreach($users as $row) {
            $data = [];
            $quiz_attempt = $this->quiz_attempt_m->get_last_quiz_attempt($row['id']);
            
            $exams = $this->quiz_attempt_m->get_quiz_by_user_id($row['id'],$_GET["quiz_id"], $quiz_attempt['id']);

            $data['user_id'] = $row['id'];
            $data['name']    = $row['name'];
            $data['phone']   = $row['phone'];
            
            $unattempted_answer_array = [];
            
            foreach ($exams as $answer) {
                $data['quiz_id'] = $answer['quiz_id'];
                $data['attempt_id'] = $answer['attempt_id'];
                $data['unattempted_count'] = $answer['skipped_answers'];
                $data['attempted_count'] = $question_count - $data['unattempted_count'];
            }

            $report[] = $data;
        }
        
        $this->data['report'] = $report;
        $this->data['page_title']   = 'Quiz Report';
        $this->data['page_name']    = 'report/quiz_report';
        $this->load->view('admin/index', $this->data);
    }
     
    
    public function registered_not_enrolled(){
        $start_date = $this->input->get('start_date');
        $end_date   = $this->input->get('end_date');
        $this->db->select('users.id, users.name, users.phone, users.email, users.created_on');
        $this->db->from('users');
        $this->db->join('enrol', 'users.id = enrol.user_id', 'left');
        $this->db->where('enrol.user_id IS NULL');
        $this->db->where('users.role_id', 2);
        if(!empty($start_date)){
            $this->data['start_date'] = $start_date;
            $this->db->where('date(users.created_on) >=', $start_date);
        }
        
        if(!empty($end_date)){
            $this->data['end_date'] = $end_date;
            $this->db->where('date(users.created_on) <=', $end_date);
        }
        $this->data['report'] =  $this->db->get()->result_array();

        $this->data['page_title'] = 'Not Enrolled Students';
        $this->data['page_name']  = 'report/registered_not_enrolled';
        $this->load->view('admin/index', $this->data);
    }
    
    
    public function enrolled_not_purchased(){
        $start_date = $this->input->get('start_date');
        $end_date   = $this->input->get('end_date');
        $course_id  = $this->input->get('course_id');
        $this->db->select('users.id, users.name, users.phone, users.email, users.created_on');
        $this->db->from('enrol');
        $this->db->join('users', 'enrol.user_id = users.id', 'inner');
        $this->db->join('payment_info', 'enrol.user_id = payment_info.user_id', 'left');
        $this->db->where('payment_info.user_id IS NULL');
        if(!empty($start_date)){
            $this->data['start_date'] = $start_date;
            $this->db->where('date(users.created_on) >=', $start_date);
        }
        
        if(!empty($end_date)){
            $this->data['end_date'] = $end_date;
            $this->db->where('date(users.created_on) <=', $end_date);
        }
        
        if($course_id > 0){
            $this->data['course_id'] = $course_id;
            $this->db->where('enrol.course_id', $course_id);
        }
        $this->data['report'] =  $this->db->get()->result_array();

        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $this->data['page_title'] = 'Enrolled - Not Purchased students';
        $this->data['page_name']  = 'report/enrolled_not_purchased';
        $this->load->view('admin/index', $this->data);
    }
    
    
    public function registered_not_purchased(){
        $start_date = $this->input->get('start_date');
        $end_date   = $this->input->get('end_date');
        $course_id  = $this->input->get('course_id');
        $this->db->select('users.id, users.name, users.phone, users.email, users.created_on');
        $this->db->from('users');
        $this->db->join('payment_info', 'users.id = payment_info.user_id', 'left');
        $this->db->where('payment_info.user_id IS NULL');
        $this->db->where('users.role_id', 2);
        if(!empty($start_date)){
            $this->data['start_date'] = $start_date;
            $this->db->where('date(users.created_on) >=', $start_date);
        }
        
        if(!empty($end_date)){
            $this->data['end_date'] = $end_date;
            $this->db->where('date(users.created_on) <=', $end_date);
        }
        
        if($course_id > 0){
            $this->data['course_id'] = $course_id;
            $this->db->join('enrol', 'enrol.user_id = users.id', 'inner');
            $this->db->where('enrol.course_id', $course_id);
        }
        $this->data['report'] =  $this->db->get()->result_array();

        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $this->data['page_title'] = 'Registered - Not Purchased students';
        $this->data['page_name']  = 'report/registered_not_purchased';
        $this->load->view('admin/index', $this->data);
    }
    
    
    public function course_completed_students(){
        $course_id  = $this->input->get('course_id');
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        $course_title_array  = array_column($this->data['course_list'], 'title', 'id');
        
        if ($course_id > 0) {
            $this->db->select('users.id, users.name, users.phone, users.email, users.created_on as registered_on, enrol.course_id, enrol.created_on as enrol_date');
            $this->db->from('enrol');
            $this->db->join('users', 'enrol.user_id = users.id', 'inner'); // Ensures only users in enrol table are fetched
            $this->data['course_id'] = $course_id;
            $this->db->where('enrol.course_id', $course_id);
        
            $enrolled_students = $this->db->get()->result_array();
            
            $course_completed_students = [];
            $incompleted_students = [];
            foreach($enrolled_students as $key=> $student){
                $progress = $this->api_m->course_progress_data($student['id'], $student['course_id']);
                $enrolled_students[$key]['course_title'] = $course_title_array[$student['course_id']];
                $enrolled_students[$key]['progress'] = $progress['course_progress'];
                $enrolled_students[$key]['course_completed'] = $progress['course_progress'] < 100 ? 0 : 1;
                
                // If the student has completed the course, add them to the completed students array
                if ($enrolled_students[$key]['course_completed'] == 1) {
                    $course_completed_students[] = $enrolled_students[$key];
                }else{
                    $incompleted_students[] = $enrolled_students[$key];
                }
            }
            $this->data['report'] =  $course_completed_students;
        } else {
            $this->data['report'] =  [];
        }
        
        $course_titles = $course_id >0 ? $course_title_array[$course_id] : '';
        $this->data['course_titles']  = $course_titles;
        $this->data['page_title'] = $course_id >0 ? 'Course Completed Students - ' . $course_titles : 'Course Completed Students';
        $this->data['page_name']  = 'report/course_completed_students';
        $this->load->view('admin/index', $this->data);
    }
    
    
    

}