<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Topic extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('topic_m');
        $this->load->model('section_m');
    }

    /*
     * Add Section
     */
    public function add(){
        if ($this->input->post()){
            $data['title']      = html_escape($this->input->post('title'));
            $data['section_id']  = $this->input->post('section_id');
            $course_id = $this->section_m->get(['id' => $data['section_id']])->row()->course_id;
            if (!file_exists('uploads/thumbnails/topic_thumbnails')) {
                mkdir('uploads/thumbnails/topic_thumbnails', 0777, true);
            }
            if ($_FILES['category_thumbnail']['name'] == "") {
                $data['thumbnail'] = 'category-thumbnail.png';
            } else {
                $data['thumbnail'] = md5(rand(10000000, 20000000)) . '.jpg';
                move_uploaded_file($_FILES['category_thumbnail']['tmp_name'], 'uploads/thumbnails/topic_thumbnails/' . $data['thumbnail']);
            }
            if ($_FILES['category_thumbnaila']['name'] == "") {
                $data['background'] = 'category-thumbnail.png';
            } else {
                $data['background'] = md5(rand(10000000, 20000000)) . '.jpg';
                move_uploaded_file($_FILES['category_thumbnaila']['tmp_name'], 'uploads/thumbnails/topic_thumbnails/' . $data['background']);
            }
            if ($_FILES['icon']['name'] == "") {
                $data['icon'] = 'category-thumbnail.png';
            } else {
                $data['icon'] = md5(rand(10000000, 20000000)) . '.jpg';
                move_uploaded_file($_FILES['icon']['tmp_name'], 'uploads/thumbnails/topic_thumbnails/' . $data['icon']);
            }


            $data['free'] = html_escape($this->input->post('free')) == 'on' ? 'on' : 'off';
            $data['created_on'] = date('Y-m-d H:i:s');
            $data['updated_on'] = date('Y-m-d H:i:s');

            $section_id = $this->topic_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/course/edit/'.$course_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }

    /*
     * Edit Section
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/course/index/');
        }else{
            if ($this->input->post()){
                $data['title'] = $this->input->post('title');

                if (!file_exists('uploads/thumbnails/topic_thumbnails')) {
                    mkdir('uploads/thumbnails/topic_thumbnails', 0777, true);
                }
                if (!empty($_FILES['category_thumbnail']['name'])) {
                    $data['thumbnail'] = md5(rand(10000000, 20000000)) . '.jpg';
                    move_uploaded_file($_FILES['category_thumbnail']['tmp_name'], 'uploads/thumbnails/topic_thumbnails/' . $data['thumbnail']);
                }

                if (!empty($_FILES['icon']['name'])) {
                    $data['icon'] = md5(rand(10000000, 20000000)) . '.jpg';
                    move_uploaded_file($_FILES['icon']['tmp_name'], 'uploads/thumbnails/topic_thumbnails/' . $data['icon']);
                }
                $data['free'] = html_escape($this->input->post('free')) == 'on' ? 'on' : 'off';

                $this->topic_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));

                $section_id = $this->topic_m->get(['id' => $id])->row()->section_id;
                $course_id = $this->section_m->get(['id' => $section_id])->row()->course_id;

                redirect('admin/course/edit/'.$course_id.'/');
            }
        }
    }

    /*
     * Delete Category
     */
    public function delete($id = 0){
        if ($id > 0){
            $section_id = $this->topic_m->get(['id' => $id])->row()->section_id;
            $course_id = $this->section_m->get(['id' => $section_id])->row()->course_id;

            $this->topic_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/course/edit/'.$course_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }
    
    
    /*
     * Get Section by Course ID
     */
    public function ajax_get_topic_by_section(){
        $section_id = $this->input->get('section_id');
        $topics = $this->topic_m->get_topic_by_section_id($section_id)->result_array();
        echo "<option value=''>Select Topic</option>";
        foreach ($topics as $topic){
            echo "<option value='{$topic['id']}'>{$topic['title']}</option>";
        }
    }
    
}