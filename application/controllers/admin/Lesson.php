<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Lesson extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('lesson_m');
        $this->load->model('section_m');
    }

    /*
     * Add Lesson
     */
    public function add(){
        if ($this->input->post()){
            $daily_task = $this->lesson_m->get(['module_category_id' => 0, 'course_id' => $this->input->post('course_id'), 'section_id' => $this->input->post('section_id')])->num_rows();
            $data['course_id']      = html_escape($this->input->post('course_id'));
            $data['title']          = html_escape($this->input->post('title'));
            $data['section_id']     = html_escape($this->input->post('section_id'));
            $data['topic_id']       = html_escape($this->input->post('topic_id'));
            $data['module_category_id'] =  $this->input->post('module_category_id');
            $data['lesson_type']    = html_escape($this->input->post('lesson_type'));
            $data['schedule_date']  = $this->input->post('schedule_date');
            $data['tips']           = $this->input->post('tips');

            $data['summary']        = $this->input->post('summary');
            $data['free']           = html_escape($this->input->post('free')) == 'on' ? 'on' : 'off';

            $data['created_on']     = date('Y-m-d H:i:s');
            $data['updated_on']     = date('Y-m-d H:i:s');
            
            if (!file_exists('uploads/thumbnails/lesson_thumbnails')) {
                mkdir('uploads/thumbnails/lesson_thumbnails', 0777, true);
            }
            if ($_FILES['thumbnail']['name'] == "") {
                $data['thumbnail'] = 'category-thumbnail.png';
            } else {
                $data['thumbnail'] = md5(rand(10000000, 20000000)) . '.jpg';
                move_uploaded_file($_FILES['thumbnail']['tmp_name'], 'uploads/thumbnails/lesson_thumbnails/' . $data['thumbnail']);
            }
            
            if($this->input->post('module_category_id')==0){
                if($daily_task > 0){
                    $this->session->set_flashdata('error_message', get_phrase('Daily Task already added to this section'));
                }else{
                    $lesson_id = $this->lesson_m->insert($data);
                    $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
                }
            }else{
                $lesson_id = $this->lesson_m->insert($data);
                $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            }
            
            redirect('admin/course/edit/'.$data['course_id'].'/');
        }else{
            redirect('admin/course/index/');
        }
    }

    /*
     * Edit Lesson
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/course/index/');
        }else{
            if ($this->input->post()){
                $daily_task = $this->lesson_m->get(['id!=' => $id, 'module_category_id' => 0, 'course_id' => $this->input->post('course_id'), 'section_id' => $this->input->post('section_id')])->num_rows();
                $data['course_id']      = html_escape($this->input->post('course_id'));
                $data['title']          = html_escape($this->input->post('title'));
                $data['section_id']     = html_escape($this->input->post('section_id'));
                $data['topic_id']       = html_escape($this->input->post('topic_id'));
                $data['module_category_id'] =  $this->input->post('module_category_id');
                $data['lesson_type']    = html_escape($this->input->post('lesson_type'));
                $data['tips']           = $this->input->post('tips');
                $data['schedule_date']  = $this->input->post('schedule_date');
                $data['summary']        = $this->input->post('summary');
                $data['free']           = html_escape($this->input->post('free')) == 'on' ? 'on' : 'off';
                $data['updated_on']     = date('Y-m-d H:i:s');
                
                log_message('error','tips: '.print_r($data['tips'],true));
                if (!file_exists('uploads/thumbnails/lesson_thumbnails')) {
                    mkdir('uploads/thumbnails/lesson_thumbnails', 0777, true);
                }
                if ($_FILES['thumbnail']['name'] == "") {
                    $data['thumbnail'] = 'category-thumbnail.png';
                } else {
                    $data['thumbnail'] = md5(rand(10000000, 20000000)) . '.jpg';
                    move_uploaded_file($_FILES['thumbnail']['tmp_name'], 'uploads/thumbnails/lesson_thumbnails/' . $data['thumbnail']);
                }
                
                if($this->input->post('module_category_id')==0){
                    if($daily_task > 0){
                        $this->session->set_flashdata('error_message', get_phrase('Daily Task already added to this section'));
                    }else{
                        $this->lesson_m->update($data, ['id' => $id]);
                        $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                    }
                }else{
                    $this->lesson_m->update($data, ['id' => $id]);
                    $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                }
                redirect('admin/course/edit/'.$data['course_id'].'/');
            }
        }
    }

    /*
     * Delete Lesson
     */
    public function delete($id = 0){
        if ($id > 0){
            $course_id = $this->lesson_m->get(['id' => $id])->row()->course_id;

            $this->lesson_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/course/edit/'.$course_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }


    /*
     * Add Bulk Lesson
     */
    public function add_bulk(){
        if ($this->input->post()){
            $title_prefix   = $this->input->post('title_prefix');
            $course_id      = $this->input->post('course_id');
            $section_id     = $this->section_m->get(['course_id' => $course_id])->row()->id;
            $start_no       = $this->input->post('start_no');
            $end_no         = $this->input->post('end_no');
            $lesson_arr     = range($start_no, $end_no);
            $free           = $this->input->post('free') == 'on' ? 'on' : 'off';
            $insert_data    = [];
            foreach ($lesson_arr as $lesson_item){
                $insert_data[] = [
                    'title'      => $title_prefix.' '.$lesson_item,
                    'course_id'  => $course_id,
                    'section_id' => $section_id,
                    'free'       => $free,
                    'created_on' => date('Y-m-d H:i:s'),
                    'updated_on' => date('Y-m-d H:i:s'),
                ];
            }
            $this->lesson_m->insert_batch($insert_data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/course/edit/'.$course_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }
    
    
    /*
     * Get Lesson by Section ID
     */
    public function ajax_get_lesson_by_section(){
        $section_id = $this->input->get('section_id');
        $lessons = $this->lesson_m->get_lesson_by_section_id($section_id)->result_array();
        echo "<option value=''>Select Lesson</option>";
        foreach ($lessons as $lesson){
            echo "<option value='{$lesson['id']}'>{$lesson['title']}</option>";
        }
    }
    
    
    /*
     * Get Lesson by Section ID
     */
    public function ajax_get_exam_lesson_by_section(){
        $section_id = $this->input->get('section_id');
        $lessons = $this->lesson_m->get_exam_lesson_by_section_id($section_id)->result_array();
        echo "<option value=''>Select Lesson</option>";
        foreach ($lessons as $lesson){
            echo "<option value='{$lesson['id']}'>{$lesson['title']}</option>";
        }
    }
    
    
    /*
     * Get Lesson by Section ID
     */
    public function ajax_get_liveclass_lesson_by_course(){
        $course_id = $this->input->get('course_id');
        $lessons = $this->lesson_m->get(['course_id' => $course_id])->result_array();
        log_message("error","@@1 ".print_r($this->db->last_query(),true));
        echo "<option value=''>Select Lesson</option>";
        foreach ($lessons as $lesson){
            echo "<option value='{$lesson['id']}'>{$lesson['title']}</option>";
        }
    }
    
    
    /*
     * Sort Lesson
     */
    public function ajax_sort_lesson() {
        $lesson_json = $this->input->post('itemJSON');
        $this->lesson_m->sort_lesson($lesson_json);
    }
    
    
    
}