<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Transaction extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('expense_m');
        $this->load->model('payment_info_m');
        $this->load->model('accounts_m');
        $this->load->model('transaction_m');
    }

    /*
     * Index Page
     */
    public function index(){
        //filter expense
        $this->data['start_date'] = $this->input->get('start_date');
        $this->data['end_date'] = $this->input->get('end_date');
        $this->data['account_id'] = $this->input->get('account_id');

        if (empty($this->data['start_date'])){ $this->data['start_date'] = date('Y-m-01');}
        if (empty($this->data['end_date'])){$this->data['end_date'] = date('Y-m-t');}

        $this->data['list_items'] = $this->transaction_m->get_transactions($this->data['start_date'], $this->data['end_date'], $this->data['account_id']);
        $this->data['accounts_list'] = $this->accounts_m->get_accounts_with_balance();
        $this->data['page_title']   = 'Transactions';
        $this->data['page_name']    = 'transaction/index';
        $this->load->view('admin/index', $this->data);
    }


}