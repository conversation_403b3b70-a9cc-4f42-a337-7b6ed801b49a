<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Feed extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('feed_m');
        $this->load->model('feed_category_m');
    }
    
    
    /*
    * Index Page
    */
    public function index(){
        $this->data['categories']   = $this->feed_category_m->get()->result_array();
        $this->data['list_items']   = $this->feed_m->get()->result_array();
        // log_message('error',print_r($this->data['list_items'],true));
        $this->data['page_title']   = 'Feed';
        $this->data['page_name']    = 'feed/index';
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*
     * Add Feed
     */
    public function add(){
        if ($this->input->post()){
             
            $data['title'] = $this->input->post('title');
            $data['content'] = html_escape($this->input->post('content'));
            $data['category_id'] = $this->input->post('category_id');
            $data['course_id']  = $this->input->post('course_id');
            $data['video_url']  = $this->input->post('video_url');
            $data['created_on'] = date('Y-m-d H:i:s');
            $file_upload = $this->upload_file('image', 'image');
            if($file_upload!= false){
                $data['image'] = $file_upload['file'];
            }else{
                $data['image'] = '';
            }
            $feed = $this->feed_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/feed/index/');
        }

        $this->data['courses']   = $this->course_m->get()->result_array();
        $this->data['page_title']   = 'Add Feed';
        $this->data['page_name']    = 'feed/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
     /*
     * Edit Feed
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/feed/index/');
        }else{
            if ($this->input->post()){
                $data['title'] = $this->input->post('title');
                $data['content'] = html_escape($this->input->post('content'));
                $data['category_id'] = $this->input->post('category_id');
                $data['course_id']  = $this->input->post('course_id');
                $data['video_url']  = $this->input->post('video_url');
                $file_upload = $this->upload_file('image', 'image');
                if($file_upload!= false){
                    $data['image'] = $file_upload['file'];
                }
                $data['updated_on']     = date('Y-m-d H:i:s'); 
                
                $this->feed_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/feed/index/');
            }
        }
        $this->data['courses']      = $this->course_m->get()->result_array();
        $this->data['edit_data']    = $this->feed_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'Feed Edit';
        $this->data['page_name']    = 'feed/edit';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Delete Feed
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->feed_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/feed/index/');
    }
    
    
}    