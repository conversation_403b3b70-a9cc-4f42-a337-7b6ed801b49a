<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Lead_source extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('lead_source_m');
        $this->load->model('lead_status_m');
    }

    /*
     * Index Page
     */
    public function index(){
        $this->data['list_items']   = $this->lead_source_m->get()->result_array();
        $this->data['page_title']   = 'Lead Source';
        $this->data['page_name']    = 'lead_source/index';
        $this->load->view('admin/index', $this->data);
    }

     /*
     * Add lead
     */
    public function add(){
        if ($this->input->post()){
             
            $data['source'] = $this->input->post('source');
           
            $leadsource = $this->lead_source_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/lead_source/index/');
        }

        $this->data['page_title']   = 'Add Lead Source';
        $this->data['page_name']    = 'lead_source/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
     /*
     * Edit lead
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('admin/lead_source/index/');
        }else{
            if ($this->input->post()){
                $data['source'] = $this->input->post('source');
                
                $this->lead_source_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/lead_source/index/');
            }
        }
        $this->data['edit_data']    = $this->lead_source_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'Lead Source Edit';
        $this->data['page_name']    = 'lead_source/edit';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Delete lead
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->lead_source_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/lead_source/index/');
    }
    
    
    
    
    
    
}