<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Lead extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('counselor_m');
        $this->load->model('lead_source_m');
        $this->load->model('lead_status_m');
        $this->load->model('lead_history_m');
    }

  
    
    /*Lead Report*/
    
    public function lead_report() {
        
        $status = $this->input->get('status');
        $lead_source = $this->input->get('lead_source');
        $this->data['statuslist'] = $this->lead_status_m->get()->result_array();
        $this->data['lead_sources'] = $this->lead_source_m->get()->result_array();
        // get the lead sources from the database and store them in an array
        $lead_sources = $this->lead_source_m->get()->result_array();
        $statuslist = $this->lead_status_m->get()->result_array();
        
        $lead_status_list = array();
        foreach ($statuslist as $value) {
            $lead_status_list[$value['id']] = $value['status'];
        }
        
        // pass the lead source list to the view
        $this->data['lead_status_list'] = $lead_status_list;
        
        
        // create a new array that maps the lead source IDs to their corresponding names
        $lead_source_list = array();
        foreach ($lead_sources as $source) {
            $lead_source_list[$source['id']] = $source['source'];
        }
        
        // pass the lead source list to the view
        $this->data['lead_source_list'] = $lead_source_list;
        
        // get the leads from the database and pass them to the view
        $this->data['leads'] = $this->student_m->get(['lead_status'=>$status,'lead_source'=>$lead_source])->result_array();

        // log_message('error',print_r($this->data['leads'],true));
        
        $this->data['page_name'] = 'lead/lead_report';
        $this->data['page_title'] = "Lead Report";
        
        $this->load->view('admin/index', $this->data);
    }
    
    
    
    /*Lead Source Report*/
    
    public function lead_source_report() {
        
        $from_date = date('Y-m-d', strtotime($this->input->get('from_date')));
        $to_date = date('Y-m-d', strtotime($this->input->get('to_date')));

        $lead_source = $this->input->get('lead_source');
        
        $this->data['lead_sources'] = $this->lead_source_m->get()->result_array();
        $lead_sources = $this->lead_source_m->get()->result_array();
         $statuslist = $this->lead_status_m->get()->result_array();
        
        $lead_status_list = array();
        foreach ($statuslist as $value) {
            $lead_status_list[$value['id']] = $value['status'];
        }
        
        $this->data['lead_status_list'] = $lead_status_list;
        
        $lead_source_list = array();
        foreach ($lead_sources as $source) {
            $lead_source_list[$source['id']] = $source['source'];
        }
        
        $this->data['lead_source_list'] = $lead_source_list;
        
        $this->data['leads'] = $this->student_m->get(['created_on >=' => $from_date,'created_on <=' => $to_date,'lead_source'=>$lead_source])->result_array();

        // log_message('error',print_r($this->data['leads'],true));
        
        $this->data['page_name'] = 'lead/lead_source_report';
        $this->data['page_title'] = "Lead Source Report";
        
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*Lead History*/
    public function lead_history($id=0){
        $this->data['list_items'] =  $this->lead_history_m->get(['lead_id' => $id])->result_array();
        $this->data['page_name'] = 'lead/lead_history';
        $this->data['page_title'] = "Lead History";
        $this->data['user_id'] = $id;
        $this->load->view('admin/index', $this->data);
    }
    
    
    /* Change Lead Status */
    public function change_status($id=0){
        if ($this->input->post()){
            $data['lead_status']    = $this->input->post('lead_status');
            $data['lead_source']    = $this->input->post('lead_source');
            $data['lead_note']      = $this->input->post('note');
            $data['followup_date']  = date('Y-m-d', strtotime($this->input->post('followup_date')));
            $this->student_m->update($data, ['id' => $id]);
            
            if ($id > 0){
                $data2['lead_id'] 		= $id;
                $data2['cre_id'] 		= $this->input->post('cre_id');
                $data2['lead_status'] 	= $this->input->post('lead_status');
                $data2['lead_source']   = $this->input->post('lead_source');
                $data2['followup_date'] = date('Y-m-d', strtotime($this->input->post('followup_date')));
                $data2['remarks']       = $this->input->post('note');
                $data2['created_on'] 	= date('Y-m-d H:i:s');
                $data2['updated_on'] 	= date('Y-m-d H:i:s');
                $this->lead_history_m->insert($data2);
            }
            redirect('admin/lead/lead_history/'.$id);
        }
        
        $this->data['lead_data']    = $this->student_m->get(['id' => $id])->row_array();
        $this->data['lead_status'] = $this->lead_status_m->get()->result_array();
        $this->data['page_title']   = 'Student Edit';
        $this->data['page_name']    = 'student/change_lead_status';
        $this->load->view('admin/index', $this->data);
    }
    
    
    
    
    public function lead_bulk_upload(){
        if($this->input->post()){
            /**
			 * Step 1: if assign_all_counselors == 1  select all counselors or selected counselors
			 * Step 2: for each counselors selected assign leads equally
			 */
			//assign counselors
			if($this->input->post('assign_all_counselor') != 1){
				$counselors = $this->input->post('counselors');
			}else{
				$counselors = $this->counselor_m->get()->result_array();
				$counselors = array_column($counselors, 'id');
			}
            log_message('error', json_encode($counselors));

			$counselors_count = count($counselors);
			
			
            $this->load->library('excel');
            $path 		 = $_FILES["file"]["tmp_name"];
            $excel_title = $this->input->post('excel_title');
            $duplicate 	= 0;
            $lead_upload_attempted      = 0;
            $lead_upload_completed      = 0;
            $counselor_assign_count     = 0;
            $global_duplication         = 0;
            $sheet_duplication          = 0;
            $excel_leads                = [];

            //insert leads upload information
            $this->db->insert('lead_upload', ['created_on' => date('Y-m-d H:i:s')]);
            $upload_id = $this->db->insert_id();

            $object = PHPExcel_IOFactory::load($path);
            foreach($object->getWorksheetIterator() as $worksheet){
                $highest_row 		= $worksheet->getHighestRow();
                $highest_column 	= $worksheet->getHighestColumn();

                $k=0;
                for($row=2; $row <= $highest_row; $row++){

                    $data = [];

                    $code   = remove_excel_icon($worksheet->getCellByColumnAndRow(1, $row)->getValue());
                    $phone  = remove_excel_icon($worksheet->getCellByColumnAndRow(2, $row)->getValue());
                    if(empty($code) || empty($phone)){
                        continue;
                    }

                    $data['name'] 	        = remove_excel_icon($worksheet->getCellByColumnAndRow(0, $row)->getValue());
                    $data['code'] 		    = $code;
                    $data['phone'] 		    = $code.$phone;
                    $data['email'] 	  	    = remove_excel_icon($worksheet->getCellByColumnAndRow(3, $row)->getValue());
                    $data['role_id'] 	    = 2;
                    $data['lead_status']    = 1;
                    $UNIX_DATE              = ($worksheet->getCellByColumnAndRow(4, $row)->getValue() - 25569) * 86400;
                    $data['followup_date']  = gmdate("Y-m-d", $UNIX_DATE);
                    $data['lead_note']      = remove_excel_icon($worksheet->getCellByColumnAndRow(5, $row)->getValue());
                    $data['upload_id'] 	    = $upload_id;
                    $data['created_by']     = get_user_id();
                    $data['updated_by']     = get_user_id();
                    $data['created_on']     = date('Y-m-d H:i:s');
                    $data['updated_on']     = date('Y-m-d H:i:s');
                    //check globally duplication
                    $phone_validity = $this->user_m->check_phone_duplication('create',  $data['phone']);

                    if(!$phone_validity){
                        $data['meta']['import'] = 1;
                        $global_duplication++;
                    }else{
                        //check sheet duplication
                        $duplicate_key = $this->search_for_duplicate_entry($data['phone'], $excel_leads);
                        if($duplicate_key==1){
                            $data['meta']['import']     = 2;
                            $sheet_duplication++;
                        }else{
                            $data['meta']['import']     = 3;
                            $counselor_assign_count++;
                            $data['cre_id'] = $counselors[($counselor_assign_count % $counselors_count)];
                        }
                    }
                    $excel_leads[] = $data;
                }
            }


            foreach ($excel_leads as $lead_key => $excel_lead){
                if($excel_lead['meta']['import'] == 3){
                    unset($excel_lead['meta']);
                    $lead_id = $this->users_model->add($excel_lead);
                    if($excel_lead['cre_id'] > 0){
                        $data2['lead_id'] 		= $lead_id;
                        $data2['cre_id'] 		= $excel_lead['cre_id'];
                        $data2['lead_status'] 	= 1;
                        $data2['followup_date'] = $excel_lead['followup_date'];
                        $data2['remarks'] 	    = $excel_lead['lead_note'];
                        $data2['created_on'] 	= date('Y-m-d H:i:s');
                        $data2['updated_on'] 	= date('Y-m-d H:i:s');
                        $this->lead_history_m->insert($data2);
                    }
                    $excel_leads[$lead_key]['lead_id'] = $lead_id;
                    $lead_upload_completed++;
                }
                $lead_upload_attempted++;
            }

            //update leads upload information
            $this->db->where(['id' => $upload_id]);
            $this->db->update('leads_upload', [
                'attempted' => $lead_upload_attempted,
                'completed' => $lead_upload_completed,
                'duplicate' => $duplicate,
            ]);

            if ($lead_upload_completed != $lead_upload_attempted){
                $this->session->set_flashdata('error_message', get_phrase('Some of the leads are already in the list!'));
            }else{
                $this->session->set_flashdata('flash_message', get_phrase('Uploaded successfully!'));
            }


            $this->data['list_items']       = $excel_leads;
            $this->data['item_count']       = $lead_upload_attempted;
            $this->data['duplicate_count']  = $sheet_duplication;
            $this->data['existing_count']   = $global_duplication;
            $this->data['success_count']    = $lead_upload_completed;
            $this->data['page_title'] 	    = 'leads_bulk_upload_status';
            $this->data['page_name'] 	    = 'lead/leads_bulk_upload_status';
            $this->load->view('admin/index', $this->data);
		}
		
        $this->data['counselors']   = $this->counselor_m->get()->result_array();
        $this->data['lead_status']  = $this->lead_status_m->get()->result_array();
        $this->data['page_title']   = 'Lead Bulk Upload';
        $this->data['page_name']    = 'lead/lead_bulk_upload';
        $this->load->view('admin/index', $this->data);
    }
    
    
    public function search_for_duplicate_entry($phone, $array=null) {
        if($array!=null){
            foreach ($array as $key => $val) {
                if ($val['phone'] === $phone) {
                    return 1;
                }
            }
        }else{
            return 0;
        }
    }
    
}