<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Lead_status extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('lead_source_m');
        $this->load->model('lead_status_m');
    }

    /*
     * Index Page
     */
    public function index(){
        $this->data['list_items']   = $this->lead_status_m->get()->result_array();
        $this->data['page_title']   = 'Lead Status';
        $this->data['page_name']    = 'lead_status/index';
        $this->load->view('admin/index', $this->data);
    }

     /*
     * Add lead
     */
    public function add(){
        if ($this->input->post()){
             
            $data['status'] = $this->input->post('status');
           
            $leadstatus = $this->lead_status_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/lead_status/index/');
        }

        $this->data['page_title']   = 'Add Lead Status';
        $this->data['page_name']    = 'lead_status/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
     /*
     * Edit lead
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('admin/lead_status/index/');
        }else{
            if ($this->input->post()){
                $data['status'] = $this->input->post('status');
                
                $this->lead_status_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/lead_status/index/');
            }
        }
        $this->data['edit_data']    = $this->lead_status_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'Lead Status Edit';
        $this->data['page_name']    = 'lead_status/edit';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Delete lead
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->lead_status_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/lead_status/index/');
    }
    
    
    
    
    
    
}