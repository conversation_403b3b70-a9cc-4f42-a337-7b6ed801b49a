<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Expense extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('accounts_m');
        $this->load->model('payment_info_m');
        $this->load->model('expense_category_m');
        $this->load->model('expense_m');
        $this->load->model('user_m');
    }

    /*
     * Index Page
     */
    public function index(){
        //filter expense
        $this->data['start_date'] = $this->input->get('start_date');
        $this->data['end_date'] = $this->input->get('end_date');
        $this->data['category_id'] = $this->input->get('category_id');
        $this->data['account_id'] = $this->input->get('account_id');

        if (empty($this->data['start_date'])){ $this->data['start_date'] = date('Y-m-01');}
        if (empty($this->data['end_date'])){$this->data['end_date'] = date('Y-m-t');}

        $where = [];

        $where['expense_date >='] = $this->data['start_date'];
        $where['expense_date <='] = $this->data['end_date'];

        if ( $this->data['category_id'] > 0){ $where['category_id'] =  $this->data['category_id'];}
        if ($this->data['account_id'] > 0){$where['account_id'] = $this->data['account_id'];}

        $this->data['list_items'] = $this->expense_m->get($where)->result_array();
        $this->data['accounts_list'] = $this->accounts_m->get_accounts_with_balance();
        $this->data['page_title']   = 'Expense';
        $this->data['page_name']    = 'expense/index';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Add Task
     */
    public function add(){
        if ($this->input->post()) {
            $data['title'] = $this->input->post('title');
            $data['amount'] = $this->input->post('amount');
            $data['category_id'] = $this->input->post('category_id');
            $data['user_id'] = $this->input->post('user_id');
            $data['account_id'] = $this->input->post('account_id');
            $data['expense_date'] = $this->input->post('expense_date');
            $data['reference_no'] = $this->input->post('reference_no');
            $data['remarks'] = $this->input->post('remarks');

            $data['created_by'] = get_user_id();
            $data['updated_by'] = get_user_id();

            $data['created_on'] = date('Y-m-d H:i:s');
            $data['updated_on'] = date('Y-m-d H:i:s');

            $this->expense_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
        }
        redirect($this->input->server('HTTP_REFERER'));
    }

    /*
     * Edit Task
     */
    public function edit($id = 0){
        if ($this->input->post() && $id){
            $data['title'] = $this->input->post('title');
            $data['amount'] = $this->input->post('amount');
            $data['category_id'] = $this->input->post('category_id');
            $data['user_id'] = $this->input->post('user_id');
            $data['account_id'] = $this->input->post('account_id');
            $data['expense_date'] = $this->input->post('expense_date');
            $data['reference_no'] = $this->input->post('reference_no');
            $data['remarks'] = $this->input->post('remarks');

            $data['updated_by'] = get_user_id();
            $data['updated_on']     = date('Y-m-d H:i:s');

            $this->expense_m->update($data, ['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
        }
        redirect($this->input->server('HTTP_REFERER'));
    }

    /*
     * Delete Lesson
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->expense_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect($this->input->server('HTTP_REFERER'));
    }
}