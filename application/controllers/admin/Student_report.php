<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Student_report extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    
    public function __construct() {
        parent::__construct();
        $this->load->model('student_report_m');
        $this->load->model('course_m');
        $this->load->model('user_m');
    }

    /**
     * Main index page - Course selection and student listing
     */
    public function index() {
        $course_id = $this->input->get('course_id');
        
        // Get all courses for dropdown
        $this->data['courses'] = $this->student_report_m->get_courses();
        $this->data['selected_course_id'] = $course_id;
        $this->data['students'] = [];
        
        // If course is selected, get students
        if ($course_id) {
            $this->data['students'] = $this->student_report_m->get_course_students($course_id);
            
            // Get course title for display
            $course = $this->course_m->get(['id' => $course_id])->row_array();
            $this->data['selected_course_title'] = $course ? $course['title'] : '';
        }
        
        $this->data['page_title'] = 'Student Activity Report';
        $this->data['page_name'] = 'student_report/index';
        $this->load->view('admin/index', $this->data);
    }

    /**
     * Detailed student report for a specific course
     */
    public function detailed_report($user_id = 0, $course_id = 0) {
        if ($user_id == 0 || $course_id == 0) {
            $this->session->set_flashdata('error_message', 'Invalid student or course ID');
            redirect('admin/student_report');
        }

        // Get detailed report data
        $this->data['report_data'] = $this->student_report_m->get_student_detailed_report($user_id, $course_id);
        
        if (empty($this->data['report_data']['student'])) {
            $this->session->set_flashdata('error_message', 'Student not found');
            redirect('admin/student_report');
        }

        $this->data['user_id'] = $user_id;
        $this->data['course_id'] = $course_id;
        $this->data['page_title'] = 'Detailed Student Report - ' . $this->data['report_data']['student']['name'];
        $this->data['page_name'] = 'student_report/detailed_report';
        $this->load->view('admin/index', $this->data);
    }

    /**
     * View student activity details
     */
    public function view_activity($activity_id = 0) {
        if ($activity_id == 0) {
            show_404();
        }

        // Get activity details
        $this->db->select('ua.*, u.name as student_name, lf.title as lesson_file_title, l.title as lesson_title');
        $this->db->from('user_activity ua');
        $this->db->join('users u', 'ua.user_id = u.id', 'left');
        $this->db->join('lesson_files lf', 'ua.activity_id = lf.id', 'left');
        $this->db->join('lesson l', 'ua.lesson_id = l.id', 'left');
        $this->db->where('ua.id', $activity_id);
        $activity = $this->db->get()->row_array();

        if (!$activity) {
            show_404();
        }

        $this->data['activity'] = $activity;
        $this->data['page_title'] = 'Student Activity Details';
        $this->data['page_name'] = 'student_report/view_activity';
        $this->load->view('admin/index', $this->data);
    }

    /**
     * View student task upload details
     */
    public function view_task_upload($upload_id = 0) {
        if ($upload_id == 0) {
            show_404();
        }

        // Get task upload details
        $this->db->select('ut.*, u.name as student_name, t.title as task_title, l.title as lesson_title');
        $this->db->from('user_task_uploads ut');
        $this->db->join('users u', 'ut.user_id = u.id', 'left');
        $this->db->join('tasks t', 'ut.task_id = t.id', 'left');
        $this->db->join('lesson l', 't.lesson_id = l.id', 'left');
        $this->db->where('ut.id', $upload_id);
        $task_upload = $this->db->get()->row_array();

        if (!$task_upload) {
            show_404();
        }

        // Parse uploaded files
        $uploaded_files = [];
        if (!empty($task_upload['file'])) {
            $files = json_decode($task_upload['file'], true);
            if (is_array($files)) {
                foreach ($files as $file) {
                    $file_path = 'uploads/user_task_uploads/' . $file;
                    if (is_file($file_path)) {
                        $ext = pathinfo($file, PATHINFO_EXTENSION);
                        $uploaded_files[] = [
                            'file' => base_url($file_path),
                            'filename' => $file,
                            'type' => in_array(strtolower($ext), ['jpg', 'jpeg', 'png', 'gif']) ? 'image' : 'file'
                        ];
                    }
                }
            }
        }

        $this->data['task_upload'] = $task_upload;
        $this->data['uploaded_files'] = $uploaded_files;
        $this->data['page_title'] = 'Student Task Upload Details';
        $this->data['page_name'] = 'student_report/view_task_upload';
        $this->load->view('admin/index', $this->data);
    }

    /**
     * Export student report to Excel
     */
    public function export_excel($course_id = 0) {
        if ($course_id == 0) {
            $this->session->set_flashdata('error_message', 'Please select a course first');
            redirect('admin/student_report');
        }

        $students = $this->student_report_m->get_course_students($course_id);
        $course = $this->course_m->get(['id' => $course_id])->row_array();

        // Set headers for Excel download
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="student_report_' . date('Y-m-d') . '.xls"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr>';
        echo '<th>Student Name</th>';
        echo '<th>Email</th>';
        echo '<th>Phone</th>';
        echo '<th>Course</th>';
        echo '<th>Premium Status</th>';
        echo '<th>Completion %</th>';
        echo '<th>Enrollment Date</th>';
        echo '</tr>';

        foreach ($students as $student) {
            echo '<tr>';
            echo '<td>' . htmlspecialchars($student['name']) . '</td>';
            echo '<td>' . htmlspecialchars($student['email']) . '</td>';
            echo '<td>' . htmlspecialchars($student['phone']) . '</td>';
            echo '<td>' . htmlspecialchars($student['course_title']) . '</td>';
            echo '<td>' . ($student['premium'] ? 'Premium' : 'Free') . '</td>';
            echo '<td>' . $student['completion_percentage'] . '%</td>';
            echo '<td>' . date('d-M-Y', strtotime($student['enrollment_date'])) . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
    }

    /**
     * AJAX method to get courses by category (if needed for future enhancement)
     */
    public function ajax_get_courses_by_category() {
        $category_id = $this->input->get('category_id');
        
        if ($category_id) {
            $this->db->where('category_id', $category_id);
        }
        
        $courses = $this->student_report_m->get_courses();
        
        echo json_encode($courses);
    }
}
