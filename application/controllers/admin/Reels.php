<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class <PERSON>els extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('reels_m');
    }
    
    
    /*
    * Index Page
    */
    public function index(){
        $this->data['list_items']   = $this->reels_m->get()->result_array();
        $this->data['page_title']   = 'Reels';
        $this->data['page_name']    = 'reels/index';
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*
     * Add Feed
     */
    public function add(){
        if ($this->input->post()){
            $data['title'] = $this->input->post('title');
            $data['content'] = html_escape($this->input->post('content'));
            $data['course_id']  = $this->input->post('course_id');
            $data['vimeo_url']  = $this->input->post('video_url');
            $mp4_url = $this->get_mp4_link($this->input->post('video_url'));
            $data['video_url'] = $mp4_url ? $mp4_url : $this->input->post('video_url');

            $html5_url = get_vimeo_file_url($this->input->post('video_url'));
            if($html5_url['status'] == 'true'){
                $data['html5_video_url']  = $html5_url['video_link'];
            }
            
            $file_upload = $this->upload_file('image', 'image');
            if($file_upload!= false){
                $data['image'] = $file_upload['file'];
            }else{
                $data['image'] = '';
            }
            
            $data['created_on'] = date('Y-m-d H:i:s');
            
            $reel = $this->reels_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/reels/index/');
        }

        $this->data['courses']   = $this->course_m->get()->result_array();
        $this->data['page_title']   = 'Add Reels';
        $this->data['page_name']    = 'reels/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
     /*
     * Edit Feed
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/feed/index/');
        }else{
            if ($this->input->post()){
                $data['title'] = $this->input->post('title');
                $data['content'] = html_escape($this->input->post('content'));
                $data['course_id']  = $this->input->post('course_id');
                $data['vimeo_url']  = $this->input->post('video_url');
                $mp4_url = $this->get_mp4_link($this->input->post('video_url'));
                $data['video_url'] = $mp4_url ? $mp4_url : $this->input->post('video_url');
                
                $html5_url = get_vimeo_file_url($this->input->post('video_url'));
                if($html5_url['status'] == 'true'){
                    $data['html5_video_url']  = $html5_url['video_link'];
                }
                
                $file_upload = $this->upload_file('image', 'image');
                if($file_upload!= false){
                    $data['image'] = $file_upload['file'];
                }
                
                $data['updated_on']     = date('Y-m-d H:i:s'); 
                
                $this->reels_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/reels/index/');
            }
        }
        $this->data['courses']      = $this->course_m->get()->result_array();
        $this->data['edit_data']    = $this->reels_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'Reel Edit';
        $this->data['page_name']    = 'reels/edit';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Delete Feed
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->reels_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/reels/index/');
    }
    
    
    public function get_mp4_link($video_url) {
        $vimeo_video_file = get_vimeo_video_file_url($video_url);
        $files = $vimeo_video_file['files'] ?? [];
    
        $mp4_video_url = null; // Initialize the variable to hold the MP4 URL
        
        // Iterate through the $files array
        foreach ($files as $file) {
            // Check if the file is of type mp4 and width is below 540
            if ($file['type'] === 'video/mp4' && $file['width'] < 540) {
                $mp4_video_url = $file['link']; // Set the link
                break; // Stop after finding the first MP4 below 540 width
            }
        }
    
        return $mp4_video_url; // Return the found URL or null if not found
    }
    
}    