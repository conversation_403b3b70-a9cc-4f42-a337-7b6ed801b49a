<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Test extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('test_m');
    }
    
    
    /*
    * Index Page
    */
    public function index(){
        $this->data['list_items']   = $this->test_m->get()->result_array();
        $this->data['courses']      = $this->course_m->get()->result_array();
        $this->data['page_title']   = 'Test';
        $this->data['page_name']    = 'test/index';
        $this->load->view('admin/index', $this->data);
    }
    
     /*
     * Add test
     */
    public function add(){
        if ($this->input->post()){
             
            $data['title']          = $this->input->post('title');
            $data['course_id']      = $this->input->post('course_id');
            $data['summary']        = $this->input->post('summary');
            $data['duration']       = $this->input->post('duration');
            $data['from_date']      = $this->input->post('from_date');
            $data['from_time']      = $this->input->post('from_time');
            $data['to_date']        = $this->input->post('to_date');
            $data['to_time']        = $this->input->post('to_time');
            $data['free']           = $this->input->post('free') == 1 ? 'on' : 'off';
            $data['status']         = '';
            $data['created_on']     = date('Y-m-d H:i:s');
            $file_upload = $this->upload_file('test', 'image');
            if($file_upload!= false){
                $data['image'] = $file_upload['file'];
            }else{
                $data['image'] = '';
            }
            
            
            $test_id = $this->test_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/test/index/');
        }

        $this->data['courses']      = $this->course_m->get()->result_array();
        $this->data['page_title']   = 'Add test';
        $this->data['page_name']    = 'test/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*
     * Edit test
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/test/index/');
        }else{
            if ($this->input->post()){
            $data['title']          = $this->input->post('title');
            $data['course_id']      = $this->input->post('course_id');
                $data['summary']        = $this->input->post('summary');
                $data['duration']       = $this->input->post('duration');
                $data['from_date']      = $this->input->post('from_date');
                $data['from_time']      = $this->input->post('from_time');
                $data['to_date']        = $this->input->post('to_date');
                $data['to_time']        = $this->input->post('to_time');
                $data['free']           = $this->input->post('free') == 1 ? 'on' : 'off';
                $data['status']         = '';
                $file_upload = $this->upload_file('test', 'image');
                if($file_upload!= false){
                    $data['image'] = $file_upload['file'];
                }
                
                $data['updated_on']     = date('Y-m-d H:i:s'); 
                
                $this->test_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/test/index/');
            }
        }
        $this->data['courses']      = $this->course_m->get()->result_array();
        $this->data['edit_data']    = $this->test_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'test Edit';
        $this->data['page_name']    = 'test/edit';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Delete test
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->test_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/test/index/');
    }
}    