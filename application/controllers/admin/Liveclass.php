<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Liveclass extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('Package_m');
        $this->load->model('liveclass_m');
        $this->load->model('enrol_m');
        
    }
    
    
    /*
     * Index Page
     */
    public function index(){
        if($this->input->get('course_id') >0) {
            $course_id = $this->input->get('course_id');
        }else{
            $course_id = 0;
        }
        
        if($this->input->get('category_id') >0 ){
            $category_id = $this->input->get('category_id');
        }else{
            $category_id = 0;
        }
        
        if($this->input->get('lesson_id') >0 ){
            $lesson_id = $this->input->get('lesson_id');
        }else{
            $lesson_id = 0;
        }
         
        $this->data['list_items']   = $this->liveclass_m->get_liveclass($course_id,$category_id,$lesson_id)->result_array();
        log_message('error', $this->db->last_query());
        // log_message('error',print_r($this->db->last_query(),true));
        $this->data['categories']   = $this->category_m->get_parent_categories();
        // log_message('error',print_r($this->data['list_items'],true));
        $this->data['page_title']   = 'Liveclass';
        $this->data['page_name']    = 'liveclass/index';
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*
     * Add liveclass
     */
    public function add(){
        if ($this->input->post()){
             
            $data['title']          = $this->input->post('title');
            $data['category_id']    = $this->input->post('category_id');
            $data['package_id']     = $this->input->post('package_id') ?? 0;
            $data['course_id']      = $this->input->post('course_id');
            $data['lesson_id']      = $this->input->post('lesson_id') ?? 0;
            $data['live_type']      = $this->input->post('live_type');
            $data['student_id']     = $this->input->post('student_id');
            $data['zoom_id']        = $this->input->post('zoom_id');
            $data['password']       = $this->input->post('password');
            $data['fromTime']       = $this->input->post('fromTime');
            $data['toTime']         = $this->input->post('toTime');
            $data['fromDate']       = $this->input->post('fromDate');
            $data['toDate']         = $this->input->post('toDate');
            $data['role_id']        = $this->input->post('role_id');
            $data['created_by']     = $this->input->post('created_by');
            $data['created_on']     = date('Y-m-d H:i:s');
            $data['status']         = "";
            
            $liveclass_id = $this->liveclass_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/liveclass/index/');
        }

        $this->data['categories']   = $this->category_m->get_parent_categories();
        $this->data['page_title']   = 'Add liveclass';
        $this->data['page_name']    = 'liveclass/add';
        $this->load->view('admin/index', $this->data);
    }
    
    /*Edit Liveclass*/
    
    public function edit($id = 0){
        if ($id == 0){
            redirect('admin/liveclass/index/');
        }else{
            if ($this->input->post()){
                $data['title']          = $this->input->post('title');
                $data['category_id']    = $this->input->post('category_id');
                $data['package_id']     = $this->input->post('package_id') ?? 0;
                $data['course_id']      = $this->input->post('course_id');
                $data['lesson_id']      = $this->input->post('lesson_id') ?? 0;
                $data['live_type']      = $this->input->post('live_type');
                $data['student_id']     = $this->input->post('student_id');
                $data['zoom_id']        = $this->input->post('zoom_id');
                $data['password']       = $this->input->post('password');
                $data['role_id']        = $this->input->post('role_id');
                $data['fromTime']       = $this->input->post('fromTime');
                $data['toTime']         = $this->input->post('toTime');
                $data['fromDate']       = $this->input->post('fromDate');
                $data['toDate']         = $this->input->post('toDate');
                $this->liveclass_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/liveclass/index/');
            }
        }
        $this->data['edit_data']    = $this->liveclass_m->get(['id' => $id])->row_array();
        $this->data['categories']   = $this->category_m->get_parent_categories();
        $this->data['page_title']   = 'liveclass Edit';
        $this->data['page_name']    = 'liveclass/edit';
        $this->load->view('admin/index', $this->data);
    }
    
    
    
    
    /*
     * Delete liveclass
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->liveclass_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/liveclass/index/');
    }

    
    
}