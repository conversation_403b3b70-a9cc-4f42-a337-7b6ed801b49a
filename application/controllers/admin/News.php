<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class News extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('news_m');
    }
    
    
    /*
    * Index Page
    */
    public function index(){
        $this->data['news']   = $this->news_m->get()->result_array();
        $this->data['page_title']   = 'News';
        $this->data['page_name']    = 'news/index';
        $this->load->view('admin/index', $this->data);
    }
    
     /*
     * Add News
     */
    public function add(){
        if ($this->input->post()){
            $data["title"]          = $this->input->post('title');
            $data["description"]    = $this->input->post('description');
            $file_upload = $this->upload_file('news', 'image');
            if($file_upload!= false){
                $data['image'] = $file_upload['file'];
            }else{
                $data['image'] = '';
            }  
            $data['created_on']     = date('Y-m-d H:i:s');
            $this->news_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/news/index/');
        }

        $this->data['page_title']   = 'Add News';
        $this->data['page_name']    = 'news/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
    
    /*
     * Delete News
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->news_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/news/index/');
    }
    
    
}    

