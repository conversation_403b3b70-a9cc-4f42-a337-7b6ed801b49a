<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Instructor_salary extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('instructor_salary_m');
        $this->load->model('expense_m');
        $this->load->model('user_m');
        $this->load->model('instructor_m');
        $this->load->model('instructor_students_m');
    }

    /*
     * Index Page
     */
    public function index(){
        $this->data['list_items']   = $this->instructors_list();
        $this->data['page_title']   = 'Instructor Salary';
        $this->data['page_name']    = 'instructor_salary/index';
        $this->load->view('admin/index', $this->data);
    }

    private function instructors_list(){
        $instructors = $this->instructor_m->get(null, ['id', 'name', 'phone'])->result_array();
        foreach ($instructors as $key => $instructor){
            $instructors[$key]['amount_total'] = 0;
            $instructors[$key]['amount_paid'] = 0;
            $instructors[$key]['amount_balance'] = 0;
        }
        return $instructors;
    }

    // view instructor
    public function view($instructor_id){
        $this->data['instructor'] = $this->instructor_m->get(['id' => $instructor_id])->row();
        $this->data['students'] = $this->instructor_students($instructor_id);
        $this->data['payment_overview'] = $this->instructor_payment_overview($instructor_id);
        $this->data['page_title']   = 'Instructor Salary';
        $this->data['page_name']    = 'instructor_salary/view';
        $this->load->view('admin/index', $this->data);
    }

    private function instructor_students($instructor_id) {
        $students = $this->instructor_students_m->get_instructor_students($instructor_id);
        foreach ($students as $key => $student) {
            $students[$key]['lessons_total'] = 0;
            $students[$key]['lessons_completed'] = 0;
            $students[$key]['lessons_pending'] = 0;
        }
        return $students;
    }

    private function instructor_payment_overview($instructor_id){
        $total_paid = $this->expense_m->get(['user_id' => $instructor_id, 'category_id' => 1], ['sum(amount) as total_amount'])->row()->total_amount;
        return [
            'amount_paid' => $total_paid
        ];
    }
}