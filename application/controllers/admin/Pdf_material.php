<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Pdf_material extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('pdf_material_m');
    }
    
    
    /*
    * Index Page
    */
    public function index(){
        $this->data['list_items']   = $this->pdf_material_m->get()->result_array();
        // log_message('error',print_r($this->data['list_items'],true));
        $this->data['page_title']   = 'Pdf material';
        $this->data['page_name']    = 'pdf_material/index';
        $this->load->view('admin/index', $this->data);
    }
    
     /*
     * Add Banner
     */
    public function add(){
        if ($this->input->post()){
             
            $data['title'] = $this->input->post('title');
            $file_upload = $this->upload_file('pdf_material', 'file');
            if($file_upload!= false){
                $data['url'] = $file_upload['file'];
            }else{
                $data['url'] = '';
            }
            $data['category_id'] = $this->input->post('category_id');
            $pdf = $this->pdf_material_m->insert($data);
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/pdf_material/index/');
        }

        
        $this->data['page_title']   = 'Add Pdf material';
        $this->data['page_name']    = 'pdf_material/add';
        $this->load->view('admin/index', $this->data);
    }
    
    
    /*
     * Edit Banner
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/pdf_material/index/');
        }else{
            if ($this->input->post()){
                $data['title'] = $this->input->post('title');
                $file_upload = $this->upload_file('pdf_material', 'file');
                if($file_upload!= false){
                    $data['url'] = $file_upload['file'];
                }
                $data['category_id'] = $this->input->post('category_id');
                $this->pdf_material_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/pdf_material/index/');
            }
        }
        $this->data['edit_data']    = $this->pdf_material_m->get(['id' => $id])->row_array();
        $this->data['page_title']   = 'pdf material Edit';
        $this->data['page_name']    = 'pdf_material/edit';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Delete Banner
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->pdf_material_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/pdf_material/index/');
    }
}    