<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Payments extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('enrol_m');
        $this->load->model('course_m');
        $this->load->model('category_m');
        $this->load->model('section_m');
        $this->load->model('user_m');
        $this->load->model('student_m');
        $this->load->model('package_m');
        $this->load->model('payment_info_m');
        $this->load->model('accounts_m');
        $this->load->model('auto_notification_m');
    }

    /*
     * Index Page
     */
    public function index(){
        //filter payments
        $this->data['start_date'] = $this->input->get('start_date');
        $this->data['end_date'] = $this->input->get('end_date');
        $this->data['course_id'] = $this->input->get('course_id');
        $this->data['expiry_date'] = $this->input->get('expiry_date');
        $this->data['expired_date'] = $this->input->get('expired_date');
        
        if(empty($this->input->get('course_id') && empty($this->input->get('package_id')))){
            if(empty($this->data['start_date'])){ $this->data['start_date'] = date('Y-01-01');}
            if(empty($this->data['end_date'])){$this->data['end_date'] = date('Y-m-t');}
        }  
        
        $where = [];
        if(!empty($this->data['start_date'])){
            $where['date(payment_date) >='] = $this->data['start_date'];
        }
        
        if(!empty($this->data['end_date'])){
            $where['date(payment_date) <='] = $this->data['end_date'];
        }

        if($this->data['course_id'] > 0){$where['package.course_id'] =  $this->data['course_id'];}
        if($this->data['expiry_date']!=""){$where['payment_info.expiry_date'] =  $this->data['expiry_date'];}
        if($this->data['expired_date']!=""){$where['payment_info.expiry_date <'] =  $this->data['expired_date'];}

        $this->data['list_items']   = $this->payment_info_m->get_payment_details($where)->result_array();
        
        
        $this->data['course_list']    = $this->course_m->get_active_courses()->result_array();
        $this->data['total_payments'] = $this->payment_info_m->get()->num_rows();
        
        $this->db->select_sum('amount_paid');
        $query = $this->payment_info_m->get();
        $this->data['total_amount']   =  $query->row()->amount_paid ?? 0;
        
        $this->data['today_payments'] = $this->payment_info_m->get(['date(payment_date)' => date('Y-m-d')])->num_rows();
        
        $this->db->select_sum('amount_paid');
        $this->db->where('date(payment_date)', date('Y-m-d'));
        $query2 = $this->payment_info_m->get();
         
        $this->db->select('
            SUM(CASE WHEN razorpay_payment_id LIKE "man_%" THEN amount_paid ELSE 0 END) AS total_manual_payment,
            SUM(CASE WHEN razorpay_payment_id LIKE "pay_%" THEN amount_paid ELSE 0 END) AS total_online_payment
        ');
        $query = $this->db->get('payment_info');
        $result = $query->row();
        $total_manual_payment = $result->total_manual_payment;
        $total_online_payment = $result->total_online_payment;
        $this->data['manual_payments']   =  $total_manual_payment ?? 0; 
        $this->data['razorpay_payments'] =  $total_online_payment ?? 0;
        
        $this->data['today_amount']   = $query2->row()->amount_paid ?? 0;
        $this->data['page_title']     = 'Payments';
        $this->data['page_name']      = 'payments/index';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Add Package
     */
    public function add(){
        if($this->input->post()){
            $student = $this->student_m->get(['id'=>$this->input->post('user_id')])->row();
            $account_id = $this->accounts_m->get_primary_account_id();

            // get package details
            $package = $this->package_m->get(['id' => $this->input->post('package_id')])->row();
            $package_duration = $package->duration ?? 10;
            $expiry_date = date('Y-m-d', strtotime(date('Y-m-d'). " + {$package_duration} days"));

            // $data['account_id'] = $this->input->post('account_id');
            $data['user_id'] = $this->input->post('user_id');
            $data['package_id'] = $this->input->post('package_id');
            $data['account_id'] = $account_id;
            $data['amount_paid'] = $this->input->post('amount_paid');
            $data['coupon_id'] = $this->input->post('coupon_id');
            $data['discount'] = $this->input->post('discount');
            $data['razorpay_payment_id'] = $this->input->post('razorpay_payment_id');
            $data['user_phone'] =  $student->phone;
            $data['user_email'] =  $student->user_email;
            $data['payment_date'] = date('Y-m-d H:i:s');
            $data['package_duration'] = $package_duration;
            $data['expiry_date'] = $expiry_date;
            $data['created_on'] = date('Y-m-d H:i:s');
            $data['updated_on'] = date('Y-m-d H:i:s');
            $data['created_by'] = get_user_id();
            $data['updated_by'] = get_user_id();
            $data['note'] = $this->input->post('note');
            
            if($this->input->post('coupon_id')>0){
    		    $coupon = $this->db->get_where('coupon_code', ['id' => $data['coupon_id']])->row();
                $data['code'] = $coupon->code."[".$coupon->discount_perc."%]";
    		}

            $this->payment_info_m->insert($data);

            //enrol to the package if not already
            $enrol = $this->enrol_m->get([
                'user_id' => $this->input->post('user_id'),
                'course_id' => $package->course_id
            ]);

            if ($enrol->num_rows() > 0) {
                //update
                $this->enrol_m->update([
                    'user_id' => $this->input->post('user_id'),
                    'course_id' => $package->course_id,
                    'package_id' => $package->id,
                    'premium' => 0,
                    'updated_on' => date('Y-m-d H:i:s'),
                ],['id' => $enrol->row()->id]);
            }else{
                // insert
                $this->enrol_m->insert([
                    'user_id' => $this->input->post('user_id'),
                    'course_id' => $package->course_id,
                    'package_id' => $package->id,
                    'premium' => 0,
                    'created_on' => date('Y-m-d H:i:s'),
                    'updated_on' => date('Y-m-d H:i:s'),
                ]);
            }
            
            $course_category_id = $this->db->get_where('course', ['id' => $package->course_id])->row()->category_id;
            
            $auto_notification = $this->auto_notification_m->is_notification_exist_in_purchase($course_category_id, $package->course_id, 1);
            // if(count($auto_notification) >0){
            if (!empty($auto_notification) && is_array($auto_notification)) {
                $user = $this->db->get_where('users', ['id' => $student->id])->result_array(); 
                $token = array_column($user, 'notification_token');
                
                $token = array_filter($token, function ($value) {
                    return !is_null($value) && $value !== '';
                });
                $token = array_chunk($token, 800);

                foreach ($token as $tk){
                    sendNotification($auto_notification['title'],$auto_notification['description'], $tk, $auto_notification['external_link']);
                }
            }
            
            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect($this->input->server('HTTP_REFERER'));
        }
        $this->data['students']     = $this->student_m->get(null, ['id', 'name', 'phone'])->result_array();
        $this->data['course_list']  = $this->course_m->get_active_courses()->result_array();
        // $this->data['packages']     = $this->package_m->get()->result_array();
        $this->data['categories']   = $this->category_m->get_parent_categories();
        $this->data['page_title']   = 'Add Payment';
        $this->data['page_name']    = 'payments/add';
        $this->load->view('admin/index', $this->data);
    }

    
    /*
     * Delete Package
     */
    public function delete($id = 0){
        if ($id > 0){
            $this->payment_info_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/payments/index/');
    }
    
    
    
    // print payment
    public function print_payment($payment_id)
	{
		if($payment_id) {
			$order_data = $this->payment_info_m->get_payment_details(['payment_info.id' => $payment_id])->row_array();
			$order_date = date('d/m/Y h:i A', strtotime($order_data['payment_date']));

			$html = '<!-- Main content -->
			<!DOCTYPE html>
			<html>
                <head>
                    <meta charset="utf-8">
                    <meta http-equiv="X-UA-Compatible" content="IE=edge">
                    <title>Invoice</title>
                    <!-- Tell the browser to be responsive to screen width -->
                    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
                    <!-- Bootstrap 3.3.7 -->
                    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
                    <!-- Font Awesome -->
                    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
                    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.0.5/css/adminlte.min.css">
                </head>
                <body onload="window.print();">
                    <div class="wrapper">
                    <section class="invoice container" style="margin-top:100px;">
                        <!-- title row -->
                        <div class="row">
                        <div class="col-md-12 col-xs-12 col-sm-12 col-xl-12">
                        <h2 class="page-header">
                        SKILLAGE 
                        <span class="pull-right" style="font-size:20px;">Date: '.$order_date.'</span>
                        </h2>
                        </div>
                        <!-- /.col -->
                        </div>
                        <!-- info row -->
                        <div class="row invoice-info">
                        
                        <div class="col-sm-4 invoice-col">
                        
                        <b>Invoice No:</b> '.$order_data['razorpay_payment_id'].'<br>
                        <b>Name:</b> '.strtoupper($order_data['user_name']).'<br>
                        <b>Phone:</b> '.$order_data['phone'].'
                        </div>
                        <!-- /.col -->
                        </div>
                        <!-- /.row -->
                        
                        <!-- Table row -->
                        <div class="row">
                            <div class="col-xs-12 table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Package name</th>
                                            <th>Price</th>
                                            <th>Discount</th>
                                            <th>Amount</th>
                                        </tr>
                                    </thead>
                                    <tbody>'; 
                                    
                                    
                                    
                                    $html .= '<tr>
                                        <td>'.$order_data['package_name'].' - '.$order_data['course_name'].'</td>
                                        <td>'.($order_data['amount_paid']+$order_data['discount']).'</td>
                                        <td>'.$order_data['discount'].'</td>
                                        <td>'.$order_data['amount_paid'].'</td>
                                    </tr>';
                                    
                                    
                                    $html .= '</tbody>
                                    <tfoot>
                                        <tr>
                                            <th></th>
                                            <td></td>
                                            <th>Total Amount:</th>
                                            <th><i class="fa fa-rupee"></i> '.$order_data['amount_paid'].'</th>
                                        </tr>
                                        <tr>
                                            <th></th>
                                            <td style="padding-top:20px">(office seal)</td>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        <!-- /.col -->
                        </div>
                        <!-- /.row -->

                    </section>
                    <!-- /.content -->
                    </div>
                </body>
                </html>';

			  echo $html;
		}
	}

    public function extend_package($payment_id = 0){
        if ($payment_id > 0){
            $payment['expiry_date'] = $this->input->post('expiry_date');
            $payment['is_extended'] = 1;
            $this->payment_info_m->update($payment, ['id' => $payment_id]);
            $this->session->set_flashdata('flash_message', get_phrase('date_updated_successfully!'));
        }
        redirect($this->input->server('HTTP_REFERER'));
    }
    
}