<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Instructor_students extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('user_m');
        $this->load->model('instructor_m');
        $this->load->model('instructor_enrol_m');
        $this->load->model('instructor_students_m');
        $this->load->model('course_m');
    }

    /*
     * Students List
     */
    public function index($id = 0){
        $this->data['list_items']   = $this->instructor_students_m->get_instructor_students($id);
        $this->data['user']         = $this->instructor_m->get(['id' => $id])->row();
        $this->data['page_title']   = 'Instructor Students';
        $this->data['page_name']    = 'instructor_students/index';
        $this->load->view('admin/index', $this->data);
    }

    /*
     * Assign Students
     */
    public function add($id = 0){
        if ($this->input->post()){
            $instructor_id = $this->input->post('instructor_id');
            $course_id = $this->input->post('course_id');
            $student_id = $this->input->post('student_id');
            $instructor_assign = $this->instructor_students_m->assign_student($instructor_id, $course_id, $student_id);
            if ($instructor_assign){
                $this->session->set_flashdata('flash_message', get_phrase('assigned_successfully'));
            }else{
                $this->session->set_flashdata('error_message', get_phrase('already_assigned'));
            }
            redirect('admin/instructor_students/index/'.$instructor_id);
        }

        $this->data['course_list']  = $this->instructor_enrol_m->get_instructor_enrolled_courses($id);
        $this->data['instructor_id'] = $id;
        $this->data['page_title']   = 'Assign Students';
        $this->data['page_name']    = 'instructor_students/add';
        $this->load->view('admin/index', $this->data);
    }


    /*
     * Remove Student
     */
    public function delete(){
        $course_id = $this->input->get('course_id');
        $instructor_id = $this->input->get('instructor_id');
        $student_id = $this->input->get('student_id');

        if ($course_id > 0 && $instructor_id > 0 && $student_id > 0){
            $this->instructor_students_m->delete(['course_id' => $course_id, 'instructor_id' => $instructor_id, 'student_id' => $student_id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        }
        redirect('admin/instructor_students/index/'.$instructor_id);
    }




}