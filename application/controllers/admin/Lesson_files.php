<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Lesson_files extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('lesson_files_m');
        $this->load->model('lesson_m');
        $this->load->model('enrol_m');
        $this->load->model('course_m');
        $this->load->model('vimeo_videolinks_m');
    }

    /*
     * Index Page
     */
    public function index($lesson_id = 0){
        if ($lesson_id == 0){
            redirect('admin/course/index/');
        }else{
            $lesson                     = $this->lesson_m->get(['id' => $lesson_id])->row();
            $this->data['list_items']   = $this->lesson_files_m->get_lesson_files_by_lesson_id($lesson_id)->result_array();
            $this->data['course_id']    = $lesson->course_id;
            $this->data['lesson_id']    = $lesson_id;
            $this->data['lesson_title'] = $lesson->title;
            $this->data['page_title']   = 'Lesson Files - '.$lesson->title;
            $this->data['page_name']    = 'lesson_files/index';
            $this->load->view('admin/index', $this->data);
        }
    }

    /*
     * Add Lesson Files
     */
    public function add(){
        if ($this->input->post()){
            $data['lesson_id']      = $this->input->post('lesson_id');
            $data['title']          = $this->input->post('title');

            $thumbnail = $this->upload_file('thumbnails/lesson_files', 'thumbnail', true);
            if($thumbnail){
                $data['thumbnail'] = $thumbnail['file'];
            }
            $lesson_type_array          = explode('-', $this->input->post('lesson_type'));
            $lesson_type                = $lesson_type_array[0];
            $data['attachment_type']    = $lesson_type_array[1];
            $data['lesson_type']        = $lesson_type;
            $lesson_provider = $this->input->post('lesson_provider');

            if ($lesson_type_array[1] == 'pdf') {
                $attachment = $this->upload_file('thumbnails/lesson_files', 'attachment', true);
                if($attachment){
                    $data['attachment'] = $attachment['file'];
                }
                
                $data['is_downloadable']  = $this->input->post('is_downloadable') == '1' ? '1' : '0';
            }else if ($lesson_type_array[1] == 'audio') {
                $audio_file = $this->upload_file('thumbnails/lesson_files', 'audio', true);
                if($audio_file){
                    $data['attachment'] = $audio_file['file'];
                    $data['audio_file'] = $audio_file['file'];
                }
            }else if ($lesson_type_array[1] == 'notes') {
                $data['attachment'] = $this->input->post('notes');
                $data['notes'] = $this->input->post('notes');
            }else if ($lesson_type_array[1] == 'exam' || $lesson_type_array[1] == 'practice') {
                $data['publish_result'] = $this->input->post('publish_result') == 1 ? 1 : 0;
                if ($lesson_type_array[1] == 'exam') {
                    $data['is_practice']    = $this->input->post('e_is_practice');
                    $duration_formatter = explode(':', $this->input->post('quiz_duration'));
                    $hour = sprintf('%02d', $duration_formatter[0]);
                    $min = sprintf('%02d', $duration_formatter[1]);
                    $sec = sprintf('%02d', $duration_formatter[2]);
                    $data['duration']       = $hour . ':' . $min . ':' . $sec;
                    
                    $data['from_date']      = $this->input->post('from_date');
                    $data['from_time']      = $this->input->post('from_time');
                    $data['to_date']        = $this->input->post('to_date');
                    $data['to_time']        = $this->input->post('to_time');
                }else if ($lesson_type_array[1] == 'practice') {
                    $data['is_practice']    = $this->input->post('p_is_practice');
                }
            }else if($lesson_type_array[1] == 'live') {
                $data['live_type']      = $this->input->post('live_type');
                $data['student_id']     = $this->input->post('student_id');
                $data['zoom_id']        = $this->input->post('zoom_id');
                $data['zoom_password']  = $this->input->post('password');
                $data['from_date']       = $this->input->post('fromDate');
                $data['from_time']       = $this->input->post('fromTime');
                $data['to_date']         = $this->input->post('toDate');
                $data['to_time']         = $this->input->post('toTime');
                $data['role_id']        = $this->input->post('role_id');
            } else if ($lesson_type_array[1] == 'activity') {
                $data['activity_type'] = $this->input->post('activity_type');
                $data['description'] = html_escape($this->input->post('activity_description'));
                $activity = $this->upload_file('thumbnails/lesson_files', 'activity_file', true);
                if($activity){
                    $data['activity_file'] = $activity['file'];
                }
            } else if ($lesson_type_array[1] == 'habit'){
                $data['habit_id'] = $this->input->post('habit_id');
            }

            if($lesson_type == 'video'){
                
                if ($lesson_provider == 'youtube' || $lesson_provider == 'vimeo') {
                    $data['attachment']    = $this->input->post('video_url');
                    $data['video_url']    = $this->input->post('video_url');
                    $duration_formatter   = explode(':', $this->input->post('duration'));
                    $hour                 = sprintf('%02d', $duration_formatter[0]);
                    $min                  = sprintf('%02d', $duration_formatter[1]);
                    $sec                  = sprintf('%02d', $duration_formatter[2]);
                    $data['duration']     = $hour . ':' . $min . ':' . $sec;
                    $data['video_type']   = $lesson_provider;
                    $data['download_url'] = $this->input->post('download_url');
                    
                    $html5_url = get_vimeo_file_url($this->input->post('video_url'));
                    if($html5_url['status'] == 'true'){
                        $data['html5_video_url']  = $html5_url['video_link'];
                    }
                }
                $data['is_portrait']  = $this->input->post('is_portrait') == '1' ? '1' : '0';
            }

            $data['free']  = $this->input->post('free') == 'on' ? 'on' : 'off';
            
            if(!empty($this->input->post('schedule_date'))){ 
                $data['schedule_date']  = $this->input->post('schedule_date'); 
            }
            
            $data['description']    = html_escape($this->input->post('description'));
            $data['created_on'] = date('Y-m-d H:i:s');
            $data['updated_on'] = date('Y-m-d H:i:s');

            $lesson_file_id = $this->lesson_files_m->insert($data);
            if($lesson_type == "video")
            {
                if($lesson_provider == 'vimeo')
                {
                    $vimeo_video_file = get_vimeo_video_file_url($data['video_url']);
                    
                    $files      =   $vimeo_video_file['files'];
                    $downloads  =   $vimeo_video_file['downloads'];


                    if(!empty($files))
                    {
                        $files = array_reverse($files);
                        $downloads = array_reverse($downloads);
                        
                        // Map downloads by rendition for quick lookup
                        $downloadMap = [];
                        foreach ($downloads as $download) {
                            $downloadMap[$download['rendition']] = $download['link'];
                        }
                        $downloadMap['adaptive'] = '';
                        
                    
                        // Add download link to files
                        foreach ($files as &$file) 
                        {
                            $file['download_link'] = $downloadMap[$file['rendition']] ?? null;
                            
                             if ($file['rendition'] === 'adaptive') 
                             {
                                $file['width'] = '1920'; // Default width for adaptive
                                $file['height'] = '1080'; // Default height for adaptive
                                
                                  $file = array_merge(
                                        array_slice($file, 0, array_search('type', array_keys($file)) + 1, true),
                                        ['width' => $file['width'], 'height' => $file['height']],
                                        array_slice($file, array_search('type', array_keys($file)) + 1, null, true)
                                    );
                            }
                            
                            
                        }
                        
                        usort($files, function ($a, $b) {
                                return strcmp($a['rendition'], $b['rendition']);
                            });
                            
                             // Sort files by rendition, placing "adaptive" first
                        usort($files, function ($a, $b) {
                            if ($a['rendition'] === 'adaptive') return -1;
                            if ($b['rendition'] === 'adaptive') return 1;
                            return strcmp($a['rendition'], $b['rendition']);
                        });
                        
                        
                        foreach ($files as $val) {
                   
                                // Prepare data for insertion
                                $vimeo_data = [
                                    'lesson_file_id' => $lesson_file_id,
                                    'quality' => isset($val['quality']) ? $val['quality'] : null,
                                    'rendition' => isset($val['rendition']) ? $val['rendition'] : null,
                                    'height' => isset($val['height']) ? $val['height'] : null, // Check for key existence
                                    'width' => isset($val['width']) ? $val['width'] : null,   // Check for key existence
                                    'type' => isset($val['type']) ? $val['type'] : null,
                                    'link' => isset($val['link']) ? $val['link'] : null,
                                    'fps' => isset($val['fps']) ? $val['fps'] : null,
                                    'size' => isset($val['size']) ? $val['size'] : null,
                                    'public_name' => isset($val['public_name']) ? $val['public_name'] : null,
                                    'size_short' => isset($val['size_short']) ? $val['size_short'] : null,
                                    'download_link' => isset($val['download_link']) ? $val['download_link'] : null,
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                        
                                // Insert into database
                                 $this->vimeo_videolinks_m->insert($vimeo_data);

                        }
                        
                    }
                }
            
            }

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/lesson_files/index/'.$data['lesson_id'].'/');
        }else{
            redirect('admin/course/index/');
        }
    }
    

    /*
     * Edit Lesson Files
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/course/index/');
        }else{
            $precious_data = $this->lesson_files_m->get(['id' => $id])->row();
            
            log_message('error','$precious_data: '.print_r($precious_data,true));
            
            if ($this->input->post()){
                $data['title']          = $this->input->post('title');

                $thumbnail = $this->upload_file('thumbnails/lesson_files', 'thumbnail', true);
                if($thumbnail){
                    $data['thumbnail'] = $thumbnail['file'];
                }
                $lesson_provider = $this->input->post('lesson_provider');
                
                if($precious_data->lesson_type == 'other'){
                    if ($precious_data->attachment_type == 'pdf') {
                        $attachment = $this->upload_file('thumbnails/lesson_files', 'attachment', true);
                        if($attachment){
                            $data['attachment'] = $attachment['file'];
                        }
                        
                        $data['is_downloadable']  = $this->input->post('is_downloadable') == '1' ? '1' : '0';
                    }else if ($precious_data->attachment_type == 'audio') {
                        $audio_file = $this->upload_file('thumbnails/lesson_files', 'audio', true);
                        if($audio_file){
                            $data['attachment'] = $audio_file['file'];
                            $data['audio_file'] = $audio_file['file'];
                        }
                    }else if ($precious_data->attachment_type == 'notes') {
                        $data['notes'] = $this->input->post('notes');
                        $data['attachment'] = $this->input->post('notes');
                    }else if ($precious_data->attachment_type == 'exam' || $precious_data->attachment_type == 'practice') {
                        $data['publish_result'] = $this->input->post('publish_result') == 1 ? 1 : 0;
                        
                        if ($precious_data->attachment_type == 'exam') {
                            $data['is_practice']    = $this->input->post('e_is_practice');
                            $duration_formatter = explode(':', $this->input->post('quiz_duration'));
                            $hour = sprintf('%02d', $duration_formatter[0]);
                            $min = sprintf('%02d', $duration_formatter[1]);
                            $sec = sprintf('%02d', $duration_formatter[2]);
                            $data['duration']       = $hour . ':' . $min . ':' . $sec;
                            
                            $data['from_date']      = $this->input->post('from_date');
                            $data['from_time']      = $this->input->post('from_time');
                            $data['to_date']        = $this->input->post('to_date');
                            $data['to_time']        = $this->input->post('to_time');
                        }else if ($precious_data->attachment_type == 'practice') {
                            $data['is_practice']    = $this->input->post('p_is_practice');
                        }
                    }else if($precious_data->attachment_type == 'live') {
                        $data['live_type']  = $this->input->post('live_type');
                        $data['student_id'] = $this->input->post('student_id');
                        $data['zoom_id']    = $this->input->post('zoom_id');
                        $data['zoom_password']   = $this->input->post('password');
                        $data['from_date']  = $this->input->post('fromDate');
                        $data['from_time']  = $this->input->post('fromTime');
                        $data['to_date']    = $this->input->post('toDate');
                        $data['to_time']    = $this->input->post('toTime');
                        $data['role_id']    = $this->input->post('role_id');
                    } else if ($precious_data->attachment_type == 'activity') {
                        $data['activity_type'] = $this->input->post('activity_type');
                        $data['description'] = html_escape($this->input->post('activity_description'));
                        $activity = $this->upload_file('thumbnails/lesson_files', 'activity_file', true);
                        if($activity){
                            $data['activity_file'] = $activity['file'];
                        } 
                    } else if ($precious_data->attachment_type == 'habit'){
                        $data['habit_id'] = $this->input->post('habit_id');
                    }
                    
                }else{
                    if ($lesson_provider == 'youtube' || $lesson_provider == 'vimeo') {
                        $data['video_url']  = $this->input->post('video_url');
                        $data['attachment']  = $this->input->post('video_url');
                        $duration_formatter = explode(':', $this->input->post('duration'));
                        $hour               = sprintf('%02d', $duration_formatter[0]);
                        $min                = sprintf('%02d', $duration_formatter[1]);
                        $sec                = sprintf('%02d', $duration_formatter[2]);
                        $data['duration']       = $hour . ':' . $min . ':' . $sec;
                        $data['video_type']     = $lesson_provider;
                        $data['download_url']   = $this->input->post('download_url');
                        
                        $html5_url = get_vimeo_file_url($this->input->post('video_url'));
                        if($html5_url['status'] == 'true'){
                            $data['html5_video_url']  = $html5_url['video_link'];
                        }
                    }
                    
                    $data['is_portrait']  = $this->input->post('is_portrait') == '1' ? '1' : '0';
                }
                
                
                $data['free'] = $this->input->post('free') == 'on' ? 'on' : 'off';
                
                if (!empty($this->input->post('schedule_date'))){
                    $data['schedule_date']  = $this->input->post('schedule_date');
                }
                
                $data['description']    = html_escape($this->input->post('description'));
                $data['updated_on']     = date('Y-m-d H:i:s');

                $this->lesson_files_m->update($data, ['id' => $id]);
                $lesson_file_id = $id;
                if($precious_data->attachment_type == "url")
                {
                    if($lesson_provider == 'vimeo')
                    {
                        $remove_existing = $this->vimeo_videolinks_m->delete(['lesson_file_id' =>$lesson_file_id]);
                        // $logger = service('logger');
                        // $logger->error('Database Error: ' . db_connect()->getLastQuery());
    
                        $vimeo_video_file_url = get_vimeo_video_file_url($data['video_url']);
                                    
                        $files      =   $vimeo_video_file_url['files'];
                        $downloads  =   $vimeo_video_file_url['downloads'];
                                    
                        $files = array_reverse($files);
                        $downloads = array_reverse($downloads);
                                    
                                   
                        if(!empty($files))
                        {
                            // Map downloads by rendition for quick lookup
                            $downloadMap = [];
                            foreach ($downloads as $download) 
                            {
                                $downloadMap[$download['rendition']] = $download['link'];
                            }
                            
                            $downloadMap['adaptive'] = '';
                                        
                                    
                            // Add download link to files
                            foreach ($files as &$file) 
                            {
                                $file['download_link'] = $downloadMap[$file['rendition']] ?? null;
                                            
                                if ($file['rendition'] === 'adaptive') 
                                {
                                    $file['width'] = '1920'; // Default width for adaptive
                                    $file['height'] = '1080'; // Default height for adaptive
                                                
                                    $file = array_merge(
                                                array_slice($file, 0, array_search('type', array_keys($file)) + 1, true),
                                                        ['width' => $file['width'], 'height' => $file['height']],
                                                        array_slice($file, array_search('type', array_keys($file)) + 1, null, true)
                                                );
                                }
                            }
                                        
                            usort($files, function ($a, $b) {
                                    return strcmp($a['rendition'], $b['rendition']);
                            });
                                            
                            // Sort files by rendition, placing "adaptive" first
                            usort($files, function ($a, $b) {
                                if ($a['rendition'] === 'adaptive') return -1;
                                if ($b['rendition'] === 'adaptive') return 1;
                                    return strcmp($a['rendition'], $b['rendition']);
                            });
                                        
                                        
                            foreach ($files as $val)
                            {
                                // Prepare data for insertion
                                $vimeo_data = [
                                    'lesson_file_id' => $lesson_file_id,
                                    'quality' => isset($val['quality']) ? $val['quality'] : null,
                                    'rendition' => isset($val['rendition']) ? $val['rendition'] : null,
                                    'height' => isset($val['height']) ? $val['height'] : null, // Check for key existence
                                    'width' => isset($val['width']) ? $val['width'] : null,   // Check for key existence
                                    'type' => isset($val['type']) ? $val['type'] : null,
                                    'link' => isset($val['link']) ? $val['link'] : null,
                                    'fps' => isset($val['fps']) ? $val['fps'] : null,
                                    'size' => isset($val['size']) ? $val['size'] : null,
                                    'public_name' => isset($val['public_name']) ? $val['public_name'] : null,
                                    'size_short' => isset($val['size_short']) ? $val['size_short'] : null,
                                    'download_link' => isset($val['download_link']) ? $val['download_link'] : null,
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                                        
                                // Insert into database
                                $this->vimeo_videolinks_m->insert($vimeo_data);
    
                            }
                        }
                    }
                }
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
            }
            redirect('admin/lesson_files/index/'.$precious_data->lesson_id.'/');
        }
    }

    /*
     * Delete Lesson Files
     */
    public function delete($id = 0){
        if($id > 0){
            $lesson_id = $this->lesson_files_m->get(['id' => $id])->row()->lesson_id;
            $this->lesson_files_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/lesson_files/index/'.$lesson_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }

    
    /*
     * Sort Lesson Files
     */
    public function ajax_sort_lesson_files() {
        $lesson_json = $this->input->post('itemJSON');
        $this->lesson_files_m->sort_lesson_files($lesson_json);
    }

    
    
    public function update_video_url() {
        // Fetch all video lesson files
        $lesson_ids = array_column($this->db->get_where('lesson', ['course_id' => 63])->result_array(), 'id');
        
        $this->db->where_in('lesson_id', $lesson_ids);
        $videos = $this->lesson_files_m->get(['lesson_type' => 'video'])->result_array();

        // // Iterate over each video
        foreach ($videos as $video) {
            $video_id = $video['id'];
            $video_url = $video['video_url'];
            $vimeo_video_file_url = get_vimeo_video_file_url($video_url);
                                    
            $files      =   $vimeo_video_file_url['files'];
            $downloads  =   $vimeo_video_file_url['downloads'];
                        
            $files = array_reverse($files);
            $downloads = array_reverse($downloads);
                        
                       
            if(!empty($files))
            {
                // Map downloads by rendition for quick lookup
                $downloadMap = [];
                foreach ($downloads as $download) 
                {
                    $downloadMap[$download['rendition']] = $download['link'];
                }
                
                $downloadMap['adaptive'] = '';
                            
                        
                // Add download link to files
                foreach ($files as &$file) 
                {
                    $file['download_link'] = $downloadMap[$file['rendition']] ?? null;
                                
                    if ($file['rendition'] === 'adaptive') 
                    {
                        $file['width'] = '1920'; // Default width for adaptive
                        $file['height'] = '1080'; // Default height for adaptive
                                    
                        $file = array_merge(
                                    array_slice($file, 0, array_search('type', array_keys($file)) + 1, true),
                                            ['width' => $file['width'], 'height' => $file['height']],
                                            array_slice($file, array_search('type', array_keys($file)) + 1, null, true)
                                    );
                    }
                }
                            
                usort($files, function ($a, $b) {
                        return strcmp($a['rendition'], $b['rendition']);
                });
                                
                // Sort files by rendition, placing "adaptive" first
                usort($files, function ($a, $b) {
                    if ($a['rendition'] === 'adaptive') return -1;
                    if ($b['rendition'] === 'adaptive') return 1;
                        return strcmp($a['rendition'], $b['rendition']);
                });
                            
                            
                foreach ($files as $val)
                {
                    // Prepare data for insertion
                    $vimeo_data = [
                        'lesson_file_id' => $video_id,
                        'quality' => isset($val['quality']) ? $val['quality'] : null,
                        'rendition' => isset($val['rendition']) ? $val['rendition'] : null,
                        'height' => isset($val['height']) ? $val['height'] : null, // Check for key existence
                        'width' => isset($val['width']) ? $val['width'] : null,   // Check for key existence
                        'type' => isset($val['type']) ? $val['type'] : null,
                        'link' => isset($val['link']) ? $val['link'] : null,
                        'fps' => isset($val['fps']) ? $val['fps'] : null,
                        'size' => isset($val['size']) ? $val['size'] : null,
                        'public_name' => isset($val['public_name']) ? $val['public_name'] : null,
                        'size_short' => isset($val['size_short']) ? $val['size_short'] : null,
                        'download_link' => isset($val['download_link']) ? $val['download_link'] : null,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                            
                    // Insert into database
                    $this->vimeo_videolinks_m->insert($vimeo_data);

                }
                            
            }
            
            log_message("error", 'Videos : ' .print_r($video_url,true));
            
            // Get HTML5 video URL from Vimeo
            $html5_url = get_vimeo_file_url($video_url);
            
            log_message("error", 'Videos : ' .print_r($html5_url,true));
            // Check if conversion was successful
            if ($html5_url['status'] === 'true') {
                // Prepare the update data
                $data = ['html5_video_url' => $html5_url['video_link']];
                
                // Update the video URL in the database
                $this->lesson_files_m->update($data, ['id' => $video_id, 'lesson_type' => 'video']);
            }
        }
    }
    
    
    // public function update_urls(){
    //     $videos = $this->db->get_where('lesson_files', ['attachment_type' => 'url', 'video_type' => 'vimeo'])->result_array();
        
    //     foreach($videos as $video)
    //     {
    //         $lesson_file_id = $video['id'];
    //         $remove_existing = $this->vimeo_videolinks_m->delete(['lesson_file_id' =>$lesson_file_id]);
    //         // $logger = service('logger');
    //         // $logger->error('Database Error: ' . db_connect()->getLastQuery());

    //         $vimeo_video_file_url = get_vimeo_video_file_url($video['video_url']);
                        
    //         $files      =   $vimeo_video_file_url['files'];
    //         $downloads  =   $vimeo_video_file_url['downloads'];
                        
    //         $files = array_reverse($files);
    //         $downloads = array_reverse($downloads);
            
    //         log_message("error", "jkbkb ". print_r($files, true));            
                       
    //         if(!empty($files))
    //         {
    //             // Map downloads by rendition for quick lookup
    //             $downloadMap = [];
    //             foreach ($downloads as $download) 
    //             {
    //                 $downloadMap[$download['rendition']] = $download['link'];
    //             }
                
    //             $downloadMap['adaptive'] = '';
                            
                        
    //             // Add download link to files
    //             foreach ($files as &$file) 
    //             {
    //                 $file['download_link'] = $downloadMap[$file['rendition']] ?? null;
                                
    //                 if ($file['rendition'] === 'adaptive') 
    //                 {
    //                     $file['width'] = '1920'; // Default width for adaptive
    //                     $file['height'] = '1080'; // Default height for adaptive
                                    
    //                     $file = array_merge(
    //                                 array_slice($file, 0, array_search('type', array_keys($file)) + 1, true),
    //                                         ['width' => $file['width'], 'height' => $file['height']],
    //                                         array_slice($file, array_search('type', array_keys($file)) + 1, null, true)
    //                                 );
    //                 }
    //             }
                            
    //             usort($files, function ($a, $b) {
    //                     return strcmp($a['rendition'], $b['rendition']);
    //             });
                                
    //             // Sort files by rendition, placing "adaptive" first
    //             usort($files, function ($a, $b) {
    //                 if ($a['rendition'] === 'adaptive') return -1;
    //                 if ($b['rendition'] === 'adaptive') return 1;
    //                     return strcmp($a['rendition'], $b['rendition']);
    //             });
                            
                            
    //             foreach ($files as $val)
    //             {
    //                 // Prepare data for insertion
    //                 $vimeo_data = [
    //                     'lesson_file_id' => $lesson_file_id,
    //                     'quality' => isset($val['quality']) ? $val['quality'] : null,
    //                     'rendition' => isset($val['rendition']) ? $val['rendition'] : null,
    //                     'height' => isset($val['height']) ? $val['height'] : null, // Check for key existence
    //                     'width' => isset($val['width']) ? $val['width'] : null,   // Check for key existence
    //                     'type' => isset($val['type']) ? $val['type'] : null,
    //                     'link' => isset($val['link']) ? $val['link'] : null,
    //                     'fps' => isset($val['fps']) ? $val['fps'] : null,
    //                     'size' => isset($val['size']) ? $val['size'] : null,
    //                     'public_name' => isset($val['public_name']) ? $val['public_name'] : null,
    //                     'size_short' => isset($val['size_short']) ? $val['size_short'] : null,
    //                     'download_link' => isset($val['download_link']) ? $val['download_link'] : null,
    //                     'created_at' => date('Y-m-d H:i:s')
    //                 ];
                            
    //                 // Insert into database
    //                 $this->vimeo_videolinks_m->insert($vimeo_data);

    //             }
    //         }
    //     }
    // }
    
    public function ajax_get_quiz_by_lesson(){
        $lesson_id = $this->input->get('lesson_id');
        $quizes = $this->db->get_where('lesson_files', ['attachment_type' => 'practice', 'lesson_id' => $lesson_id])->result_array();
        log_message("error", "kjnkn ". print_r($this->db->last_query(),true));
        echo "<option value=''>Select quiz</option>";
        foreach ($quizes as $quiz){
            echo "<option value='{$quiz['id']}'>{$quiz['title']}</option>";
        }
    }
    
    
    // public function update_video_links(){
    //     $vimeo_video_file = get_vimeo_video_file_url('https://vimeo.com/1056996676');
    //     log_message("error", "jknk ". print_r($vimeo_video_file, true));
    //     $files      =   $vimeo_video_file['files'];
    //     $downloads  =   $vimeo_video_file['downloads'];


    //     if(!empty($files))
    //     {
    //         $files = array_reverse($files);
    //         $downloads = array_reverse($downloads);
            
    //         // Map downloads by rendition for quick lookup
    //         $downloadMap = [];
    //         foreach ($downloads as $download) {
    //             $downloadMap[$download['rendition']] = $download['link'];
    //         }
    //         $downloadMap['adaptive'] = '';
            
        
    //         // Add download link to files
    //         foreach ($files as &$file) 
    //         {
    //             $file['download_link'] = $downloadMap[$file['rendition']] ?? null;
                
    //              if ($file['rendition'] === 'adaptive') 
    //              {
    //                 $file['width'] = '1920'; // Default width for adaptive
    //                 $file['height'] = '1080'; // Default height for adaptive
                    
    //                   $file = array_merge(
    //                         array_slice($file, 0, array_search('type', array_keys($file)) + 1, true),
    //                         ['width' => $file['width'], 'height' => $file['height']],
    //                         array_slice($file, array_search('type', array_keys($file)) + 1, null, true)
    //                     );
    //             }
                
                
    //         }
            
    //         usort($files, function ($a, $b) {
    //                 return strcmp($a['rendition'], $b['rendition']);
    //             });
                
    //              // Sort files by rendition, placing "adaptive" first
    //         usort($files, function ($a, $b) {
    //             if ($a['rendition'] === 'adaptive') return -1;
    //             if ($b['rendition'] === 'adaptive') return 1;
    //             return strcmp($a['rendition'], $b['rendition']);
    //         });
            
            
    //         foreach ($files as $val) {
       
    //                 // Prepare data for insertion
    //                 $vimeo_data = [
    //                     'lesson_file_id' => 542,
    //                     'quality' => isset($val['quality']) ? $val['quality'] : null,
    //                     'rendition' => isset($val['rendition']) ? $val['rendition'] : null,
    //                     'height' => isset($val['height']) ? $val['height'] : null, // Check for key existence
    //                     'width' => isset($val['width']) ? $val['width'] : null,   // Check for key existence
    //                     'type' => isset($val['type']) ? $val['type'] : null,
    //                     'link' => isset($val['link']) ? $val['link'] : null,
    //                     'fps' => isset($val['fps']) ? $val['fps'] : null,
    //                     'size' => isset($val['size']) ? $val['size'] : null,
    //                     'public_name' => isset($val['public_name']) ? $val['public_name'] : null,
    //                     'size_short' => isset($val['size_short']) ? $val['size_short'] : null,
    //                     'download_link' => isset($val['download_link']) ? $val['download_link'] : null,
    //                     'created_at' => date('Y-m-d H:i:s')
    //                 ];
            
    //                 // Insert into database
    //                  $this->vimeo_videolinks_m->insert($vimeo_data);

    //         }
            
    //     }
    // }
    
    
    
    
    
    
    
}