<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
/*
 *  <AUTHOR> Creativeitem
 *  date    : 14 september, 2017
 *  Ekattor School Management System Pro
 *  http://codecanyon.net/user/Creativeitem
 *  http://support.creativeitem.com
 */
class Modal extends Admin_controller {


	function __construct()
  {
    parent::__construct();
    $this->load->database();
    $this->load->library('session');
    /*cache control*/
    $this->output->set_header('Last-Modified: ' . gmdate("D, d M Y H:i:s") . ' GMT');
    $this->output->set_header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
    $this->output->set_header('Pragma: no-cache');
    $this->output->set_header("Expires: Mon, 26 Jul 1997 05:00:00 GMT");


      $this->load->model('api_m');
      $this->load->model('course_m');
      $this->load->model('category_m');
      $this->load->model('section_m');
      $this->load->model('lesson_m');
      $this->load->model('lesson_files_m');
      $this->load->model('tasks_m');

      $this->load->model('batch_m');
      $this->load->model('batch_lesson_m');
      $this->load->model('batch_lesson_files_m');
      $this->load->model('batch_tasks_m');
      $this->load->model('batch_students_m');
      $this->load->model('batch_lesson_schedule_m');

      $this->load->model('expense_category_m');
      $this->load->model('accounts_m');
      $this->load->model('expense_m');
      $this->load->model('quiz_m');
      $this->load->model('question_m');
      $this->load->model('user_m');
      $this->load->model('package_m');
      $this->load->model('package_lessons_m');
      $this->load->model('payment_info_m');
      $this->load->model('topic_m');
      $this->load->model('student_m');
      $this->load->model('enrol_m');
      $this->load->model('module_category_m');
      $this->load->model('module_completion_history_m');

  }

	function popup($page_name = 'get' , $param2 = '' , $param3 = '', $param4 = '', $param5 = '', $param6 = '', $param7 = '')
	{
        if ($page_name == 'get'){
            $page_name = $this->input->get('page_name');
        }
		$page_data['param2']		=	$param2;
		$page_data['param3']		=	$param3;
		$page_data['param4']		=	$param4;
		$page_data['param5']		=	$param5;
		$page_data['param6']		=	$param6;
		$page_data['param7']		=	$param7;
		$this->load->view( 'admin/'.$page_name.'.php' ,$page_data);
	}
}
