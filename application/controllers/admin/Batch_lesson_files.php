<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Batch_lesson_files extends Admin_Controller {
    public function __construct () {
        parent::__construct();
        $this->load->model('batch_lesson_files_m');
        $this->load->model('batch_lesson_m');
    }


    public function index($lesson_id = 0){
        if ($lesson_id == 0){
            redirect('admin/course/index/');
        }else{
            $lesson                     = $this->batch_lesson_m->get(['id' => $lesson_id])->row();
            $this->data['list_items']   = $this->batch_lesson_files_m->get(['lesson_id' => $lesson_id])->result_array();
            $this->data['course_id']    = $lesson->course_id;
            $this->data['lesson_id']    = $lesson_id;
            $this->data['lesson_title'] = $lesson->title;
            $this->data['page_title']   = 'Lesson Files - '.$lesson->title;
            $this->data['page_name']    = 'batch_lesson_files/index';
            $this->load->view('admin/index', $this->data);
        }
    }


    public function add(){
        if ($this->input->post()){
            $data['lesson_id']      = $this->input->post('lesson_id');
            $data['title']          = $this->input->post('title');

            $lesson_type_array          = explode('-', $this->input->post('lesson_type'));
            $lesson_type                = $lesson_type_array[0];
            $data['attachment_type']    = $lesson_type_array[1];
            $data['lesson_type']        = $lesson_type;

            if ($lesson_type_array[1] == 'pdf') {
                $attachment = $this->upload_file('lesson_files', 'attachment', true);
                if($attachment){
                    $data['attachment'] = $attachment['file'];
                }
            }

            if ($lesson_type == 'video') {
                $lesson_provider = $this->input->post('lesson_provider');
                if ($lesson_provider == 'youtube' || $lesson_provider == 'vimeo') {
                    $data['video_url']  = $this->input->post('video_url');
                    $duration_formatter = explode(':', $this->input->post('duration'));
                    $hour               = sprintf('%02d', $duration_formatter[0]);
                    $min                = sprintf('%02d', $duration_formatter[1]);
                    $sec                = sprintf('%02d', $duration_formatter[2]);
                    $data['duration']       = $hour . ':' . $min . ':' . $sec;
                    $data['video_type']     = $lesson_provider;
                    $data['download_url']   = $this->input->post('download_url');
                }
            }

            $data['free']           = $this->input->post('free') == 'on' ? 'on' : 'off';

            $data['created_on']     = date('Y-m-d H:i:s');
            $data['updated_on']     = date('Y-m-d H:i:s');

            $lesson_file_id = $this->batch_lesson_files_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/batch_lesson_files/index/'.$data['lesson_id'].'/');
        }else{
            redirect('admin/course/index/');
        }
    }


    public function edit($id = 0){
        if ($id == 0){
            redirect('/course/index/');
        }else{
            $precious_data = $this->batch_lesson_files_m->get(['id' => $id])->row();
            if ($this->input->post()){
                $data['title']          = $this->input->post('title');



                if ($precious_data->lesson_type == 'other'){
                    $attachment = $this->upload_file('lesson_files', 'attachment', true);
                    if($attachment){
                        $data['attachment'] = $attachment['file'];
                    }
                }else{
                    $lesson_provider = $this->input->post('lesson_provider');
                    if ($lesson_provider == 'youtube' || $lesson_provider == 'vimeo') {
                        $data['video_url']  = $this->input->post('video_url');
                        $duration_formatter = explode(':', $this->input->post('duration'));
                        $hour               = sprintf('%02d', $duration_formatter[0]);
                        $min                = sprintf('%02d', $duration_formatter[1]);
                        $sec                = sprintf('%02d', $duration_formatter[2]);
                        $data['duration']       = $hour . ':' . $min . ':' . $sec;
                        $data['video_type']     = $lesson_provider;
                        $data['download_url']   = $this->input->post('download_url');
                    }
                }
                $data['free']           = $this->input->post('free') == 'on' ? 'on' : 'off';

                $data['updated_on']     = date('Y-m-d H:i:s');

                $this->batch_lesson_files_m->update($data, ['id' => $id]);
                log_message('error', $this->db->last_query());

                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
            }
            redirect('admin/batch_lesson_files/index/'.$precious_data->lesson_id.'/');
        }

    }


    public function delete($id = 0){
        if ($id > 0){
            $lesson_id = $this->batch_lesson_files_m->get(['id' => $id])->row()->lesson_id;
            $this->batch_lesson_files_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/batch_lesson_files/index/'.$lesson_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }

}