<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Batch extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('batch_m');
        $this->load->model('course_m');
    }

    public function index($course_id){
        $course_title  = $this->course_m->get(['id' => $course_id])->row()->title;
        $this->data['course_id']    = $course_id;
        $this->data['list_items']   = $this->batch_m->get(['course_id' => $course_id])->result_array();
        $this->data['page_title']   = 'Batches - '.$course_title;
        $this->data['page_name']    = 'batch/index';
        $this->load->view('admin/index', $this->data);
    }

    public function add(){
        if ($this->input->post()){
            $data['title']       = $this->input->post('title');
            $data['start_date']  = $this->input->post('start_date');
            $data['end_date']    = $this->input->post('end_date');
            $data['course_id']   = $this->input->post('course_id');
            $data['created_on']  = date('Y-m-d H:i:s');
            $data['updated_on']  = date('Y-m-d H:i:s');
            $this->batch_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('batch_added_successfully'));

            redirect('admin/batch/index/'.$data['course_id'].'/');
        }else{
            redirect('admin/course/index/');
        }
    }


    public function edit($id = 0){
        if ($id == 0){
            redirect('/course/index/');
        }else{
            if ($this->input->post()){
                $data['title']       = $this->input->post('title');
                $data['start_date']  = $this->input->post('start_date');
                $data['end_date']    = $this->input->post('end_date');
                $data['updated_on']  = date('Y-m-d H:i:s');

                $this->batch_m->update($data, ['id' => $id]);
                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully!'));

                $course_id = $this->batch_m->get(['id' => $id])->row()->course_id;

                redirect('admin/batch/index/'.$course_id.'/');
            }
        }
    }


    public function delete($id = 0){
        if ($id > 0){
            $course_id = $this->batch_m->get(['id' => $id])->row()->course_id;

            $this->batch_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/batch/index/'.$course_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }
    
    
    
    public function ajax_get_batch_by_course(){
        $course_id = $this->input->get('course_id');
        $batches = $this->batch_m->get(['course_id' => $course_id])->result_array();
        echo "<option value=''>Select Batch</option>";
        foreach ($batches as $batch){
            echo "<option value='{$batch['id']}'>{$batch['title']}</option>";
        }
    }



}