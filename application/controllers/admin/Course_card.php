<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Course_card extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('course_card_m');
        
    }

    /*
     * Index Page
     */
    public function index($course_id){
        $data['course_id']    = $course_id;
        $data['page_name']    = 'card/course_lists';
        $data['card_lists']   =  $this->db->get_where('course_card', array('course_id' => $course_id,))->result_array();
        $this->load->view('admin/index', $data);
    }
    
    public function add($course_id){
        
        if($this->input->post()){
            $data['title']         = $this->input->post('Card_title');
            $data['details']       = $this->trim_and_return_json($this->input->post('points'));
            $data['course_id']     = $course_id;
            $data['date']          = date('Y-m-d');
            $insert                = $this->course_card_m->insert($data);
            if($insert){
                $this->session->set_flashdata('flash_message', get_phrase('course_card_added_successfully'));
            }else{
                $this->session->set_flashdata('flash_message', get_phrase('Server_error'));
            }
        }
        $value['page_name']    = 'card/Add';
        $value['course_id']    = $course_id;
        $this->load->view('admin/index', $value);
    }
    
    public function delete($id,$course_id){
        $this->course_card_m->delete(['id' => $id]);
        $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
        $data['course_id']    = $course_id;
        $data['page_name']    = 'card/course_lists';
        $data['card_lists']   =  $this->db->get_where('course_card', array('id' => $id,))->result_array();
        $this->load->view('admin/index', $data);
    }
    
    public function edit($id,$course_id){
        $data['course_id']    = $course_id;
        $data['id']           = $id;
        $data['page_name']    = 'card/edit';
        $data['card_lists']   =  $this->db->get_where('course_card', array('id' => $id,))->result_array();
        $this->load->view('admin/index', $data);  
    }
    
    public function update($course_is,$id){
            $data['title']         = $this->input->post('Card_title');
            $data['details']       = json_encode($this->input->post('points'));;
            $update                = $this->course_card_m->update($data, ['id' => $id]);
            if($update){
                $this->session->set_flashdata('flash_message', get_phrase('course_card_added_successfully'));
            }else{
                $this->session->set_flashdata('flash_message', get_phrase('Server_error'));
            }
            
        redirect('admin/Course_card/index/'.$course_is);    
    }
    
    
    
}