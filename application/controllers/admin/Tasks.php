<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Tasks extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	TUTORPRO
    | -----------------------------------------------------
    | AUTHOR:			TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY TROGON MEDIA PVT LTD
    | -----------------------------------------------------
    | WEBSITE:			http://trogonmedia.com
    | -----------------------------------------------------
    */
    public function __construct () {
        parent::__construct();
        $this->load->model('tasks_m');
        $this->load->model('lesson_m');
    }

    /*
     * Index Page
     */
    public function index($lesson_id = 0){
        if ($lesson_id == 0){
            redirect('admin/course/index/');
        }else{
            $lesson                     = $this->lesson_m->get(['id' => $lesson_id])->row();
            $this->data['list_items']   = $this->tasks_m->get_tasks_by_lesson_id($lesson_id)->result_array();
            $this->data['course_id']    = $lesson->course_id;
            $this->data['lesson_id']    = $lesson_id;
            $this->data['lesson_title'] = $lesson->title;
            $this->data['page_title']   = 'Tasks - '.$lesson->title;
            $this->data['page_name']    = 'tasks/index';
            $this->load->view('admin/index', $this->data);
        }
    }

    /*
     * Add Task
     */
    public function add(){
        if ($this->input->post()){
            $data['lesson_id']      = $this->input->post('lesson_id');
            $data['task_type']      = $this->input->post('task_type');
            $data['title']          = $this->input->post('title');

            // upload question file
            $file_question = $this->upload_file_multiple('task', 'file_question', true);
            if(count($file_question)){
                $data['file_question'] = json_encode($file_question);
            }

            // upload answer file
            $file_answer = $this->upload_file_multiple('task', 'file_answer', true);
            if(count($file_answer)){
                $data['file_answer'] = json_encode($file_answer);
            }

            // upload audio file
            $file_audio = $this->upload_file_multiple('task', 'file_audio', true);
            if(count($file_audio)){
                $data['file_audio'] = json_encode($file_audio);
            }

            $data['created_on']     = date('Y-m-d H:i:s');
            $data['updated_on']     = date('Y-m-d H:i:s');

            $task_id = $this->tasks_m->insert($data);

            $this->session->set_flashdata('flash_message', get_phrase('added_successfully'));
            redirect('admin/tasks/index/'.$data['lesson_id'].'/');
        }else{
            redirect('admin/course/index/');
        }
    }

    /*
     * Edit Task
     */
    public function edit($id = 0){
        if ($id == 0){
            redirect('/course/index/');
        }else{
            if ($this->input->post()){
                $data['lesson_id']      = $this->input->post('lesson_id');
                $data['task_type']      = $this->input->post('task_type');
                $data['title']          = $this->input->post('title');

                // upload question file
                $file_question = $this->upload_file_multiple('task', 'file_question', true);
                if(count($file_question)){
                    $data['file_question'] = json_encode($file_question);
                }

                // upload answer file
                $file_answer = $this->upload_file_multiple('task', 'file_answer', true);
                if(count($file_answer)){
                    $data['file_answer'] = json_encode($file_answer);
                }

                // upload audio file
                $file_audio = $this->upload_file_multiple('task', 'file_audio', true);
                if(count($file_audio)){
                    $data['file_audio'] = json_encode($file_audio);
                }

                $data['updated_on']     = date('Y-m-d H:i:s');

                $this->tasks_m->update($data, ['id' => $id]);

                $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
                redirect('admin/tasks/index/'.$data['lesson_id'].'/');
            }
        }
    }

    /*
     * Delete Lesson
     */
    public function delete($id = 0){
        if ($id > 0){
            $lesson_id = $this->tasks_m->get(['id' => $id])->row()->lesson_id;
            $this->tasks_m->delete(['id' => $id]);
            $this->session->set_flashdata('flash_message', get_phrase('deleted_successfully'));
            redirect('admin/tasks/index/'.$lesson_id.'/');
        }else{
            redirect('admin/course/index/');
        }
    }
    
    
    /*
     * Sort Task
     */
    public function ajax_sort_task() {
        $task_json = $this->input->post('itemJSON');
        $this->tasks_m->sort_task($task_json);
    }

}