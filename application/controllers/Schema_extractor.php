<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Schema_extractor extends CI_Controller {
    
    public function __construct() {
        parent::__construct();
        $this->load->database();
        $this->load->helper('file');
    }
    
    public function extract_to_file() {
        set_time_limit(0);
        ini_set('memory_limit', '512M');
        
        $schema_content = "DATABASE SCHEMA EXPORT\n";
        $schema_content .= "Generated on: " . date('Y-m-d H:i:s') . "\n";
        $schema_content .= "Database: " . $this->db->database . "\n";
        $schema_content .= str_repeat("=", 80) . "\n\n";
        
        // Get all tables
        $tables = $this->db->list_tables();
        
        foreach($tables as $table) {
            $schema_content .= "TABLE: $table\n";
            $schema_content .= str_repeat("-", 40) . "\n";
            
            // Get table structure using SHOW CREATE TABLE
            $query = $this->db->query("SHOW CREATE TABLE `$table`");
            if($query && $query->num_rows() > 0) {
                $result = $query->row_array();
                $create_table = $result['Create Table'];
                $schema_content .= $create_table . "\n\n";
            }
            
            // Get field information
            $fields = $this->db->field_data($table);
            $schema_content .= "FIELD DETAILS:\n";
            foreach($fields as $field) {
                $schema_content .= sprintf("  %-20s %-15s %-10s %-10s %-15s\n", 
                    $field->name, 
                    $field->type, 
                    $field->max_length ?: 'N/A',
                    $field->primary_key ? 'PRIMARY' : '',
                    $field->default ?: 'NULL'
                );
            }
            
            // Get indexes
            $indexes = $this->db->query("SHOW INDEX FROM `$table`");
            if($indexes && $indexes->num_rows() > 0) {
                $schema_content .= "\nINDEXES:\n";
                foreach($indexes->result_array() as $index) {
                    $schema_content .= sprintf("  %-20s %-15s %-10s\n",
                        $index['Key_name'],
                        $index['Column_name'],
                        $index['Non_unique'] ? 'NON-UNIQUE' : 'UNIQUE'
                    );
                }
            }
            
            $schema_content .= "\n" . str_repeat("=", 80) . "\n\n";
        }
        
        // Create directory if it doesn't exist
        $export_dir = 'db_schema_exports';
        if (!is_dir($export_dir)) {
            mkdir($export_dir, 0755, true);
        }
        
        // Write to file
        $filename = $export_dir . '/database_schema_' . date('Y-m-d_H-i-s') . '.txt';
        
        if(write_file($filename, $schema_content)) {
            echo "Schema exported successfully to: $filename<br>";
            echo "File size: " . number_format(filesize($filename)) . " bytes<br>";
            echo "<a href='" . base_url($filename) . "' target='_blank'>Download Schema File</a>";
        } else {
            echo "Failed to write schema file.";
        }
    }
    
    public function extract_detailed() {
        set_time_limit(0);
        ini_set('memory_limit', '512M');
        
        $schema_content = "DETAILED DATABASE SCHEMA EXPORT\n";
        $schema_content .= "Generated on: " . date('Y-m-d H:i:s') . "\n";
        $schema_content .= "Database: " . $this->db->database . "\n";
        $schema_content .= str_repeat("=", 100) . "\n\n";
        
        // Get database info
        $db_info = $this->db->query("SELECT VERSION() as version")->row();
        $schema_content .= "MySQL Version: " . $db_info->version . "\n\n";
        
        $tables = $this->db->list_tables();
        $schema_content .= "Total Tables: " . count($tables) . "\n\n";
        
        foreach($tables as $table) {
            $schema_content .= str_repeat("=", 100) . "\n";
            $schema_content .= "TABLE: $table\n";
            $schema_content .= str_repeat("=", 100) . "\n";
            
            // Table status
            $status = $this->db->query("SHOW TABLE STATUS LIKE '$table'")->row_array();
            if($status) {
                $schema_content .= "Engine: " . $status['Engine'] . "\n";
                $schema_content .= "Rows: " . number_format($status['Rows']) . "\n";
                $schema_content .= "Data Length: " . number_format($status['Data_length']) . " bytes\n";
                $schema_content .= "Auto Increment: " . ($status['Auto_increment'] ?: 'N/A') . "\n";
                $schema_content .= "Collation: " . $status['Collation'] . "\n\n";
            }
            
            // Full CREATE TABLE statement
            $query = $this->db->query("SHOW CREATE TABLE `$table`");
            if($query && $query->num_rows() > 0) {
                $result = $query->row_array();
                $schema_content .= "CREATE TABLE STATEMENT:\n";
                $schema_content .= str_repeat("-", 50) . "\n";
                $schema_content .= $result['Create Table'] . "\n\n";
            }
            
            // Foreign key relationships
            $fk_query = $this->db->query("
                SELECT 
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '$table' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            
            if($fk_query && $fk_query->num_rows() > 0) {
                $schema_content .= "FOREIGN KEY RELATIONSHIPS:\n";
                $schema_content .= str_repeat("-", 50) . "\n";
                foreach($fk_query->result_array() as $fk) {
                    $schema_content .= sprintf("  %s -> %s.%s\n", 
                        $fk['COLUMN_NAME'], 
                        $fk['REFERENCED_TABLE_NAME'], 
                        $fk['REFERENCED_COLUMN_NAME']
                    );
                }
                $schema_content .= "\n";
            }
            
            $schema_content .= "\n";
        }
        
        // Create directory if it doesn't exist
        $export_dir = 'db_schema_exports';
        if (!is_dir($export_dir)) {
            mkdir($export_dir, 0755, true);
        }
        
        // Write to file
        $filename = $export_dir . '/detailed_schema_' . date('Y-m-d_H-i-s') . '.txt';
        
        if(write_file($filename, $schema_content)) {
            echo "Detailed schema exported successfully to: $filename<br>";
            echo "File size: " . number_format(filesize($filename)) . " bytes<br>";
            echo "<a href='" . base_url($filename) . "' target='_blank'>Download Detailed Schema File</a>";
        } else {
            echo "Failed to write detailed schema file.";
        }
    }
}