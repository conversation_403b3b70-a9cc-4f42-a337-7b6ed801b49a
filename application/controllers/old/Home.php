<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        // Your own constructor code
        $this->load->database();
        $this->load->library('session');

        // $this->load->library('stripe');
        /*cache control*/
        $this->output->set_header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        $this->output->set_header('Pragma: no-cache');
        if (!$this->session->userdata('cart_items')) {
            $this->session->set_userdata('cart_items', array());
        }
        log_message('error', "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]");
        log_message('error', json_encode($_REQUEST));
    }

    public function index() {
        $this->home();
        // $this->load->view('frontend/default/triz/index');
    }

    public function home() {
        // $page_data['page_name'] = "home";
        // $page_data['page_title'] = get_phrase('home');
        // $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
        // //  redirect(site_url('login'), 'refresh');
        // $this->load->view('website/index');
        redirect(site_url('login'), 'refresh');
    }
    
    public function shopping_cart() {
        if (!$this->session->userdata('cart_items')) {
            $this->session->set_userdata('cart_items', array());
        }
        $page_data['page_name'] = "shopping_cart";
        $page_data['page_title'] = get_phrase('shopping_cart');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function courses() {
        if (!$this->session->userdata('layout')) {
            $this->session->set_userdata('layout', 'list');
        }
        $layout = $this->session->userdata('layout');
        $selected_category_id = "all";
        $selected_price = "all";
        $selected_level = "all";
        $selected_language = "all";
        $selected_rating = "all";
        // Get the category ids
        if (isset($_GET['category']) && !empty($_GET['category'] && $_GET['category'] != "all")) {
            $selected_category_id = $this->crud_model->get_category_id($_GET['category']);
        }

        // Get the selected price
        if (isset($_GET['price']) && !empty($_GET['price'])) {
            $selected_price = $_GET['price'];
        }

        // Get the selected level
        if (isset($_GET['level']) && !empty($_GET['level'])) {
            $selected_level = $_GET['level'];
        }

        // Get the selected language
        if (isset($_GET['language']) && !empty($_GET['language'])) {
            $selected_language = $_GET['language'];
        }

        // Get the selected rating
        if (isset($_GET['rating']) && !empty($_GET['rating'])) {
            $selected_rating = $_GET['rating'];
        }


        if ($selected_category_id == "all" && $selected_price == "all" && $selected_level == 'all' && $selected_language == 'all' && $selected_rating == 'all') {
            $this->db->where('status', 'active');
            $total_rows = $this->db->get('course')->num_rows();
            $config = array();
            $config = pagintaion($total_rows, 6);
            $config['base_url']  = site_url('home/courses/');
            $this->pagination->initialize($config);
            $this->db->where('status', 'active');
            $page_data['courses'] = $this->db->get('course', $config['per_page'], $this->uri->segment(3))->result_array();
        }else {
            $courses = $this->crud_model->filter_course($selected_category_id, $selected_price, $selected_level, $selected_language, $selected_rating);
            $page_data['courses'] = $courses;
        }

        $page_data['page_name']  = "courses_page";
        $page_data['page_title'] = get_phrase('courses');
        $page_data['layout']     = $layout;
        $page_data['selected_category_id']     = $selected_category_id;
        $page_data['selected_price']     = $selected_price;
        $page_data['selected_level']     = $selected_level;
        $page_data['selected_language']     = $selected_language;
        $page_data['selected_rating']     = $selected_rating;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function set_layout_to_session() {
        $layout = $this->input->post('layout');
        $this->session->set_userdata('layout', $layout);
    }

    public function course($slug = "", $course_id = "") {
        $this->access_denied_courses($course_id);
        $page_data['course_id'] = $course_id;
        $page_data['page_name'] = "course_page";
        $page_data['page_title'] = get_phrase('course');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function instructor_page($instructor_id = "") {
        $page_data['page_name'] = "instructor_page";
        $page_data['page_title'] = get_phrase('instructor_page');
        $page_data['instructor_id'] = $instructor_id;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function all_courses() {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }

        $page_data['page_name'] = "all_courses";
        $page_data['page_title'] = get_phrase("all_courses");
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function enrol_course($course_id){
        $this->crud_model->enrol_student_web($course_id);
        redirect(site_url('home/my_courses/'), 'refresh');
    }

    public function my_courses() {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }

        $page_data['page_name'] = "my_courses";
        $page_data['page_title'] = get_phrase("my_courses");
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    
    //added count in view - Adeeb
     public function my_subjects($course_id="") {
        
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }
        $page_data['course_id'] = $course_id;
        $page_data['page_name'] = "my_subjects";
        $page_data['page_title'] = get_phrase("all_subjects");
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function exams() {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }
        $page_data['page_name'] = "exams_list";
        $page_data['page_title'] = get_phrase("exams_list");
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function my_messages($param1 = "", $param2 = "") {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }
        if ($param1 == 'read_message') {
            $page_data['message_thread_code'] = $param2;
        }
        elseif ($param1 == 'send_new') {
            $message_thread_code = $this->crud_model->send_new_private_message();
            $this->session->set_flashdata('flash_message', get_phrase('message_sent!'));
            redirect(site_url('home/my_messages/read_message/' . $message_thread_code), 'refresh');
        }
        elseif ($param1 == 'send_reply') {
            $this->crud_model->send_reply_message($param2); //$param2 = message_thread_code
            $this->session->set_flashdata('flash_message', get_phrase('message_sent!'));
            redirect(site_url('home/my_messages/read_message/' . $param2), 'refresh');
        }
        $page_data['page_name'] = "my_messages";
        $page_data['page_title'] = get_phrase('my_messages');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function my_notifications() {
        $page_data['page_name'] = "my_notifications";
        $page_data['page_title'] = get_phrase('my_notifications');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function my_wishlist() {
        if (!$this->session->userdata('cart_items')) {
            $this->session->set_userdata('cart_items', array());
        }
        $my_courses = $this->crud_model->get_courses_by_wishlists();
        $page_data['my_courses'] = $my_courses;
        $page_data['page_name'] = "my_wishlist";
        $page_data['page_title'] = get_phrase('my_wishlist');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function purchase_history() {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }

        $total_rows = $this->crud_model->purchase_history($this->session->userdata('user_id'))->num_rows();
        $config = array();
        $config = pagintaion($total_rows, 10);
        $config['base_url']  = site_url('home/purchase_history');
        $this->pagination->initialize($config);
        $page_data['per_page']   = $config['per_page'];

        if(addon_status('offline_payment') == 1):
            $this->load->model('addons/offline_payment_model');
            $page_data['pending_offline_payment_history'] = $this->offline_payment_model->pending_offline_payment($this->session->userdata('user_id'))->result_array();
        endif;

        $page_data['page_name']  = "purchase_history";
        $page_data['page_title'] = get_phrase('purchase_history');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function profile($param1 = "") {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }

        if ($param1 == 'user_profile') {
            $page_data['page_name'] = "user_profile";
            $page_data['page_title'] = get_phrase('user_profile');
        }elseif ($param1 == 'user_credentials') {
            $page_data['page_name'] = "user_credentials";
            $page_data['page_title'] = get_phrase('credentials');
        }elseif ($param1 == 'user_photo') {
            $page_data['page_name'] = "update_user_photo";
            $page_data['page_title'] = get_phrase('update_user_photo');
        }
        $page_data['user_details'] = $this->user_model->get_user($this->session->userdata('user_id'));
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function update_profile($param1 = "") {
        if ($param1 == 'update_basics') {
            $this->user_model->edit_user($this->session->userdata('user_id'));
        }elseif ($param1 == "update_credentials") {
            $this->user_model->update_account_settings($this->session->userdata('user_id'));
        }elseif ($param1 == "update_photo") {
            $this->user_model->upload_user_image($this->session->userdata('user_id'));
            $this->session->set_flashdata('flash_message', get_phrase('updated_successfully'));
        }
        redirect(site_url('home/profile/user_profile'), 'refresh');
    }

    public function handleWishList() {
        if ($this->session->userdata('user_login') != 1) {
            echo false;
        }else {
            if (isset($_POST['course_id'])) {
                $course_id = $this->input->post('course_id');
                $this->crud_model->handleWishList($course_id);
            }
            $this->load->view('frontend/'.get_frontend_settings('theme').'/wishlist_items');
        }
    }
    public function handleCartItems() {
        if (!$this->session->userdata('cart_items')) {
            $this->session->set_userdata('cart_items', array());
        }

        $course_id = $this->input->post('course_id');
        $previous_cart_items = $this->session->userdata('cart_items');
        if (in_array($course_id, $previous_cart_items)) {
            $key = array_search($course_id, $previous_cart_items);
            unset($previous_cart_items[$key]);
        }else {
            array_push($previous_cart_items, $course_id);
        }

        $this->session->set_userdata('cart_items', $previous_cart_items);
        $this->load->view('frontend/'.get_frontend_settings('theme').'/cart_items');
    }

    public function handleCartItemForBuyNowButton() {
        if (!$this->session->userdata('cart_items')) {
            $this->session->set_userdata('cart_items', array());
        }

        $course_id = $this->input->post('course_id');
        $previous_cart_items = $this->session->userdata('cart_items');
        if (!in_array($course_id, $previous_cart_items)) {
            array_push($previous_cart_items, $course_id);
        }
        $this->session->set_userdata('cart_items', $previous_cart_items);
        $this->load->view('frontend/'.get_frontend_settings('theme').'/cart_items');
    }

    public function refreshWishList() {
        $this->load->view('frontend/'.get_frontend_settings('theme').'/wishlist_items');
    }

    public function refreshShoppingCart() {
        $this->load->view('frontend/'.get_frontend_settings('theme').'/shopping_cart_inner_view');
    }

    public function isLoggedIn() {
        if ($this->session->userdata('user_login') == 1)
        echo true;
        else
        echo false;
    }

    //choose payment gateway
    public function payment($total_price_of_checking_out = ""){
        if ($this->session->userdata('user_login') != 1)
        redirect('login', 'refresh');

        $page_data['total_price_of_checking_out'] = $total_price_of_checking_out;
        $page_data['page_title'] = get_phrase("payment_gateway");
        $this->load->view('payment/index', $page_data);
    }

    // SHOW PAYPAL CHECKOUT PAGE
    public function paypal_checkout($payment_request = "only_for_mobile") {
        if ($this->session->userdata('user_login') != 1 && $payment_request != 'true')
        redirect('home', 'refresh');

        //checking price
        if($this->session->userdata('total_price_of_checking_out') == $this->input->post('total_price_of_checking_out')):
            $total_price_of_checking_out = $this->input->post('total_price_of_checking_out');
        else:
            $total_price_of_checking_out = $this->session->userdata('total_price_of_checking_out');
        endif;
        $page_data['payment_request'] = $payment_request;
        $page_data['user_details']    = $this->user_model->get_user($this->session->userdata('user_id'))->row_array();
        $page_data['amount_to_pay']   = $total_price_of_checking_out;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/paypal_checkout', $page_data);
    }

    // PAYPAL CHECKOUT ACTIONS
    public function paypal_payment($user_id = "", $amount_paid = "", $paymentID = "", $paymentToken = "", $payerID = "", $payment_request_mobile = "") {
        $paypal_keys = get_settings('paypal');
        $paypal = json_decode($paypal_keys);

        if ($paypal[0]->mode == 'sandbox') {
            $paypalClientID = $paypal[0]->sandbox_client_id;
            $paypalSecret   = $paypal[0]->sandbox_secret_key;
        }else{
            $paypalClientID = $paypal[0]->production_client_id;
            $paypalSecret   = $paypal[0]->production_secret_key;
        }

        //THIS IS HOW I CHECKED THE PAYPAL PAYMENT STATUS
        $status = $this->payment_model->paypal_payment($paymentID, $paymentToken, $payerID, $paypalClientID, $paypalSecret);
        if (!$status) {
            $this->session->set_flashdata('error_message', get_phrase('an_error_occurred_during_payment'));
            redirect('home', 'refresh');
        }
        $this->crud_model->enrol_student($user_id);
        $this->crud_model->course_purchase($user_id, 'paypal', $amount_paid);
        $this->email_model->course_purchase_notification($user_id, 'paypal', $amount_paid);
        $this->session->set_flashdata('flash_message', get_phrase('payment_successfully_done'));
        if($payment_request_mobile == 'true'):
            $course_id = $this->session->userdata('cart_items');
            redirect('home/payment_success_mobile/'.$course_id[0].'/'.$user_id.'/paid', 'refresh');
        else:
            $this->session->set_userdata('cart_items', array());
            redirect('home', 'refresh');
        endif;

    }

    // SHOW STRIPE CHECKOUT PAGE
    public function stripe_checkout($payment_request = "only_for_mobile") {
        if ($this->session->userdata('user_login') != 1 && $payment_request != 'true')
        redirect('home', 'refresh');

        //checking price
        if($this->session->userdata('total_price_of_checking_out') == $this->input->post('total_price_of_checking_out')):
            $total_price_of_checking_out = $this->input->post('total_price_of_checking_out');
        else:
            $total_price_of_checking_out = $this->session->userdata('total_price_of_checking_out');
        endif;
        $page_data['payment_request'] = $payment_request;
        $page_data['user_details']    = $this->user_model->get_user($this->session->userdata('user_id'))->row_array();
        $page_data['amount_to_pay']   = $total_price_of_checking_out;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/stripe_checkout', $page_data);
    }

    // STRIPE CHECKOUT ACTIONS
    public function stripe_payment($user_id = "", $amount_paid = "", $payment_request_mobile = "") {

        $token_id = $this->input->post('stripeToken');
        $stripe_keys = get_settings('stripe_keys');
        $values = json_decode($stripe_keys);
        if ($values[0]->testmode == 'on') {
            $public_key = $values[0]->public_key;
            $secret_key = $values[0]->secret_key;
        } else {
            $public_key = $values[0]->public_live_key;
            $secret_key = $values[0]->secret_live_key;
        }

        //THIS IS HOW I CHECKED THE STRIPE PAYMENT STATUS
        $status = $this->payment_model->stripe_payment($token_id, $user_id, $amount_paid, $secret_key);
        if (!$status) {
            $this->session->set_flashdata('error_message', get_phrase('an_error_occurred_during_payment'));
            redirect('home', 'refresh');
        }

        $this->crud_model->enrol_student($user_id);
        $this->crud_model->course_purchase($user_id, 'stripe', $amount_paid);
        $this->email_model->course_purchase_notification($user_id, 'stripe', $amount_paid);
        $this->session->set_flashdata('flash_message', get_phrase('payment_successfully_done'));
        if($payment_request_mobile == 'true'):
            $course_id = $this->session->userdata('cart_items');
            redirect('home/payment_success_mobile/'.$course_id[0].'/'.$user_id.'/paid', 'refresh');
        else:
            $this->session->set_userdata('cart_items', array());
            redirect('home', 'refresh');
        endif;
    }


    public function lesson($slug = "", $course_id = "", $section_id="",$lesson_id = "") {
        //  ini_set('display_errors', 1);
        if ($this->session->userdata('user_login') != 1){
            if ($this->session->userdata('admin_login') != 1){
                redirect('home', 'refresh');
            }
        }
        $course_details = $this->crud_model->get_course_by_id($course_id)->row_array();
        $sections = $this->crud_model->get_section('section', $section_id);
        if ($sections->num_rows() > 0) {
            $page_data['sections'] = $sections->result_array();
            if ($lesson_id == "") {
                $default_section = $sections->row_array();
                $page_data['section_id'] = $default_section['id'];
                $lessons = $this->crud_model->get_lessons('section', $default_section['id']);
                if ($lessons->num_rows() > 0) {
                    $default_lesson = $lessons->row_array();
                    $lesson_id = $default_lesson['id'];
                    $page_data['lesson_id']  = $default_lesson['id'];
                }else {
                    $page_data['page_name'] = 'empty';
                    $page_data['page_title'] = get_phrase('no_lesson_found');
                    $page_data['page_body'] = get_phrase('no_lesson_found');
                }
            }else {
                $page_data['lesson_id']  = $lesson_id;
                $section_id = $this->db->get_where('lesson', array('id' => $lesson_id))->row()->section_id;
                $page_data['section_id'] = $section_id;
            }

        }else {
            $page_data['sections'] = array();
            $page_data['page_name'] = 'empty';
            $page_data['page_title'] = get_phrase('no_section_found');
            $page_data['page_body'] = get_phrase('no_section_found');
        }

        // Check if the lesson contained course is purchased by the user
        if (isset($page_data['lesson_id']) && $page_data['lesson_id'] > 0) {
            $lesson_details = $this->crud_model->get_lessons('lesson', $page_data['lesson_id'])->row_array();
            $page_data['lesson_details']=$lesson_details;
            $lesson_id_wise_course_details = $this->crud_model->get_course_by_id($lesson_details['course_id'])->row_array();
            if ($this->session->userdata('role_id') != 1 && $lesson_id_wise_course_details['user_id'] != $this->session->userdata('user_id')) {
                if (!is_purchased($lesson_details['course_id'])) {
                    // redirect(site_url('home/course/'.slugify($course_details['title']).'/'.$course_details['id']), 'refresh');
                }
            }
        }else {
            if (!is_purchased($course_id)) {
                // redirect(site_url('home/course/'.slugify($course_details['title']).'/'.$course_details['id']), 'refresh');
            }
        }

        $page_data['course_id']  = $course_id;
        $page_data['page_name']  = 'lessons';
        $page_data['page_title'] = $course_details['title'];
        $this->load->view('lessons/index', $page_data);
    }
    
    public function lesson_files($slug = "", $course_id = "", $lesson_id = "",$lesson_file_id="") {
        $page_data['lesson_file_id']  = $lesson_file_id;
        if ($this->session->userdata('user_login') != 1){
            if ($this->session->userdata('admin_login') != 1){
                redirect('home', 'refresh');
            }
        }

        $course_details = $this->crud_model->get_course_by_id($course_id)->row_array();
        $sections = $this->crud_model->get_section('course', $course_id);
        if ($sections->num_rows() > 0) {
            $page_data['sections'] = $sections->result_array();
            if ($lesson_id == "") {
                $default_section = $sections->row_array();
                $page_data['section_id'] = $default_section['id'];
                $lessons = $this->crud_model->get_lessons('section', $default_section['id']);
                if ($lessons->num_rows() > 0) {
                    $default_lesson = $lessons->row_array();
                    $lesson_id = $default_lesson['id'];
                    $page_data['lesson_id']  = $default_lesson['id'];
                    
                }else {
                    $page_data['page_name'] = 'empty';
                    $page_data['page_title'] = get_phrase('no_lesson_found');
                    $page_data['page_body'] = get_phrase('no_lesson_found');
                }
            }
            
            else {
                $page_data['lesson_id']  = $lesson_id;
                $section_id = $this->db->get_where('lesson', array('id' => $lesson_id))->row()->section_id;
                $page_data['section_id'] = $section_id;
            }
            
        }else {
            $page_data['sections'] = array();
            $page_data['page_name'] = 'empty';
            $page_data['page_title'] = get_phrase('no_section_found');
            $page_data['page_body'] = get_phrase('no_section_found');
        }

        // Check if the lesson contained course is purchased by the user
        if (isset($page_data['lesson_id']) && $page_data['lesson_id'] > 0) {
            $lesson_details = $this->crud_model->get_lessons('lesson', $page_data['lesson_id'])->row_array();
            $page_data['lesson_file_details'] = $lesson_details;
            $lesson_id_wise_course_details = $this->crud_model->get_course_by_id($lesson_details['course_id'])->row_array();
            if ($this->session->userdata('role_id') != 1 && $lesson_id_wise_course_details['user_id'] != $this->session->userdata('user_id')) {
                if (!is_purchased($lesson_details['course_id'])) {
                    // redirect(site_url('home/course/'.slugify($course_details['title']).'/'.$course_details['id']), 'refresh');
                }
            }
        }else {
            if (!is_purchased($course_id)) {
                // redirect(site_url('home/course/'.slugify($course_details['title']).'/'.$course_details['id']), 'refresh');
            }
        }
        if ($lesson_file_id == "")
            {
                $lessons = $this->crud_model->get_lessons_files('lesson',$lesson_id);
                if($lessons){
                $lessons=$lessons->row_array();
                 $page_data['lesson_file_id']  = $lessons["id"];
                }
            }
        $page_data['course_id']  = $course_id;
        $page_data['page_name']  = 'lesson_files';
        $page_data['page_title'] = $course_details['title'];
        $this->load->view('lessons/index', $page_data);
    }
    

    public function my_courses_by_category() {
        $category_id = $this->input->post('category_id');
        $course_details = $this->crud_model->get_my_courses_by_category_id($category_id)->result_array();
        $page_data['my_courses'] = $course_details;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/reload_my_courses', $page_data);
    }

    public function search($search_string = "") {
        if (isset($_GET['query']) && !empty($_GET['query'])) {
            $search_string = $_GET['query'];
            $page_data['courses'] = $this->crud_model->get_courses_by_search_string($search_string)->result_array();
        }else {
            $this->session->set_flashdata('error_message', get_phrase('no_search_value_found'));
            redirect(site_url(), 'refresh');
        }

        if (!$this->session->userdata('layout')) {
            $this->session->set_userdata('layout', 'list');
        }
        $page_data['layout']     = $this->session->userdata('layout');
        $page_data['page_name'] = 'courses_page';
        $page_data['search_string'] = $search_string;
        $page_data['page_title'] = get_phrase('search_results');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    public function my_courses_by_search_string() {
        $search_string = $this->input->post('search_string');
        $course_details = $this->crud_model->get_my_courses_by_search_string($search_string)->result_array();
        $page_data['my_courses'] = $course_details;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/reload_my_courses', $page_data);
    }

    public function get_my_wishlists_by_search_string() {
        $search_string = $this->input->post('search_string');
        $course_details = $this->crud_model->get_courses_of_wishlists_by_search_string($search_string);
        $page_data['my_courses'] = $course_details;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/reload_my_wishlists', $page_data);
    }

    public function reload_my_wishlists() {
        $my_courses = $this->crud_model->get_courses_by_wishlists();
        $page_data['my_courses'] = $my_courses;
        $this->load->view('frontend/'.get_frontend_settings('theme').'/reload_my_wishlists', $page_data);
    }

    public function get_course_details() {
        $course_id = $this->input->post('course_id');
        $course_details = $this->crud_model->get_course_by_id($course_id)->row_array();
        echo $course_details['title'];
    }

    public function rate_course() {
        $data['review'] = $this->input->post('review');
        $data['ratable_id'] = $this->input->post('course_id');
        $data['ratable_type'] = 'course';
        $data['rating'] = $this->input->post('starRating');
        $data['date_added'] = strtotime(date('D, d-M-Y'));
        $data['user_id'] = $this->session->userdata('user_id');
        $this->crud_model->rate($data);
    }

    public function about_us() {
        $page_data['page_name'] = 'about_us';
        $page_data['page_title'] = get_phrase('about_us');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function terms_and_condition() {
        $page_data['page_name'] = 'terms_and_condition';
        $page_data['page_title'] = get_phrase('terms_and_condition');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function privacy_policy() {
        $page_data['page_name'] = 'privacy_policy';
        $page_data['page_title'] = get_phrase('privacy_policy');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    public function cookie_policy() {
        $page_data['page_name'] = 'cookie_policy';
        $page_data['page_title'] = get_phrase('cookie_policy');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }


    // Version 1.1
    public function dashboard($param1 = "") {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }

        if ($param1 == "") {
            $page_data['type'] = 'active';
        }else {
            $page_data['type'] = $param1;
        }

        $page_data['page_name']  = 'instructor_dashboard';
        $page_data['page_title'] = get_phrase('instructor_dashboard');
        $page_data['user_id']    = $this->session->userdata('user_id');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function create_course() {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }

        $page_data['page_name'] = 'create_course';
        $page_data['page_title'] = get_phrase('create_course');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function edit_course($param1 = "", $param2 = "") {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }

        if ($param2 == "") {
            $page_data['type']   = 'edit_course';
        }else {
            $page_data['type']   = $param2;
        }
        $page_data['page_name']  = 'manage_course_details';
        $page_data['course_id']  = $param1;
        $page_data['page_title'] = get_phrase('edit_course');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function course_action($param1 = "", $param2 = "") {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }

        if ($param1 == 'create') {
            if (isset($_POST['create_course'])) {
                $this->crud_model->add_course();
                redirect(site_url('home/create_course'), 'refresh');
            }else {
                $this->crud_model->add_course('save_to_draft');
                redirect(site_url('home/create_course'), 'refresh');
            }
        }elseif ($param1 == 'edit') {
            if (isset($_POST['publish'])) {
                $this->crud_model->update_course($param2, 'publish');
                redirect(site_url('home/dashboard'), 'refresh');
            }else {
                $this->crud_model->update_course($param2, 'save_to_draft');
                redirect(site_url('home/dashboard'), 'refresh');
            }
        }
    }


    public function sections($action = "", $course_id = "", $section_id = "") {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }

        if ($action == "add") {
            $this->crud_model->add_section($course_id);

        }elseif ($action == "edit") {
            $this->crud_model->edit_section($section_id);

        }elseif ($action == "delete") {
            $this->crud_model->delete_section($course_id, $section_id);
            $this->session->set_flashdata('flash_message', get_phrase('section_deleted'));
            redirect(site_url("home/edit_course/$course_id/manage_section"), 'refresh');

        }elseif ($action == "serialize_section") {
            $container = array();
            $serialization = json_decode($this->input->post('updatedSerialization'));
            foreach ($serialization as $key) {
                array_push($container, $key->id);
            }
            $json = json_encode($container);
            $this->crud_model->serialize_section($course_id, $json);
        }
        $page_data['course_id'] = $course_id;
        $page_data['course_details'] = $this->crud_model->get_course_by_id($course_id)->row_array();
        return $this->load->view('frontend/'.get_frontend_settings('theme').'/reload_section', $page_data);
    }

    public function manage_lessons($action = "", $course_id = "", $lesson_id = "") {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }
        if ($action == 'add') {
            $this->crud_model->add_lesson();
            $this->session->set_flashdata('flash_message', get_phrase('lesson_added'));
        }
        elseif ($action == 'edit') {
            $this->crud_model->edit_lesson($lesson_id);
            $this->session->set_flashdata('flash_message', get_phrase('lesson_updated'));
        }
        elseif ($action == 'delete') {
            $this->crud_model->delete_lesson($lesson_id);
            $this->session->set_flashdata('flash_message', get_phrase('lesson_deleted'));
        }
        redirect('home/edit_course/'.$course_id.'/manage_lesson');
    }

    public function lesson_editing_form($lesson_id = "", $course_id = "") {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }
        $page_data['type']      = 'manage_lesson';
        $page_data['course_id'] = $course_id;
        $page_data['lesson_id'] = $lesson_id;
        $page_data['page_name']  = 'lesson_edit';
        $page_data['page_title'] = get_phrase('update_lesson');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function download($filename = "") {
        $tmp           = explode('.', $filename);
        $fileExtension = strtolower(end($tmp));
        $yourFile = base_url().'uploads/lesson_files_new/'.$filename;
        $file = @fopen($yourFile, "rb");

        header('Content-Description: File Transfer');
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename='.$filename);
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($yourFile));
        while (!feof($file)) {
            print(@fread($file, 1024 * 8));
            ob_flush();
            flush();
        }
    }

    // Version 1.3 codes
    public function get_enrolled_to_free_course($course_id) {
        if ($this->session->userdata('user_login') == 1) {
            $this->crud_model->enrol_to_free_course($course_id, $this->session->userdata('user_id'));
            redirect(site_url('home/my_courses'), 'refresh');
        }else {
            redirect(site_url('login'), 'refresh');
        }
    }

    // Version 1.4 codes
    // public function login() {
    //     if ($this->session->userdata('admin_login')) {
    //         redirect(site_url('admin'), 'refresh');
    //     }elseif ($this->session->userdata('user_login')) {
    //         redirect(site_url('user'), 'refresh');
    //     }
    //     $page_data['page_name'] = 'login';
    //     $page_data['page_title'] = get_phrase('login');
    //     $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    // }
    public function login() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            if($this->session->userdata('role_id')!=2)
                redirect(site_url('user'), 'refresh');
            else redirect(site_url('home'), 'refresh');
        }
        $page_data['page_name'] = 'login_new';
        $page_data['page_title'] = get_phrase('login');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    public function signup() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            if($this->session->userdata('role_id')!=2)
                redirect(site_url('user'), 'refresh');
            else redirect(site_url('home'), 'refresh');
        }
        $page_data['page_name'] = 'signup_new';
        $page_data['page_title'] = get_phrase('signup');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    public function admin() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            redirect(site_url('user'), 'refresh');
        }
        $page_data['page_name'] = 'login_new';
        $page_data['page_title'] = get_phrase('login');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    public function otp() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            redirect(site_url('user'), 'refresh');
        }
        $page_data['page_name'] = 'otp';
        $page_data['page_title'] = get_phrase('login');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function sign_up() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            redirect(site_url('user'), 'refresh');
        }
        $page_data['page_name'] = 'sign_up';
        $page_data['page_title'] = get_phrase('sign_up');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function forgot_password() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            redirect(site_url('user'), 'refresh');
        }
        $page_data['page_name'] = 'forgot_password';
        $page_data['page_title'] = get_phrase('forgot_password');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function submit_quiz($from = "",$user_id = "") {
        $submitted_quiz_info = array();
        $container = array();
        $quiz_id = $this->input->post('lesson_id');

        //check attempt id
        $attempt_count = $this->crud_model->get_quiz_attempt_count($user_id, $quiz_id);

        $quiz_questions = $this->crud_model->get_quiz_questions($quiz_id)->result_array();
        $total_correct_answers = 0;
        foreach ($quiz_questions as $quiz_question) {
            $submitted_answer_status = 0;
            $correct_answers = json_decode($quiz_question['correct_answers']);
            $submitted_answers = array();
            foreach ($this->input->post($quiz_question['id']) as $each_submission) {
                if (isset($each_submission)) {
                    array_push($submitted_answers, $each_submission);
                }
            }
            sort($correct_answers);
            sort($submitted_answers);
            if ($correct_answers == $submitted_answers) {
                $submitted_answer_status = 1;
                $total_correct_answers++;
            }
            
            $uid=$user_id;
            if($user_id=="")
            $uid=$this->session->userdata('user_id');
            $container = array(
                "user_id" => $uid,
                "quiz_id" => $quiz_id,
                "question_id" => $quiz_question['id'],
                'submitted_answer_status' => $submitted_answer_status,
                "submitted_answers" => json_encode($submitted_answers),
                "correct_answers"  => json_encode($correct_answers),
                "attempt"  => $attempt_count + 1,
            );
            $this->db->insert('quiz_answer', $container);
          //  echo json_encode($this->db->error());
            array_push($submitted_quiz_info, $container);
        }
        $page_data['submitted_quiz_info']   = $submitted_quiz_info;
        $page_data['total_correct_answers'] = $total_correct_answers;
        $page_data['total_questions'] = count($quiz_questions);
        
        $this->quiz_result_mobile_web_view($quiz_id, $uid);
        // if ($from == 'mobile') {
        //     $this->load->view('mobile/quiz_submit', $page_data);
        // }else{
        //     $this->load->view('lessons/quiz_submit', $page_data);
        // }
    }

    private function access_denied_courses($course_id){
        $course_details = $this->crud_model->get_course_by_id($course_id)->row_array();
        if ($course_details['status'] == 'draft' && $course_details['user_id'] != $this->session->userdata('user_id')) {
            $this->session->set_flashdata('error_message', get_phrase('you_do_not_have_permission_to_access_this_course'));
            redirect(site_url('home'), 'refresh');
        }elseif ($course_details['status'] == 'pending') {
            if ($course_details['user_id'] != $this->session->userdata('user_id') && $this->session->userdata('role_id') != 1) {
                $this->session->set_flashdata('error_message', get_phrase('you_do_not_have_permission_to_access_this_course'));
                redirect(site_url('home'), 'refresh');
            }
        }
    }

    public function invoice($purchase_history_id = '') {
        if ($this->session->userdata('user_login') != 1){
            redirect('home', 'refresh');
        }
        $purchase_history = $this->crud_model->get_payment_details_by_id($purchase_history_id);
        if ($purchase_history['user_id'] != $this->session->userdata('user_id')) {
            redirect('home', 'refresh');
        }
        $page_data['payment_info'] = $purchase_history;
        $page_data['page_name'] = 'invoice';
        $page_data['page_title'] = 'invoice';
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    public function page_not_found() {
        $page_data['page_name'] = '404';
        $page_data['page_title'] = get_phrase('404_page_not_found');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }

    // AJAX CALL FUNCTION FOR CHECKING COURSE PROGRESS
    function check_course_progress($course_id) {
        echo course_progress($course_id);
    }
    /**
     * TESTS
     */
    public function test_mobile_web_view($test_id = "", $user_id="") {
        $data['test_details'] = $this->crud_model->get_test_single($test_id);
        $data['test_questions'] = $this->crud_model->get_test_question($test_id);
        $data['test_details']['questions_count'] = count($data['test_questions']);
        $data['test_details']['quiz_time'] = $this->hourMinute2Minutes($data['test_details']['duration']);
        $data['test_details']['quiz_marks'] = $data['test_details']['questions_count'] * 4;

        $data['user_id'] = $user_id;
        $data['test_id'] = $test_id;
        $data['page_name'] = 'test';
        $this->load->view('quiz_test/index', $data);
    }
    public function test_mobile_start_exam(){
        $post_data = $this->input->post();
        $attempt_count = $this->crud_model->get_test_attempt_count($post_data['test_id'], $post_data['user_id']);
        $data = [
            'user_id' => $post_data['user_id'],
            'test_id' => $post_data['test_id'],
            'start_time' => date('Y-m-d H:i:s'),
            'test_status' => 0,
            'time_taken ' => 0,
            'attempt_count' => $attempt_count + 1 ?? 1
        ];
        $attempt_id = $this->crud_model->save_test_attempt($data);
        echo json_encode(['status' => 1, 'message' => 'Success', 'attempt_id' => $attempt_id]);
    }
    public function test_mobile_save_attempt(){
        $post_data = $this->input->post();
        $data = [
            'user_id' => $post_data['user_id'],
            'test_id' => $post_data['test_id'],
            'end_time' => date('Y-m-d H:i:s'),
            'time_taken' => $post_data['time_taken'],
            'test_status' => $post_data['test_status'],
        ];

        $this->crud_model->update_test_attempt($data, $post_data['attempt_id']);

        $test_questions = $this->crud_model->get_test_question($post_data['test_id']);

        $post_data['user_answers'] = array_column($post_data['user_answers'], 'answer', 'question_id');
        foreach($test_questions as $test_question){
            $answer_data = [
                'user_id' => $post_data['user_id'],
                'test_id' => $post_data['test_id'],
                'question_id' => $test_question['id'],
                'attempt_id' => $post_data['attempt_id'],
                'correct_answers' => $test_question['correct_answers'],
            ];
            $correct_answers = json_decode($test_question['correct_answers'], true);

            if($correct_answers[0] == $post_data['user_answers'][$test_question['id']]){
                $answer_data['answer_status'] = 1;
                $answer_data['submitted_answer'] = $test_question['correct_answers'];
            }else{
                $answer_data['submitted_answer'] = json_encode([]);
                $options = json_decode($test_question['options'], true);
                foreach ($options as $key => $option){
                    $option_no = $key + 1;
                    if($option_no == $post_data['user_answers'][$test_question['id']]){
                        $answer_data['submitted_answer'] = json_encode(["$option_no"]);
                        $answer_data['answer_status'] = 0;
                    }
                }
            }
            $this->crud_model->save_test_answer($answer_data);
        }
        echo json_encode(['status' => 1, 'message' => 'Success']);
    }
    public function test_mobile_show_result($test_id = "", $user_id=""){

        $data['test_details'] = $this->crud_model->get_test_single($test_id);
        $data['test_questions'] = $this->crud_model->get_test_question($test_id);
        $data['test_details']['questions_count'] = count($data['test_questions']);
        $data['test_details']['quiz_time'] = $this->hourMinute2Minutes($data['test_details']['duration']);
        $data['test_details']['quiz_marks'] = $data['test_details']['questions_count'] * 4;

        $quiz_attempt = $this->crud_model->get_last_test_attempt($test_id, $user_id);
        $data['test_result']['user_answers'] = $this->crud_model->get_test_answers($test_id, $user_id, $quiz_attempt['id']);
        $data['test_result']['user_test_answers'] = array_column($data['test_result']['user_answers'], 'submitted_answer', 'question_id');
        $data['test_result']['user_test_answer_status'] = array_column($data['test_result']['user_answers'], 'answer_status', 'question_id');

        $correct_answer_array = [];
        $incorrect_answer_array = [];
        $unattempted_answer_array = [];
        foreach ($data['test_result']['user_answers'] as $answer){
            if($answer['answer_status'] == "1"){
                $correct_answer_array[] = $answer['question_id'];
            }elseif($answer['answer_status'] == "0"){
                $incorrect_answer_array[] = $answer['question_id'];
            }else{
                $unattempted_answer_array[] = $answer['question_id'];
            }
        }
        $data['test_result']['correct_count'] = count($correct_answer_array);
        $data['test_result']['incorrect_count'] = count($incorrect_answer_array);
        $data['test_result']['unattempted_count'] = count($unattempted_answer_array);
        $data['test_result']['attempted_count'] = $data['test_details']['questions_count'] - $data['test_result']['unattempted_count'];
        $data['test_result']['time_taken'] = gmdate("H:i", $quiz_attempt['time_taken']*60);
        $data['test_result']['score'] = $data['test_result']['correct_count'] * 4 - $data['test_result']['incorrect_count'];

        $data['user_id'] = $user_id;
        $data['test_id'] = $test_id;
        $data['page_name'] = 'test_result';
        $this->load->view('quiz_test/index', $data);
    }
    function hourMinute2Minutes($strHourMinute): int {
        $from = date('Y-m-d 00:00:00');
        $to = date('Y-m-d '.$strHourMinute);
        $diff = strtotime($to) - strtotime($from);
        $minutes = $diff / 60;
        return (int) $minutes;
    }

    /**
     * QUIZ PRACTICE
     */
    // public function quiz_practice_web_view($user_id = 0){
    //     $chapter_id         = $this->input->get('chapter_id');
    //     $data['user_id']    = $user_id;

    //     if($chapter_id == '0'){
    //         $data['sections']  = $this->crud_model->get_section_wise_chapters();
    //         $data['page_name'] = 'select_chapter';
    //     }else{
    //         if(is_numeric($chapter_id) && $chapter_id > 0){
    //             $chapter_id = [$chapter_id];
    //         }else{
    //             $chapter_id = json_decode($chapter_id, true);
    //         }
    //         $data['page_name'] = 'practice';
    //         $this->db->limit(20);
    //         $this->db->order_by('rand()');
    //         $this->db->where_in('lesson_id', $chapter_id);
    //         $data['questions'] = $this->db->get_where('question_bank', ['is_equation' => 1])->result_array();
    //     }
    //     $this->load->view('practice_ui/index', $data);
    // }
    
    
    
    // This is the function for rendering quiz web view for mobile
    public function quiz_mobile_web_view($quiz_id = 0, $user_id = 0) {
        $data['quiz']                      = $this->db->get_where('quiz', ['id' => $quiz_id])->row_array();
        $data['quiz']['already_attempted'] = 0;
        if($data['quiz']['is_practice']!=1){
            $attempt_count = $this->crud_model->get_quiz_attempt_count($user_id, $quiz_id);
            if($attempt_count > 0){
                $data['quiz']['already_attempted'] = 1;
            }
        }

        $data['quiz']['questions']         = $this->crud_model->get_quiz_question($quiz_id);
        $data['quiz']['questions_count']   = count($data['quiz']['questions']);
        $data['user_id']                   = $user_id;
        $data['quiz_id']                   = $quiz_id;
        $data['user']                      = $this->db->get_where('users', ['id' => $user_id])->row_array();
        $data['page_name']                 = 'quiz';
        $this->load->view('quiz_ui/index', $data);
    }
    public function ajax_quiz_mobile_save_attempt(){
        $post_data      = $this->input->post();
        $attempt_count  = $this->crud_model->get_quiz_attempt_count($post_data['quiz_id'], $post_data['user_id']);
        $data = [
            'user_id'       => $post_data['user_id'],
            'quiz_id'       => $post_data['quiz_id'],
            'datetime'      => date('Y-m-d H:i:s'),
            'attempt_count' => $attempt_count + 1 ?? 1
        ];
        $attempt_id                 = $this->crud_model->save_quiz_attempt($data);
        
        $quiz_questions             = $this->crud_model->get_quiz_question($post_data['quiz_id']);
        $post_data['user_answers']  = array_column($post_data['user_answers'], 'answer', 'question_id');

        foreach($quiz_questions as $quiz_question){
            $answer_data = [
                'user_id'           => $post_data['user_id'],
                'quiz_id'           => $post_data['quiz_id'],
                'question_id'       => $quiz_question['id'],
                'attempt_id'        => $attempt_id,
                'correct_answers'   => $quiz_question['correct_answers'],
            ];
            $correct_answers = json_decode($quiz_question['correct_answers'], true);

            if($correct_answers[0] == $post_data['user_answers'][$quiz_question['id']]){
                $answer_data['answer_status'] = 1;
                $answer_data['submitted_answer'] = $quiz_question['correct_answers'];
            }else{
                $answer_data['submitted_answer'] = json_encode([]);
                $options = json_decode($quiz_question['options'], true);
                foreach ($options as $key => $option){
                    $option_no = $key + 1;
                    if($option_no == $post_data['user_answers'][$quiz_question['id']]){
                        $answer_data['submitted_answer'] = json_encode(["$option_no"]);
                        $answer_data['answer_status'] = 0;
                    }
                }
            }
            $this->crud_model->save_quiz_answer($answer_data);
        }
        echo json_encode(['status' => 1, 'message' => 'Success', 'attempt_id' => $attempt_id]);

    }
    
    public function quiz_practice_web_view($user_id = 0, $course_id=0) {
        if($course_id>0){
            $this->db->where('course_id',$course_id);
        }
        $data['questions']         = $this->crud_model->get_question_bank();
        $data['questions_count']   = count($data['questions']);
        $data['user_id']           = $user_id;
        $data['course_id']         = $course_id;
        $data['user']              = $this->db->get_where('users', ['id' => $user_id])->row_array();
        $data['page_name']         = 'practice';
        $this->load->view('practice_ui/index', $data);
    }
    
    public function ajax_practice_save_attempt(){
        $post_data      = $this->input->post();
        $attempt_count  = $this->crud_model->get_practice_attempt_count($post_data['user_id']);
        $user_id        = $post_data['user_id'];

        $attempt_data = [
            'user_id'       => $user_id,
            'datetime'      => date('Y-m-d H:i:s'),
            'submit_status' => 0
        ];
        $this->db->insert('practice_attempt', $attempt_data);
        $attempt_id = $this->db->insert_id();


        // if($attempt_count > 0 && $user_id > 0){
            //get quiz attempt
            $attempt = $this->db->get_where('practice_attempt', [
                'user_id'   => $user_id,
                'id'        => $attempt_count + 1
            ])->row_array();
            

            //get user answers
            $user_answers = $post_data['user_answers'];

            //get questions
            $question_id_arr = json_decode($attempt['question_id'], true);
            $this->db->where_in('id', $question_id_arr);
            $this->db->where('course_id',$post_data['course_id']);
            $questions = $this->db->get('question_bank')->result_array();

            //order user answer
            $user_answers = array_column($user_answers, 'answer', 'question_id');

            //check answer
            $quiz_score['correct']    = 0;
            $quiz_score['incorrect']  = 0;
            $quiz_score['skip']       = 0;

            $question_answer = [];
            $answer['user_id']      = $this->input->post('user_id');
            $answer['attempt_id']   = $attempt['id'];
            $answer['datetime']     = date('Y-m-d H:i:s');
            foreach ($questions as $key => $question){
                $question_answer[$key]                      = $answer;
                $question_answer[$key]['question_id']       = $question['id'];
                $question_answer[$key]['answer_correct']    = $question['correct_answers'];

                $correct_answers = json_decode($question['correct_answers'], true);

                if(empty($user_answers[$question['id']])){
                    $quiz_score['skip'] += 1;
                    $question_answer[$key]['answer_status']     = 3;
                    $question_answer[$key]['answer_submitted']  = json_encode([]);

                }elseif($correct_answers[0] == $user_answers[$question['id']]){
                    $quiz_score['correct'] += 1;
                    $question_answer[$key]['answer_status']     = 1;
                    $question_answer[$key]['answer_submitted']  = $question['correct_answers'];

                }else{
                    $options = json_decode($question['options'], true);
                    foreach ($options as $option_key => $option){
                        $option_no = $option_key + 1;
                        if($option_no == $user_answers[$question['id']]){
                            $question_answer[$key]['answer_submitted']  = json_encode(["$option_no"]);
                            $question_answer[$key]['answer_status']     = 2;
                        }
                    }
                    $quiz_score['incorrect'] += 1;
                }
            }

            $this->db->insert_batch('practice_answer', $question_answer);

            //calculate score
            $quiz_score['score'] = $quiz_score['correct'] * 4 - $quiz_score['incorrect'];
            
            //update attempt
            $this->db->where('id', $attempt['id']);
            $this->db->update('practice_attempt', [
                'course_id'     => $post_data['course_id'],
                'question_no'   => count($questions),
                'question_id'   => json_encode(array_column($questions, 'id')),
                'end_time'      => date('Y-m-d H:i:s'),
                'time_taken'    => round($this->input->post('time_taken'), 2),
                'correct'       => $quiz_score['correct'],
                'incorrect'     => $quiz_score['incorrect'],
                'skip'          => $quiz_score['skip'],
                'score'         => round($quiz_score['score'], 2),
                'submit_status' => 1,
            ]);

            echo json_encode(['status' => 1, 'message' => 'Success', 'attempt_id' => $attempt_id]);
        // }

    }
    
    public function practice_show_result($user_id){
        $attempt_id = $this->db->get_where('practice_attempt', ['user_id'   => $user_id])->num_rows();
        $data['attempt'] = $this->db->get_where('practice_attempt', ['id' => $attempt_id, 'user_id' => $user_id])->row_array();
        $question_ids = json_decode($data['attempt']['question_id'], true);
        
        //get user answer
        $this->db->select([
           'question_bank.title', 'question_bank.title_file', 'question_bank.solution', 'question_bank.solution_file', 'question_bank.is_equation',
            'question_bank.is_equation_solution', 'question_bank.options', 'question_bank.correct_answers',
            'practice_answer.answer_correct', 'practice_answer.answer_submitted', 'practice_answer.question_id', 'practice_answer.answer_status',
        ]);
        $this->db->from('practice_answer');
        $this->db->join('question_bank', 'practice_answer.question_id = question_bank.id');
        $this->db->where_in('practice_answer.question_id', $question_ids);
        $this->db->where('practice_answer.user_id', $user_id);
        $this->db->where('practice_answer.attempt_id', $attempt_id);
        $this->db->order_by('FIELD(question_bank.id, '.implode (", ", $question_ids).')');

        $data['user_answers']   = $this->db->get()->result_array();
        $data['page_name']      = 'quiz_result';
        $data['user_id']        = $user_id;
        $data['attempt_id']     = $attempt_id;

        $this->load->view('practice_ui/index', $data);
    }
    

    public function ajax_change_sound_status(){
        $user_id        = $this->input->post('user_id');
        $sound_status   = $this->input->post('sound_status') == 1 ? 0 : 1;
        $this->db->where('id', $user_id);
        $this->db->update('users', ['sound_status'=> $sound_status]);
        echo json_encode(['sound_status'=> $sound_status]);
    }

    public function quiz_mobile_show_result($quiz_id = "", $user_id=""){

        $data['quiz_details']   = $this->crud_model->get_lessons('lesson', $quiz_id)->row_array();
        $data['quiz_questions'] = $this->crud_model->get_quiz_question($quiz_id);
        $data['quiz_details']['questions_count'] = count($data['test_questions']);
        $data['quiz_details']['quiz_marks'] = $data['quiz_details']['questions_count'];

        $quiz_attempt = $this->crud_model->get_last_quiz_attempt($user_id);
        $data['quiz_result']['user_answers']            = $this->crud_model->get_quiz_answers($quiz_id, $user_id, $quiz_attempt['id']);

        
        $data['quiz_result']['user_quiz_answers']       = array_column($data['quiz_result']['user_answers'], 'submitted_answer', 'question_id');
        $data['quiz_result']['user_quiz_answer_status'] = array_column($data['quiz_result']['user_answers'], 'answer_status', 'question_id');

        $correct_answer_array = [];
        $incorrect_answer_array = [];
        $unattempted_answer_array = [];
        foreach ($data['quiz_result']['user_answers'] as $answer){
            if($answer['answer_status'] == "1"){
                $correct_answer_array[] = $answer['question_id'];
            }elseif($answer['answer_status'] == "0"){
                $incorrect_answer_array[] = $answer['question_id'];
            }else{
                $unattempted_answer_array[] = $answer['question_id'];
            }
        }
        $data['quiz_result']['correct_count'] = count($correct_answer_array);
        $data['quiz_result']['incorrect_count'] = count($incorrect_answer_array);
        $data['quiz_result']['unattempted_count'] = count($unattempted_answer_array);
        $data['quiz_result']['attempted_count'] = $data['quiz_details']['questions_count'] - $data['quiz_result']['unattempted_count'];
        $data['quiz_result']['score'] = $data['quiz_result']['correct_count'] * 4 - $data['quiz_result']['incorrect_count'];
        $data['user_id'] = $user_id;
        $data['quiz_id'] = $quiz_id;
        $data['page_name'] = 'quiz_result';
        $this->load->view('quiz_ui/index', $data);
    }
    
    public function quiz_result_mobile_web_view($lesson_id = "", $user_id="") {
        $data['lesson_details'] = $this->crud_model->get_lessons('lesson', $lesson_id)->row_array();
        $data['user_id'] = $user_id;
        $data['quiz_id'] = $lesson_id;
        
        $data['questions'] = $this->crud_model->get_quiz_questions($lesson_id)->result_array();
        $user_answers = $this->crud_model->get_user_quiz_answer($lesson_id, $user_id);
        
        {
            $correct_arr = [];
            $incorrect_arr = [];
            $question_arr = [];
            $correct = 0;
            $incorrect = 0;
            $user_ans = [];
            foreach($user_answers as $ans){
                if(!in_array($ans['question_id'], $question_arr)){
                    $question_arr[] = $ans['question_id'];
                    if($ans["submitted_answer_status"]==1) {
                        $correct_arr[] = $ans['question_id'];
                    }elseif($ans['submitted_answers'] != '[]'){
                        if($ans["submitted_answer_status"]==0) {
                            $incorrect_arr[] = $ans['question_id'];
                        }
                    }
                    $submitted_ans = json_decode($ans['submitted_answers'], true);
                    $user_ans[$ans['question_id']] = $submitted_ans[0];
                    $user_ans_status[$ans['question_id']] = $ans["submitted_answer_status"];
                    
                }
            }
            $correct = count($correct_arr);
            $incorrect = count($incorrect_arr);
        }
        $data['user_ans_status']            = $user_ans_status;
        $data['user_answer']                = $user_ans;
        $data['quiz_score']['correct']      = $correct;
        $data['quiz_score']['incorrect']    = $incorrect;
        $data['quiz_score']['total_marks']  = count($data['questions'])*4;
        $data['quiz_score']['your_marks']   = ($correct * 4) - $incorrect;
        
        $data['page_name'] = 'quiz_solutions';
        $this->load->view('mobile/index', $data);
    }

    //FOR MOBILE
    public function course_purchase($auth_token = '', $course_id  = ''){
        $this->load->model('jwt_model');
        if(empty($auth_token) || $auth_token == "null"){
            $page_data['cart_item'] = $course_id;
            $page_data['user_id'] = '';
            $page_data['is_login_now'] = 0;
            $page_data['enroll_type'] = null;
            $page_data['page_name'] = 'shopping_cart';
            $this->load->view('mobile/index', $page_data);
        }else{

            $logged_in_user_details = json_decode($this->jwt_model->token_data_get($auth_token), true);

            if ($logged_in_user_details['user_id'] > 0) {

                $credential = array('id' => $logged_in_user_details['user_id'], 'status' => 1, 'role_id' => 2);
                $query = $this->db->get_where('users', $credential);
                if ($query->num_rows() > 0) {
                    $row = $query->row();
                    $page_data['cart_item'] = $course_id;
                    $page_data['user_id'] = $row->id;
                    $page_data['is_login_now'] = 1;
                    $page_data['enroll_type'] = null;
                    $page_data['page_name'] = 'shopping_cart';

                    $cart_item = array($course_id);
                    $this->session->set_userdata('cart_items', $cart_item);
                    $this->session->set_userdata('user_login', '1');
                    $this->session->set_userdata('user_id', $row->id);
                    $this->session->set_userdata('role_id', $row->role_id);
                    $this->session->set_userdata('role', get_user_role('user_role', $row->id));
                    $this->session->set_userdata('name', $row->first_name.' '.$row->last_name);
                    $this->load->view('mobile/index', $page_data);
                }
            }

        }
    }

    //FOR MOBILE
    public function get_enrolled_to_free_course_mobile($course_id ="", $user_id ="", $get_request = "") {
        if ($get_request == "true") {
            $this->crud_model->enrol_to_free_course_mobile($course_id, $user_id);
        }
    }

    //FOR MOBILE
    public function payment_success_mobile($course_id = "", $user_id = "", $enroll_type = ""){
        if($course_id > 0 && $user_id > 0):
            $page_data['cart_item'] = $course_id;
            $page_data['user_id'] = $user_id;
            $page_data['is_login_now'] = 1;
            $page_data['enroll_type'] = $enroll_type;
            $page_data['page_name'] = 'shopping_cart';

            $this->session->unset_userdata('user_id');
            $this->session->unset_userdata('role_id');
            $this->session->unset_userdata('role');
            $this->session->unset_userdata('name');
            $this->session->unset_userdata('user_login');
            $this->session->unset_userdata('cart_items');

            $this->load->view('mobile/index', $page_data);
        endif;
    }
    
     public function live_class() {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }

        $page_data['page_name'] = "live_class";
        $page_data['page_title'] = "Live class";
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    
     public function assignments() {
        if (!$this->session->userdata('cart_items')) {
            $this->session->set_userdata('cart_items', array());
        }
        $my_courses = $this->crud_model->get_courses_by_wishlists();
       
        $page_data['my_courses'] = $my_courses;
        $page_data['user_id'] =  $this->session->userdata('user_id');
        $page_data['page_name'] = "assignments";
        $page_data['page_title'] = get_phrase('my_wishlist');
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    
    
      public function assignments_upload() {
    $response = array();
    $lesson_id=$_POST["assignment_id"];
    
        for($i = 0; $i < count($_FILES['file']['tmp_name']); $i++){ 
            $rand=rand(10,10000);
            move_uploaded_file($_FILES['file']['tmp_name'][$i], 'uploads/user_file/'.$this->session->userdata('user_id')."-".$lesson_id."-".$i.$rand.'.jpg');
        
        
        
            $data["file"]=$this->session->userdata('user_id')."-".$lesson_id."-".$i.$rand.'.jpg';      
             $data["user_id"]=$this->session->userdata('user_id');
              $data["assignment_id"]=$lesson_id;
             $this->db->insert('user_file', $data);
             $response['status'] = 'success';
             
        }
        
        redirect(site_url('home/assignments'), 'refresh');
        
      
    
    return $this->set_response($response, REST_Controller::HTTP_OK);
  }
  
    public function pdf_category() {
        if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }

        $page_data['page_name'] = "pdf_category";
        $page_data['page_title'] = get_phrase("pdf_category");
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    
    public function pdf_meterial_by_category($id)
    {
         if ($this->session->userdata('user_login') != true) {
            redirect(site_url('home'), 'refresh');
        }
        
        $page_data['page_name'] = "pdf_materials";
        $page_data['page_title'] = get_phrase("pdf_materials");
        $page_data['pdf_material'] = $this->crud_model->get_pdf_material_by_category($id);
        $this->load->view('frontend/'.get_frontend_settings('theme').'/index', $page_data);
    }
    
    public function app_features() {
        $this->load->view('frontend/default/triz/app-features');
    }
    public function careers() {
        $this->load->view('frontend/default/triz/careers');
    }
    public function contact() {
        $this->load->view('frontend/default/triz/contact');
    }
    public function course_listing() {
        $this->load->view('frontend/default/triz/course-listing');
    }
    public function demo_class() {
        $this->load->view('frontend/default/triz/demo-class');
    }
    public function insight() {
        $this->load->view('frontend/default/triz/insight');
    }
    public function mock_test() {
        $this->load->view('frontend/default/triz/mock-test');
    }
    public function quistion_papper() {
        $this->load->view('frontend/default/triz/quistion-papper');
    }
    public function syllabus() {
        $this->load->view('frontend/default/triz/syllabus');
    }
    public function team() {
        $this->load->view('frontend/default/triz/team');
    }
}
