<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        // Your own constructor code
        $this->load->database();
        $this->load->library('session');
        /*cache control*/
        $this->output->set_header('Cache-Control: no-store, no-cache, must-revalidate, post-check=0, pre-check=0');
        $this->output->set_header('Pragma: no-cache');
    }

    public function index() {
        if ($this->session->userdata('admin_login')) {
            redirect(site_url('admin'), 'refresh');
        }elseif ($this->session->userdata('user_login')) {
            redirect(site_url('user'), 'refresh');
        }else {
            redirect(site_url('home/login'), 'refresh');
        }
    }
    public function otp()
    {
            $otp = $this->input->post('otp');
            $credential = array('id' =>$this->session->userdata('user_id'), 'verification_code' => $otp);
    		$query = $this->db->get_where('users', $credential);
    		if ($query->num_rows() > 0) {
    		    $data['status'] = 1;
        		$this->db->where('id', $this->session->userdata('user_id'));
        		$this->db->update('users', $data);
        		$this->session->set_userdata('user_login', '1');
    		    $this->session->set_flashdata('flash_message', "Success");
    		  //  echo json_encode($_SESSION);
    		    redirect(site_url('home'), 'refresh');
    		}
    		else
    		{
    		     $this->session->set_flashdata('error_message', "Incorrrect OTP");
    		     redirect(site_url('home/otp'), 'refresh');
    		   
    		}
    }
//     public function validate_login($from = "") {
// //         ini_set('display_errors', '1');
// // ini_set('display_startup_errors', '1');
// // error_reporting(E_ALL);
//         $email = $this->input->post('email');
//         $password = $this->input->post('password');
//         $credential = array('email' => $email, 'password' => sha1($password));

//         // Checking login credential for admin
//         $query = $this->db->get_where('users', $credential);

//         if ($query->num_rows() > 0) {
//             $row = $query->row();
//             $this->session->set_userdata('user_id', $row->id);
//             $this->session->set_userdata('role_id', $row->role_id);
//             $this->session->set_userdata('role', get_user_role('user_role', $row->id));
//             $this->session->set_userdata('name', $row->first_name.' '.$row->last_name);
//             $this->session->set_flashdata('flash_message', get_phrase('welcome').' '.$row->first_name.' '.$row->last_name);
//             if ($row->role_id == 1) {
//                 $this->session->set_userdata('admin_login', '1');
//                 redirect(site_url('admin/dashboard'), 'refresh');
//             }else if($row->role_id == 2){
//                 if($row->status==0)
//                 {
//                     $digits = 4;
//                     $OTP= rand(pow(10, $digits-1), pow(10, $digits)-1);
//                     $this->sms_api($email,"Your OTP is ".$OTP);
//                 	$data['verification_code'] = $OTP;
//             		$this->db->where('id', $row->id);
//             		$this->db->update('users', $data);
//                     $this->session->set_flashdata('flash_message', "OTP Send to your phone ");
//                      redirect(site_url('home/otp'), 'refresh');
//                 }
//                 else
//                 {
//                      $this->session->set_userdata('user_login', '1');
//                      redirect(site_url('home'), 'refresh');
//                 }
               
//             }
//         }else {
//             $this->session->set_flashdata('error_message',get_phrase('invalid_login_credentials'));
//             redirect(site_url('home/login'), 'refresh');
//         }
//     }

public function validate_login1($from = "") {
//         ini_set('display_errors', '1');
// ini_set('display_startup_errors', '1');
// error_reporting(E_ALL);
        $email = $this->input->post('email');
        // $email = str_replace("+", "", $email);
        $password = $this->input->post('password');
        $credential = array('email' => $email, 'password' => sha1($password));
        $login_type=$this->input->post('login_type');
        if(isset($login_type)){
        // echo json_encode($_POST);
        if($login_type==1)
            $credential = array('email' => $email, 'password' => sha1($password));
        else{
            $email =  $this->input->post('phone');
            $email=str_replace("+","",$email);
            $credential = array('email' => $email);
        } 
        // Checking login credential for admin
        $query = $this->db->get_where('users', $credential);

        if ($query->num_rows() > 0) {
            $row = $query->row();
            $this->session->set_userdata('user_id', $row->id);
            $this->session->set_userdata('role_id', $row->role_id);
            $this->session->set_userdata('role', get_user_role('user_role', $row->id));
            $this->session->set_userdata('name', $row->first_name.' '.$row->last_name);
            
            if ($row->role_id == 1 || $row->role_id == 5 || $row->role_id == 6 || $row->role_id == 7 || $row->role_id == 8 ) {
                //ADMIN LOGIN
                $this->session->set_flashdata('flash_message', get_phrase('welcome').' '.$row->first_name.' '.$row->last_name);
                $this->session->set_userdata('admin_login', '1');
                redirect(site_url('admin/dashboard'), 'refresh');
            }else if($row->role_id == 2){
                 //STUDENT LOGIN
                $this->session->set_flashdata('flash_message', get_phrase('welcome').' '.$row->first_name.' '.$row->last_name);
                // if($row->status==0)
                // {
                $digits = 4;
                $OTP= rand(pow(10, $digits-1), pow(10, $digits)-1);
                $this->sms_api('+'.$email,$OTP);
            	$data['verification_code'] = $OTP;
        		$this->db->where('id', $row->id);
        		$this->db->update('users', $data);
                $this->session->set_flashdata('flash_message', "OTP Send to your phone ");
                 redirect(site_url('home/otp'), 'refresh');
                // }
                // else
                // {
                //      $this->session->set_userdata('user_login', '1');
                //      redirect(site_url('home'), 'refresh');
                // }
               
            }
            else{
                 $this->session->set_flashdata('error_message',get_phrase('invalid_login_credentials'));
                 redirect(site_url('home/login'), 'refresh');
            }
        }else {
            $this->session->set_flashdata('error_message',get_phrase('phone_number_not_registered!'));
            redirect(site_url('home/signup'), 'refresh');
        }
        }
        else {
            $this->session->set_flashdata('error_message',get_phrase('invalid_login_credentials'));
            redirect(site_url('home/login'), 'refresh');
        }
    }

    public function signup_new(){
        $email =  $this->input->post('phone');
        $name =  $this->input->post('name');
        $email = str_replace("+","",$email);
        $credential = array('email' => $email);
        // Checking login credential for admin
        $query = $this->db->get_where('users', $credential);

        if ($query->num_rows() == 0) {
            $user_id = $this->user_model->register_user_web($email, $name);
                if(!empty($user_id)) {
                    $query = $this->db->get_where('users', ['id' => $user_id]);

                    if ($query->num_rows() > 0) {
                        $row = $query->row();
                        $this->session->set_userdata('user_id', $row->id);
                        $this->session->set_userdata('role_id', $row->role_id);
                        $this->session->set_userdata('role', get_user_role('user_role', $row->id));
                        $this->session->set_userdata('name', $row->first_name . ' ' . $row->last_name);

                        $digits = 4;
                        $OTP= rand(pow(10, $digits-1), pow(10, $digits)-1);
                        $this->sms_api('+'.$email,$OTP);
                        $data['verification_code'] = $OTP;
                        $this->db->where('id', $user_id);
                        $this->db->update('users', $data);
                        $this->session->set_flashdata('flash_message', "OTP Send to your phone ");
                        redirect(site_url('home/otp'), 'refresh');
                    }
                    }else{
                        $this->session->set_flashdata('error_message',get_phrase('something_went_wrong_try_again'));
                        redirect(site_url('home/login'), 'refresh');
                    }

        }else {
            $this->session->set_flashdata('error_message',get_phrase('phone_number_already_exists'));
            redirect(site_url('home/login'), 'refresh');
        }
    }
    
    
    
    public function validate_login($from = "") {
//         ini_set('display_errors', '1');
// ini_set('display_startup_errors', '1');
// error_reporting(E_ALL);
        $email = $this->input->post('email');
        
        $password = $this->input->post('password');
        $credential = array('email' => $email, 'password' => sha1($password));

        // Checking login credential for admin
        $query = $this->db->get_where('users', $credential);

        if ($query->num_rows() > 0) {
            $row = $query->row();
            $this->session->set_userdata('user_id', $row->id);
            $this->session->set_userdata('role_id', $row->role_id);
            $this->session->set_userdata('role', get_user_role('user_role', $row->id));
            $this->session->set_userdata('name', $row->first_name.' '.$row->last_name);
            
            if ($row->role_id == 1) {
                //ADMIN LOGIN
                $this->session->set_flashdata('flash_message', get_phrase('welcome').' '.$row->first_name.' '.$row->last_name);
                $this->session->set_userdata('admin_login', '1');
                redirect(site_url('admin/dashboard'), 'refresh');
            }else if($row->role_id == 2){
                 //STUDENT LOGIN
                $this->session->set_flashdata('flash_message', get_phrase('welcome').' '.$row->first_name.' '.$row->last_name);
                // if($row->status==0)
                // {
                    $digits = 4;
                    $OTP= rand(pow(10, $digits-1), pow(10, $digits)-1);
                    // $this->sms_api($email,$OTP);
                	$data['verification_code'] = $OTP;
            		$this->db->where('id', $row->id);
            		$this->db->update('users', $data);
                    $this->session->set_flashdata('flash_message', "OTP Send to your phone ");
                     redirect(site_url('home/otp'), 'refresh');
                // }
                // else
                // {
                //      $this->session->set_userdata('user_login', '1');
                //      redirect(site_url('home'), 'refresh');
                // }
               
            }
            else{
                 $this->session->set_flashdata('error_message',get_phrase('invalid_login_credentials'));
                 redirect(site_url('home/login'), 'refresh');
            }
        }else {
            $this->session->set_flashdata('error_message',get_phrase('invalid_login_credentials'));
            redirect(site_url('home/login'), 'refresh');
        }
    }

    public function register() {
        $data['first_name'] = html_escape($this->input->post('first_name'));
        $data['last_name']  = html_escape($this->input->post('last_name'));
        $data['email']  = html_escape($this->input->post('email'));
        $data['password']  = sha1($this->input->post('password'));

        $verification_code =  md5(rand(100000000, 200000000));
        $data['verification_code'] = $verification_code;

        if (get_settings('student_email_verification') == 'enable') {
            $data['status'] = 0;
        }else {
            $data['status'] = 1;
        }

        $data['wishlist'] = json_encode(array());
        $data['watch_history'] = json_encode(array());
        $data['date_added'] = strtotime(date("Y-m-d H:i:s"));
        $social_links = array(
            'facebook' => "",
            'twitter'  => "",
            'linkedin' => ""
        );
        $data['social_links'] = json_encode($social_links);
        $data['role_id']  = 2;

        // Add paypal keys
        $paypal_info = array();
        $paypal['production_client_id'] = "";
        array_push($paypal_info, $paypal);
        $data['paypal_keys'] = json_encode($paypal_info);
        // Add Stripe keys
        $stripe_info = array();
        $stripe_keys = array(
            'public_live_key' => "",
            'secret_live_key' => ""
        );
        array_push($stripe_info, $stripe_keys);
        $data['stripe_keys'] = json_encode($stripe_info);

        $validity = $this->user_model->check_duplication('on_create', $data['email']);
        if ($validity) {
            $user_id = $this->user_model->register_user($data);
            /*$this->session->set_userdata('user_login', '1');
            $this->session->set_userdata('user_id', $user_id);
            $this->session->set_userdata('role_id', 2);
            $this->session->set_userdata('role', get_user_role('user_role', 2));
            $this->session->set_userdata('name', $data['first_name'].' '.$data['last_name']);*/

            if (get_settings('student_email_verification') == 'enable') {
                $this->email_model->send_email_verification_mail($data['email'], $verification_code);
                $this->session->set_flashdata('flash_message', get_phrase('your_registration_has_been_successfully_done').'. '.get_phrase('please_check_your_mail_inbox_to_verify_your_email_address').'.');
            }else {
                $this->session->set_flashdata('flash_message', get_phrase('your_registration_has_been_successfully_done'));
            }

        }else {
            $this->session->set_flashdata('error_message', get_phrase('email_duplication'));
        }
        redirect(site_url('home'), 'refresh');
    }

    public function logout($from = "") {
        //destroy sessions of specific userdata. We've done this for not removing the cart session
        $this->session_destroy();
        redirect(site_url('home/login'), 'refresh');
    }

    public function session_destroy() {
        $this->session->unset_userdata('user_id');
        $this->session->unset_userdata('role_id');
        $this->session->unset_userdata('role');
        $this->session->unset_userdata('name');
        if ($this->session->userdata('admin_login') == 1) {
            $this->session->unset_userdata('admin_login');
        }else {
            $this->session->unset_userdata('user_login');
        }
    }

    function forgot_password($from = "") {
        $email = $this->input->post('email');
        //resetting user password here
        $new_password = substr( md5( rand(100000000,20000000000) ) , 0,7);

        // Checking credential for admin
        $query = $this->db->get_where('users' , array('email' => $email));
        if ($query->num_rows() > 0)
        {
            $this->db->where('email' , $email);
            $this->db->update('users' , array('password' => sha1($new_password)));
            // send new password to user email
            $this->email_model->password_reset_email($new_password, $email);
            $this->session->set_flashdata('flash_message', get_phrase('please_check_your_email_for_new_password'));
            if ($from == 'backend') {
                redirect(site_url('login'), 'refresh');
            }else {
                redirect(site_url('home'), 'refresh');
            }
        }else {
            $this->session->set_flashdata('error_message', get_phrase('password_reset_failed'));
            if ($from == 'backend') {
                redirect(site_url('login'), 'refresh');
            }else {
                redirect(site_url('home'), 'refresh');
            }
        }
    }

    public function verify_email_address($verification_code = "") {
        $user_details = $this->db->get_where('users', array('verification_code' => $verification_code));
        if($user_details->num_rows() == 0) {
            $this->session->set_flashdata('error_message', get_phrase('email_duplication'));
        }else {
            $user_details = $user_details->row_array();
            $updater = array(
                'status' => 1
            );
            $this->db->where('id', $user_details['id']);
            $this->db->update('users', $updater);
            $this->session->set_flashdata('flash_message', get_phrase('congratulations').'!'.get_phrase('your_email_address_has_been_successfully_verified').'.');
        }
        redirect(site_url('home'), 'refresh');
    }
    
//     	public function sms_api($phno,$message)
// 	{
	    	 
            	
            
//             	$message=urlencode ( $message );
//             $fields = array(
//                 'username' => 'prism',
//                 'password' => 'stallion123',
//                 'sendername' => 'CENTUM',
//                  'mobileno'=>$phno,
//                  'message'=>$message
//                 );
//             $url = "sms.sangamamonline.in/httpapi/smsapi?uname=prism&password=stallion123&sender=CENTUM&receiver=$phno&route=TA&msgtype=1&sms=$message";
//             //open connection
//             $ch = curl_init();
            
//             //set options 
//             curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
//             curl_setopt($ch, CURLOPT_URL, $url);
//             curl_setopt($ch, CURLOPT_POSTFIELDS, $fields); 
//             curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false
            
//             //execute post
//             $result = curl_exec($ch);
            
//             //close connection
//             curl_close($ch);
            
//             //write to file
//             $fp = fopen('uploads/result.pdf', 'w');  //make sure the directory markdown.md is in and the result.pdf will go to has proper permissions
//             fwrite($fp, $result);
//             fclose($fp);
//             //echo "sms sent successfully";
//           // echo $message;
//           //echo $phno;
// 	}

public function sms_api($phno,$message)
	{
	    	 
            	
            
            	$message=urlencode ( $message );
            $fields = array(
                'username' => 'prism',
                'password' => 'stallion123',
                'sendername' => 'PADPST',
                 'mobileno'=>$phno,
                 'message'=>$message
                );
            $url = "https://2factor.in/API/V1/5f32c941-ad59-11ea-9fa5-0200cd936042/SMS/$phno/$message/2factor";
            //open connection
            $ch = curl_init();
            
            //set options 
            curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-type: multipart/form-data"));
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $fields); 
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); //needed so that the $result=curl_exec() output is the file and isn't just true/false
            
            //execute post
            $result = curl_exec($ch);
            
            //close connection
            curl_close($ch);
            
            //write to file
            $fp = fopen('uploads/result.pdf', 'w');  //make sure the directory markdown.md is in and the result.pdf will go to has proper permissions
            fwrite($fp, $result);
            fclose($fp);
            //echo "sms sent successfully";
           // echo $message;
           return $result;
	}
}
